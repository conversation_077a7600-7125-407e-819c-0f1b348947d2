<?php

declare(strict_types=1);

namespace app\tests\unit\components;

use app\back\components\CliArgument;
use app\back\components\CliArguments;
use app\back\components\CliOption;
use PHPUnit\Framework\TestCase;

class CliArgumentsTest extends TestCase
{
    public function testBasic(): void
    {
        $f = new class () {
            public function m(
                #[CliArgument] string $a1,
                #[CliOption] bool $asd,
                #[CliOption] bool $a2 = false,
                #[CliOption] ?bool $a3 = null,
            ): array {
                return func_get_args();
            }
        };

        $c = new CliArguments(['qwe', '--asd'], $f, 'm');
        $params = $c->getAllParams();
        $this->assertSame(['a1' => 'qwe', 'asd' => true], $params);
        $this->assertSame(['qwe', true], $f->m(...$params));
    }

    public function testFailOnInvalidOption(): void
    {
        $f = new class () {
            public function m(
                #[CliArgument] string $a1,
                #[CliOption] ?string $a3 = null,
            ): array {
                return func_get_args();
            }
        };

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid option: --asd');
        new CliArguments(['qwe', '--asd'], $f, 'm');
    }

    public function testFailOnInvalidArgument(): void
    {
        $f = new class () {
            public function m(
                #[CliArgument] string $a1,
                #[CliOption] int $o1,
            ): array {
                return func_get_args();
            }
        };

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Invalid argument: asd");
        new CliArguments(['qwe', 'asd', '--o1=123'], $f, 'm');
    }

    public function testDefaultArgument(): void
    {
        $f = new class () {
            public function m(
                #[CliArgument] string $a = 'v',
            ): string {
                return $a;
            }
        };

        $params = (new CliArguments([], $f, 'm'))->getAllParams();
        $this->assertSame([], $params);
        $this->assertSame('v', $f->m());

        $params = (new CliArguments(['W'], $f, 'm'))->getAllParams();
        $this->assertSame(['a' => 'W'], $params);
        $this->assertSame('W', $f->m(...$params));
    }

    public function testDefaultOption(): void
    {
        $f = new class () {
            public function m(
                #[CliOption('optV')] string $opt = 'defV',
            ): string {
                return $opt;
            }
        };

        $params = (new CliArguments([], $f, 'm'))->getAllParams();
        $this->assertSame([], $params);
        $this->assertSame('defV', $f->m());

        $params = (new CliArguments(['--opt'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => 'optV'], $params);
        $this->assertSame('optV', $f->m(...$params));

        $params = (new CliArguments(['--opt=Val'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => 'Val'], $params);
        $this->assertSame('Val', $f->m(...$params));
    }

    public function testShortcut(): void
    {
        $f = new class () {
            public function m(
                #[CliOption(false, 'o')] bool $opt,
            ): bool {
                return $opt;
            }
        };

        $params = (new CliArguments(['--opt'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => false], $params);
        $this->assertFalse($f->m(...$params));

        $params = (new CliArguments(['-o'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => false], $params);
        $this->assertFalse($f->m(...$params));

        $params = (new CliArguments(['--opt=0'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => false], $params);
        $this->assertFalse($f->m(...$params));

        $params = (new CliArguments(['--opt=yes'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => true], $params);
        $this->assertTrue($f->m(...$params));
    }

    public function testTypecast(): void
    {
        $f = new class () {
            public function m(
                #[CliOption] int $opt,
            ): int {
                return $opt;
            }
        };

        $params = (new CliArguments(['--opt'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => 1], $params);
        $this->assertSame(1, $f->m(...$params));

        $params = (new CliArguments(['--opt=0'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => 0], $params);
        $this->assertSame(0, $f->m(...$params));

        $params = (new CliArguments(['--opt=10'], $f, 'm'))->getAllParams();
        $this->assertSame(['opt' => 10], $params);
        $this->assertSame(10, $f->m(...$params));

        $this->expectException(\InvalidArgumentException::class);
        (new CliArguments(['--opt=qwe'], $f, 'm'))->getAllParams();
    }

    public function testIdToCamel(): void
    {
        $f = new class () {
            public function m(
                #[CliOption] int $camelCaseProp,
            ): int {
                return $camelCaseProp;
            }
        };

        $params = (new CliArguments(['--camel-case-prop=5'], $f, 'm'))->getAllParams();

        $this->assertSame(['camelCaseProp' => 5], $params);
        $this->assertSame(5, $f->m(...$params));

        $params = (new CliArguments(['--camelCaseProp=7'], $f, 'm'))->getAllParams();
        $this->assertSame(['camelCaseProp' => 7], $params);
        $this->assertSame(7, $f->m(...$params));
    }

    public function testLikeTaskCommand(): void
    {
        $f = new class () {
            public function m(
                #[CliArgument] string $res,
                #[CliArgument] string $from = 'today',
                #[CliOption(true, 'd')] bool $debug = true,
            ): void {
            }
        };

        $params = (new CliArguments(['CV', '-1 minute'], $f, 'm'))->getAllParams();
        $this->assertSame(['res' => 'CV', 'from' => '-1 minute'], $params);

        $params = (new CliArguments(['CV', '-1 minute', '-d'], $f, 'm'))->getAllParams();
        $this->assertSame(['res' => 'CV', 'from' => '-1 minute', 'debug' => true], $params);

        $params = (new CliArguments(['CV', '-1 minute', '--debug'], $f, 'm'))->getAllParams();
        $this->assertSame(['res' => 'CV', 'from' => '-1 minute', 'debug' => true], $params);

        $params = (new CliArguments(['CV', '-minute'], $f, 'm'))->getAllParams();
        $this->assertSame(['res' => 'CV', 'from' => '-minute'], $params);

        $params = (new CliArguments(['CV', '-minute', '--debug=n'], $f, 'm'))->getAllParams();
        $this->assertSame(['res' => 'CV', 'from' => '-minute', 'debug' => false], $params);

        $params = (new CliArguments(['CV', '--debug=0'], $f, 'm'))->getAllParams();
        $this->assertSame(['res' => 'CV', 'debug' => false], $params);

        $params = (new CliArguments(['CV', '-d=no'], $f, 'm'))->getAllParams();
        $this->assertSame(['res' => 'CV', 'debug' => false], $params);

        $params = (new CliArguments(['CV', 'today -1 minute'], $f, 'm'))->getAllParams();
        $this->assertSame(['res' => 'CV', 'from' => 'today -1 minute'], $params);
    }
}
