<?php

declare(strict_types=1);

namespace app\tests\unit\components;

use app\back\components\services\GeoIp;
use app\tests\libs\ContainerUnitTrait;
use PHPUnit\Framework\TestCase;

class GeoIpTest extends TestCase
{
    use ContainerUnitTrait;

    private GeoIp $geoIp;

    public function setUp(): void
    {
        $this->geoIp = $this->container()->get(GeoIp::class);
    }

    public function testCountryRu(): void
    {
        self::assertSame('RU', $this->geoIp->getCountryCode('***************'));
    }

    public function testNoCountry(): void
    {
        self::assertSame(null, $this->geoIp->getCountryCode('***********'));
    }

    public function testCountryRuIpv6(): void
    {
        self::assertSame('RU', $this->geoIp->getCountryCode('2a00:1fa1:e00:9358:177c:53f3:f06a:9527'));
    }

    public function testNoCity(): void
    {
        self::assertSame(null, $this->geoIp->getCity('0.0.0.0'));
    }

    public function testCityCountryData(): void
    {
        [$country, $city] = $this->geoIp->getCountryCitySource('**********');

        self::assertSame('RU', $country);
        self::assertNotEmpty($city);
    }

    public function testCityCountryNullData(): void
    {
        self::assertSame([null, null, null], $this->geoIp->getCountryCitySource('0.0.0.0'));
    }

//    public function testMaxMindNullToIp2LocationData(): void
//    {
//        [$country, $city, $source] = $this->geoIp->getCountryCitySource('2405:8100:8000:5ca1::178:4401');
//
//        self::assertSame('US', $country);
//        self::assertSame('San Francisco', $city);
//        self::assertSame(GeoIp::SOURCE_MM, $source);
//    }

    /**
     * Next two tests represents difference between maxmind and ip2Location for one IP
     * maxmind:
     *  country => Ukraine
     *  city => Makiivka
     * ip2location:
     *  country => russia
     *  city => Tambovka
     */
    public function testUkraineCountryCityData(): void
    {
        [$country, $city, $source] = $this->geoIp->getCountryCitySource('************');
        self::assertSame('UA', $country);
        self::assertContains($city, ['Makiyivka', 'Makiivka', 'Yasynuvata', null]);
        self::assertSame(GeoIp::SOURCE_MM, $source);
    }

//     /**
//     * maxmind:
//     *  country => PL
//     *  city => null
//     * ip2location:
//     *  country => PL
//     *  city => Lodz
//     */
//    public function testMaxMindCityNullToIp2LocationData(): void
//    {
//        [$country, $_, $source] = $this->geoIp->getCountryCitySource('*************');
//
//        self::assertSame('PL', $country);
//        self::assertSame(GeoIp::SOURCE_IP2L, $source);
//    }

    /**
     * maxmind:
     *  country => null
     *  city => null
     * ip2location:
     *  country => SG
     *  city => Singapore
     */
    public function testMaxMindCityCountryNullToIp2LocationData(): void
    {
        self::assertSame(['SG', 'Singapore', GeoIp::SOURCE_IP2L], $this->geoIp->getCountryCitySource('*************'));
    }

    /**
     * maxmind:
     *  country => BR
     *  city => null
     * ip2location:
     *  country => US
     *  city => Mountain View
     */
    public function testMaxMindCountryCityDifferData(): void
    {
        self::assertSame(['BR', null, GeoIp::SOURCE_MM], $this->geoIp->getCountryCitySource('************'));
    }
}
