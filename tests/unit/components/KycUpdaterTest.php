<?php

declare(strict_types=1);

namespace app\tests\unit\components;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Json;
use app\back\components\kyc\KycStatusModel;
use app\back\entities\Employee;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use app\tests\libs\KycUpdateTrait;
use Monolog\Test\TestCase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;

#[CoversClass(KycStatusModel::class)]
class KycUpdaterTest extends TestCase
{
    use KycUpdateTrait;

    public static function statusChangeDataProvider(): array
    {
        return [
            // block doc errors
            // prevStatus,                 sendStatus,                         resStatus,                          prevDocs,      sendDocs,      resDocs
            [null,                         UserKyc::KYC_NOT_VERIFIED,          new \InvalidArgumentException(),    [],            [100],         []                  ],
            [null,                         UserKyc::KYC_VERIFIED,              new \InvalidArgumentException(),    [],            [100],         []                  ],
            [null,                         UserKyc::KYC_REQUEST,               new \InvalidArgumentException(),    [],            [100],         []                  ],
            [null,                         UserKyc::KYC_REQUEST_REPEAT,        new \InvalidArgumentException(),    [],            [100],         []                  ],
            [null,                         UserKyc::KYC_FAILED,                new \InvalidArgumentException(),    [],            [100],         []                  ],
            [null,                         UserKyc::KYC_WAIT,                  new \InvalidArgumentException(),    [],            [],            []                  ],
            [null,                         UserKyc::KYC_WAIT_WITHOUT_REQUEST,  new \InvalidArgumentException(),    [],            [],            []                  ],
            // block success
            [null,                         UserKyc::KYC_REQUEST,               UserKyc::KYC_REQUEST,               [],            [],            []                  ],
            [UserKyc::KYC_REQUEST,         UserKyc::KYC_WAIT,                  UserKyc::KYC_WAIT,                  [],            [1113],        [1113]              ],
            [UserKyc::KYC_WAIT,            UserKyc::KYC_WAIT,                  UserKyc::KYC_WAIT,                  [1333],        [2333, 3999],  [1333, 2333, 3999]  ],
            [UserKyc::KYC_WAIT,            UserKyc::KYC_FAILED,                UserKyc::KYC_FAILED,                [2333, 3999],  [],            []                  ],
            [UserKyc::KYC_FAILED,          UserKyc::KYC_VERIFIED,              UserKyc::KYC_VERIFIED,              [],            [],            []                  ],
            [UserKyc::KYC_VERIFIED,        UserKyc::KYC_NOT_VERIFIED,          UserKyc::KYC_NOT_VERIFIED,          [],            [],            []                  ],
            // block fixed status in result and docs in not wait pregress entities
            [UserKyc::KYC_NOT_VERIFIED,    UserKyc::KYC_WAIT,                  UserKyc::KYC_WAIT_WITHOUT_REQUEST,  [],            [3111],        [3111]              ],
            [UserKyc::KYC_VERIFIED,        UserKyc::KYC_WAIT,                  UserKyc::KYC_VERIFIED,              [],            [403, 404],    [403, 404]          ],
            [UserKyc::KYC_FAILED,          UserKyc::KYC_WAIT,                  UserKyc::KYC_FAILED,                [],            [1888],        [1888]              ],
            [UserKyc::KYC_WAIT,            UserKyc::KYC_REQUEST,               UserKyc::KYC_REQUEST_REPEAT,        [1113],        [],            []                  ],
            [UserKyc::KYC_REQUEST_REPEAT,  UserKyc::KYC_WAIT_WITHOUT_REQUEST,  UserKyc::KYC_WAIT,                  [],            [1333],        [1333]              ],
        ];
    }

    #[DataProvider('statusChangeDataProvider')]
    public function testStatusChange(int|null $prevStatus, int $sendStatus, int | \Throwable $resStats, array $curDocs, array $sendDocs, array $resDocs): void
    {
        $kycStatusModel = $this->container()->get(KycStatusModel::class);
        $kycStatusModel->source = UserDocumentProgress::SOURCE_ANALYTICS;
        $user = $this->haveUserRecord();

        $kycStatusModel->fixAndValidateStatus = false;
        if ($prevStatus !== null) {
            $kycStatusModel->update($user->site_id, $user->user_id, $prevStatus, docIds: $curDocs);
            $this->assertKycStatus($user, $prevStatus, null, $curDocs);
        }

        $kycStatusModel->fixAndValidateStatus = true;

        if ($resStats instanceof \Throwable) {
            try {
                $kycStatusModel->update($user->site_id, $user->user_id, $sendStatus, docIds: $sendDocs);
                self::fail('Status "' . (UserKyc::KYC_STATUSES[$sendStatus] ?? $sendStatus) . '" with docs ' . Json::encode($sendDocs) . ' must throw error');
            } catch (\Throwable $e) {
                if (!$e instanceof $resStats) {
                    throw $e;
                }
            }
            return;
        }

        $kycStatusModel->update($user->site_id, $user->user_id, $sendStatus, docIds: $sendDocs);
        $this->assertKycStatus($user, $resStats, $prevStatus, $resDocs);
    }

    public static function departmentKycChangeDataProvider(): array
    {
        $system = [UserDocumentProgress::SOURCE_ANALYTICS, null];
        $s2pApi = [UserDocumentProgress::SOURCE_API_S2P, null];
        $s2pWeb = [UserDocumentProgress::SOURCE_ANALYTICS, Employee::KYC_DEPARTMENT_S2P_RISK];
        $analyticsWeb = [UserDocumentProgress::SOURCE_ANALYTICS, Employee::KYC_DEPARTMENT_ANALYTICS_RISK];
        return [
            // formCred,        toCred,            fromStatus,             toStatus,               resStatus
            [...$analyticsWeb,  ...$s2pApi,        UserKyc::KYC_REQUEST,   UserKyc::KYC_VERIFIED,  new \InvalidArgumentException()  ],
            [...$s2pApi,        ...$analyticsWeb,  UserKyc::KYC_REQUEST,   UserKyc::KYC_VERIFIED,  new \InvalidArgumentException()  ],
            [...$analyticsWeb,  ...$s2pWeb,        UserKyc::KYC_REQUEST,   UserKyc::KYC_VERIFIED,  new \InvalidArgumentException()  ],

            [...$system,        ...$s2pApi,        UserKyc::KYC_REQUEST,   UserKyc::KYC_VERIFIED,  UserKyc::KYC_VERIFIED],
            [...$s2pApi,        ...$s2pWeb,        UserKyc::KYC_REQUEST,   UserKyc::KYC_VERIFIED,  UserKyc::KYC_VERIFIED],
            [...$analyticsWeb,  ...$analyticsWeb,  UserKyc::KYC_REQUEST,   UserKyc::KYC_VERIFIED,  UserKyc::KYC_VERIFIED],
            [...$system,        ...$analyticsWeb,  UserKyc::KYC_REQUEST,   UserKyc::KYC_VERIFIED,  UserKyc::KYC_VERIFIED],

            [...$s2pWeb,        ...$analyticsWeb,  UserKyc::KYC_VERIFIED,  UserKyc::KYC_REQUEST,   UserKyc::KYC_REQUEST],
        ];
    }

    #[DataProvider('departmentKycChangeDataProvider')]
    public function testDepartmentKycChange(int $formSource, int | null $fromDepartment, int $toSource, int | null $toDepartment, int $fromStatus, int $toStatus, int | \Throwable $resStatus): void
    {
        $user = $this->haveUserRecord();

        /** @var KycStatusModel $fromKycStatusModel */
        $fromEmployee = $this->haveEmployee(['kyc_department' => $fromDepartment]);
        $fromKycStatusModel = $this->container()->withDefinitions([BaseAuthAccess::class => $this->fakeAuthAccess($fromEmployee)], fn() => $this->container()->get(KycStatusModel::class));
        $fromKycStatusModel->source = $formSource;

        $toEmployee = $this->haveEmployee(['kyc_department' => $toDepartment]);
        /** @var KycStatusModel $toKycStatusModel */
        $toKycStatusModel = $this->container()->withDefinitions([BaseAuthAccess::class => $this->fakeAuthAccess($toEmployee)], fn() => $this->container()->get(KycStatusModel::class));
        $toKycStatusModel->throwIfDepartmentLocked = $resStatus instanceof \Throwable ? $resStatus : new \Exception();
        $toKycStatusModel->source = $toSource;

        $fromKycStatusModel->update($user->site_id, $user->user_id, $fromStatus);
        try {
            $toKycStatusModel->update($user->site_id, $user->user_id, $toStatus);
            if ($resStatus instanceof \Throwable) {
                self::fail();
            }
        } catch (\InvalidArgumentException $e) {
            if (is_int($resStatus) || !$e instanceof $resStatus) {
                throw $e;
            }
            if (!$resStatus instanceof \Throwable) {
                self::fail();
            }
        }

        if (is_int($resStatus)) {
            $this->assertKycStatus($user, $toStatus, $fromStatus);
        }
    }

    public function testExpiredKycStatus(): void
    {
        $kycStatusModel = $this->container()->get(KycStatusModel::class);
        $kycStatusModel->source = UserDocumentProgress::SOURCE_ANALYTICS;
        $kycStatusModel->fixAndValidateStatus = false;
        $user = $this->haveUserRecord();

        $kycStatusModel->update($user->site_id, $user->user_id, UserKyc::KYC_VERIFIED, createdAt: date(DateHelper::DATETIME_FORMAT_PHP));
        $this->assertKycStatus($user, UserKyc::KYC_VERIFIED);

        // EXPIRED metric
        $statusTime = date(DateHelper::DATETIME_FORMAT_PHP, strtotime('-10 seconds'));
        $kycStatusModel->update($user->site_id, $user->user_id, UserKyc::KYC_FAILED, createdAt: $statusTime);
        $this->assertKycStatus($user, UserKyc::KYC_VERIFIED, UserKyc::KYC_VERIFIED);

        // FRESH metric
        $statusTime = date(DateHelper::DATETIME_FORMAT_PHP, strtotime('+10 seconds'));
        $kycStatusModel->update($user->site_id, $user->user_id, UserKyc::KYC_FAILED, createdAt: $statusTime);
        $this->assertKycStatus($user, UserKyc::KYC_FAILED, expectedTime: $statusTime);
    }

    private function fakeAuthAccess(Employee $employee): BaseAuthAccess
    {
        return new class ($employee) extends BaseAuthAccess
        {
            public function __construct(private readonly Employee $employee)
            {
            }

            public function can(string $rule): bool
            {
                return true;
            }

            public function employeeId(): int
            {
                return $this->employee->employee_id;
            }

            public function employee(): Employee
            {
                return $this->employee;
            }
        };
    }
}
