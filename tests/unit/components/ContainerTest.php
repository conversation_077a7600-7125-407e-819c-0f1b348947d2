<?php

declare(strict_types=1);

namespace app\tests\unit\components;

use app\back\components\AccessChecker;
use app\back\components\BaseAuthAccess;
use app\back\components\SystemEmployeeAuthAccess;
use app\back\components\Container;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\repositories\Employees;
use Monolog\Logger;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Yiisoft\Db\Connection\ConnectionInterface;

class ContainerTest extends TestCase
{
    public function testResolving(): void
    {
        $container = new Container();
        $container->addShared(BaseAuthAccess::class, SystemEmployeeAuthAccess::class)
            ->addArgument(Employees::class);
        $staticContainer = new Container();

        self::assertInstanceOf(SystemEmployeeAuthAccess::class, $container->get(BaseAuthAccess::class));
        self::assertInstanceOf(AccessChecker::class, $container->get(AccessChecker::class));

        self::assertSame($staticContainer->get(ConnectionInterface::class), $container->get(ConnectionInterface::class));
        self::assertNotSame($container->get(SiteIdColumn::class), $container->get(SiteIdColumn::class));

        // create instance with default $timezone instead of creation DateTimeZone with error
        self::assertInstanceOf(\DateTimeImmutable::class, $staticContainer->get(\DateTimeImmutable::class));

        try {
            self::fail($staticContainer->get(BaseAuthAccess::class)::class . ' leaked to static container');
        } catch (\Error) {
        }
        try {
            self::fail($staticContainer->get(SiteIdColumn::class)::class . ' leaked to static container');
        } catch (\Error) {
        }
    }

    public function testInterfaceAndCreate(): void
    {
        $container = new Container();
        $container->add(LoggerInterface::class, Logger::class)
            ->addArgument('test channel');

        $logger = $container->get(LoggerInterface::class);
        self::assertInstanceOf(Logger::class, $logger);
        $loggerCustom = $container->create(Logger::class, ['name' => 'customName']);
        self::assertInstanceOf(Logger::class, $loggerCustom);
        self::assertNotSame($logger, $loggerCustom);
        self::assertSame('customName', $loggerCustom->getName());
        self::assertSame($logger, $container->call(fn(LoggerInterface $sameLogger) => $sameLogger));
    }
}
