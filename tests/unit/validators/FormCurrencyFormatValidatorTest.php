<?php

declare(strict_types=1);

namespace app\tests\unit\validators;

use app\back\components\Form;
use app\back\components\validators\CurrencyFormatValidator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(CurrencyFormatValidator::class)]
class FormCurrencyFormatValidatorTest extends TestCase
{
    public function testDefault(): void
    {
        $form = new class
        {
            use Form;

            #[CurrencyFormatValidator]
            public string $currency;
        };
        $this->assertSame(['currency' => 'Currency is required'], $form->validate(['currency' => null]));
        $this->assertSame(['currency' => 'Currency is required'], $form->validate(['currency' => false]));
        $this->assertSame(['currency' => 'Currency is too long (30 > 10)'], $form->validate(['currency' => 'ABCDEFGHIJABCDEFGHIJABCDEFGHIJ']));
        $this->assertSame(['currency' => 'Currency is not currency code'], $form->validate(['currency' => 'usd']));
        $this->assertSame([], $form->validate(['currency' => 'USD']));
    }

    public function testCustomLength(): void
    {
        $form = new class
        {
            use Form;

            #[CurrencyFormatValidator(4)]
            public string $currency;
        };

        $this->assertSame(['currency' => 'Currency is too long (5 > 4)'], $form->validate(['currency' => 'USDTS']));
        $this->assertSame(['currency' => 'Currency is not currency code'], $form->validate(['currency' => 'usdt']));
        $this->assertSame([], $form->validate(['currency' => 'USD']));
    }
}
