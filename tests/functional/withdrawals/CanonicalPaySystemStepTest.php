<?php

declare(strict_types=1);

namespace app\tests\functional\withdrawals;

use app\back\entities\CanonicalPaySySource;
use app\back\entities\Site;
use app\back\entities\Withdrawal;
use app\back\modules\finance\withdrawalsAutoProcessing\components\steps\CanonicalPaySystemStep;
use app\back\repositories\CanonicalPaySySources;
use app\back\repositories\CanonicalPaySystems;
use app\back\repositories\PaySystems;
use app\back\repositories\Withdrawals;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(CanonicalPaySystemStep::class)]
class CanonicalPaySystemStepTest extends BaseWithdrawalTestCase
{
    use DbTransactionalUnitTrait;

    public function testCanonicalPaySystem(): void
    {
        $paySysName1 = 'bitcoin';
        $paySysName2 = 'bitcoin_bc';
        $paySysName3 = 'visa';

        $canonicalPaySystemName = 'bitcoin';
        $canonicalPaySystemOperator = '=';

        $this->haveRecords(PaySystems::class, [
            ['name' => $paySysName1],
            ['name' => $paySysName2],
            ['name' => $paySysName3],
        ]);

        $canonicalPaySystems = $this->haveRecords(CanonicalPaySystems::class, [
            ['name' => $canonicalPaySystemName],
        ]);

        $this->haveRecords(CanonicalPaySySources::class, [
            ['canonical_pay_sys_id' => $canonicalPaySystems[0]->id, 'source' => CanonicalPaySySource::SOURCE_S2P, 'name' => $paySysName1],
            ['canonical_pay_sys_id' => $canonicalPaySystems[0]->id, 'source' => CanonicalPaySySource::SOURCE_S2P, 'name' => $paySysName2],
        ]);

        $user1 = $this->haveUserRecord(['site_id' => Site::GGB]);
        $user2 = $this->haveUserRecord(['site_id' => Site::GGB]);
        $user3 = $this->haveUserRecord(['site_id' => Site::GGB]);

        $userStat1 = $this->haveUserStatRecordOut($user1, '-2 hour', $paySysName1);
        $userStat2 = $this->haveUserStatRecordOut($user2, '-1 hour', $paySysName2);
        $userStat3 = $this->haveUserStatRecordOut($user3, '-1 hour', $paySysName3);

        $this->expected->rows_processed = 3;
        $this->expected->rows_finished = 2;
        $this->expected->logs = [
            [
                'transaction_id' => $userStat1->transaction_id,
                'user_id' => $user1->user_id,
                'decision' => 'success',
                'comment' => "Allow, User: $user1->user_id, Amount: $userStat1->amount_orig $userStat1->currency, Requisite: $userStat1->wallet",
            ],
            [
                'transaction_id' => $userStat2->transaction_id,
                'user_id' => $user2->user_id,
                'decision' => 'success',
                'comment' => "Allow, User: $user2->user_id, Amount: $userStat2->amount_orig $userStat2->currency, Requisite: $userStat2->wallet"
            ],
            [
                'transaction_id' => $userStat3->transaction_id,
                'user_id' => $user3->user_id,
                'decision' => 'no',
                'comment' => "Not processed, User $user3->user_id Pay System = $paySysName3, but CanonicalPaySystem $canonicalPaySystemOperator $canonicalPaySystemName expected",
            ],
        ];

        $extra = [
            'rule_id' => $this->rule->id,
        ];

        $this->resultProcessRule([
            "CanonicalPaySystem(name $canonicalPaySystemOperator $canonicalPaySystemName)",
        ]);

        $this->seeRecords(Withdrawals::class, [
            [
                'site_id' => $user1->site_id,
                'transaction_id' => $userStat1->transaction_id,
                'user_id' => $user1->user_id,
                'status' => Withdrawal::STATUS_NEW,
                'operator_id' => $this->employeeId,
                'decision' => Withdrawal::DECISION_ALLOW,
                'extra' => $extra,
            ],
            [
                'site_id' => $user2->site_id,
                'transaction_id' => $userStat2->transaction_id,
                'user_id' => $user2->user_id,
                'status' => Withdrawal::STATUS_NEW,
                'operator_id' => $this->employeeId,
                'decision' => Withdrawal::DECISION_ALLOW,
                'extra' => $extra,
            ],
        ]);

        $this->dontSeeRecords(Withdrawals::class, [
            [
                'site_id' => $user3->site_id,
                'transaction_id' => $userStat3->transaction_id,
            ],
        ]);
    }
}
