<?php

declare(strict_types=1);

namespace app\tests\functional\withdrawals;

use app\back\entities\Site;
use app\back\entities\Withdrawal;
use app\back\modules\finance\withdrawalsAutoProcessing\components\steps\UserCountryStep;
use app\back\repositories\Withdrawals;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserCountryStep::class)]
class UserCountryStepTest extends BaseWithdrawalTestCase
{
    use DbTransactionalUnitTrait;

    public function testUserCountry(): void
    {
        $ruleCountry = 'BR, UA, TR';

        $user1 = $this->haveUserRecord(['site_id' => Site::GGB, 'country' => 'UA']);
        $user2 = $this->haveUserRecord(['site_id' => Site::GGB, 'country' => 'PL']);
        $user3 = $this->haveUserRecord(['site_id' => Site::GGB, 'country' => null]);

        $userStat1 = $this->haveUserStatRecordOut($user1);
        $userStat2 = $this->haveUserStatRecordOut($user2);
        $userStat3 = $this->haveUserStatRecordOut($user3);

        $this->expected->rows_processed = 3;
        $this->expected->rows_finished = 1;
        $this->expected->logs = [
            [
                'transaction_id' => $userStat1->transaction_id,
                'user_id' => $user1->user_id,
                'decision' => 'success',
                'comment' => "Allow, User: $user1->user_id, Amount: " . number_format((float)$userStat1->amount_orig, 2) .
                    " $userStat1->currency, Requisite: $userStat1->wallet",
            ],
            [
                'transaction_id' => $userStat2->transaction_id,
                'user_id' => $user2->user_id,
                'decision' => 'no',
                'comment' => "Not processed, User $user2->user_id country = PL, but = $ruleCountry expected",
            ],
            [
                'transaction_id' => $userStat3->transaction_id,
                'user_id' => $user3->user_id,
                'decision' => 'no',
                'comment' => "Not processed, User $user3->user_id country = , but = $ruleCountry expected",
            ],
        ];

        $this->resultProcessRule([
            "UserCountry(country = $ruleCountry)",
        ]);

        $this->seeRecord(Withdrawals::class, [
            'site_id' => $user1->site_id,
            'transaction_id' => $userStat1->transaction_id,
            'user_id' => $user1->user_id,
            'status' => Withdrawal::STATUS_NEW,
            'operator_id' => $this->employeeId,
            'decision' => Withdrawal::DECISION_ALLOW,
            'extra' => ['rule_id' => $this->rule->id],
        ]);
    }
}
