<?php

declare(strict_types=1);

namespace app\tests\functional\withdrawals;

use app\back\entities\Site;
use app\back\entities\Withdrawal;
use app\back\modules\finance\withdrawalsAutoProcessing\components\steps\HasNoBlockedRequisitesStep;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\Withdrawals;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(HasNoBlockedRequisitesStep::class)]
class HasNoBlockedRequisitesStepTest extends BaseWithdrawalTestCase
{
    use DbTransactionalUnitTrait;

    public function testCheckHasNoBlockedRequisitesStep(): void
    {
        $user1 = $this->haveUserRecord(['site_id' => Site::GGB]);
        $user2 = $this->haveUserRecord(['site_id' => Site::GGB]);

        $userStat1 = $this->haveUserStatRecordOut($user1);
        $userStat2 = $this->haveUserStatRecordOut($user2);

        $this->haveRecords(UserSpecialInfos::class, [
            [
                'site_id' => Site::GGB,
                'user_id' => $user1->user_id,
                'has_blocked_requisites' => true,
            ],
            [
                'site_id' => Site::GGB,
                'user_id' => $user2->user_id,
                'has_blocked_requisites' => false,
            ]
        ]);

        $this->expected->rows_processed = 2;
        $this->expected->rows_finished = 1;
        $this->expected->logs = [
            [
                'transaction_id' => $userStat1->transaction_id,
                'user_id' => $user1->user_id,
                'decision' => 'no',
                'comment' => "Not processed, User $user1->user_id has blocked requisites",
            ],
            [
                'transaction_id' => $userStat2->transaction_id,
                'user_id' => $user2->user_id,
                'decision' => 'success',
                'comment' => "Allow, User: $user2->user_id, Amount: $userStat2->amount_orig $userStat2->currency, Requisite: $userStat2->wallet",
            ],
        ];

        $this->resultProcessRule([
            'HasNoBlockedRequisites',
        ]);

        $this->seeRecord(Withdrawals::class, [
            'site_id' => $user2->site_id,
            'transaction_id' => $userStat2->transaction_id,
            'user_id' => $user2->user_id,
            'status' => Withdrawal::STATUS_NEW,
            'operator_id' => $this->employeeId,
            'decision' => Withdrawal::DECISION_ALLOW,
            'extra' => ['rule_id' => $this->rule->id],
        ]);

        $this->dontSeeRecord(Withdrawals::class, [
            'site_id' => $user1->site_id,
            'transaction_id' => $userStat1->transaction_id,
        ]);
    }
}
