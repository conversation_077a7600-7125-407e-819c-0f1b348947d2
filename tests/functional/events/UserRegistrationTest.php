<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\modules\events\events\UserRegistrationEvent;
use app\back\repositories\Users;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserRegistrationEvent::class)]
class UserRegistrationTest extends BaseEventTestCase
{
    public function testTaskUsers(): void
    {
        $userId = self::uniqRuntimeId();
        $siteId = Site::GGB;

        $this->dontSeeRecord(Users::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
        ]);

        $subscription = $this->haveSubscription('crm', 'user_registration', ['site_id' => $siteId]);

        MockServer::with(function () use ($userId) {
            $this->runTask('users', Res::GGB, self::LAST_MINUTE_PERIOD, $this->csv([[
                'userId' => $userId,
                'email' => '<EMAIL>',
                'dateCreated' => '2021-10-17 07:35:05',
                'emailConfirmed' => 1,
            ]]));

            $this->sendQueuedEvents();
        }, [
            $this->eventRequest($subscription, [[
                'site_id' => $siteId,
                'user_id' => $userId,
            ]])
        ]);
    }
}
