<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\entities\Useragent;
use app\back\entities\UserContact;
use app\back\modules\events\events\UserContactSubscriptionAddEvent;
use app\back\repositories\Useragents;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserContactSubscriptionAddEvent::class)]
class UserContactSubscriptionsAddTest extends BaseEventTestCase
{
    public function testTaskUsersDevices(): void
    {
        $useragent = 'Dalvik/2.1.0 (Linux; U; Android 13; 2109119DG Build/TKQ1.220829.002) NativeApp/Android/com.adm777.app/v0.8.7/intbrowsrm';
        $useragentsRepo = $this->repo(Useragents::class);
        $useragentId = $useragentsRepo->createDictionary()->getIdByName($useragent);

        /** @var Useragent $useragent */
        $useragent = $useragentsRepo->findOne(['id' =>  $useragentId]);

        $subscription = $this->haveSubscription('crm', 'subscription_add', [
            'site_id' => Site::GGB,
        ]);
        $userContact = new UserContact([
            'site_id' => Site::GGB,
            'user_id' => self::uniqRuntimeId(),
            'type' => UserContact::TYPE_SUBSCRIPTION,
            'value' => uniqid('', false),
            'useragent_id' => $useragentId,
        ]);
        $userContactMismatchType = new UserContact([
            'site_id' => Site::GGB,
            'user_id' => self::uniqRuntimeId(),
            'type' => UserContact::TYPE_EMAIL,
            'value' => uniqid('', false),
            'useragent_id' => $useragentId,
        ]);

        MockServer::with(function () use ($userContact, $userContactMismatchType, $useragent) {
            $this->runTask('users-devices', Res::GGB, self::LAST_MINUTE_PERIOD, $this->csv([[
                'playerId' => $userContact->user_id,
                'subscriptionId' => $userContact->value,
                'userAgent' => $useragent,
            ], [
                'playerId' => $userContactMismatchType->user_id,
                'subscriptionId' => $userContactMismatchType->value,
                'userAgent' => $useragent,
            ]]));
            $this->sendQueuedEvents();
        }, [
            $this->eventRequest($subscription, [[
                'site_id' => $userContact->site_id,
                'user_id' => $userContact->user_id,
                'variant_id' => $useragent->variant_id,
                'platform_id' => $useragent->platform_id,
            ]])
        ]);
    }
}
