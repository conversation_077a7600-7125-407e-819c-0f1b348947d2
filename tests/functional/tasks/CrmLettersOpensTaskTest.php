<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\config\tasks\Res;
use app\back\entities\CrmLetter;
use app\back\entities\Site;
use app\back\entities\UserContact;
use app\back\modules\task\actions\import\crm\CrmLettersOpensTask;
use app\back\repositories\CrmLetters;
use app\back\repositories\UserContacts;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(CrmLettersOpensTask::class)]
class CrmLettersOpensTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private const string DATA = <<<DATA
    [
        {
            "contact": "<EMAIL>",
            "bulk_id": "1",
            "contact_type": 1,
            "site_id": 7,
            "user_id": 1,
            "open_count": "2",
            "tracked_at": "2020-01-02 09:25:37"
        },
        {
            "contact": "<EMAIL>",
            "bulk_id": "1",
            "contact_type": 1,
            "site_id": 7,
            "user_id": 1,
            "open_count": "2",
            "tracked_at": "2020-01-02 09:25:33"
        },
        {
            "contact": "<EMAIL>",
            "bulk_id": "1",
            "contact_type": 1,
            "site_id": 7,
            "user_id": 2,
            "open_count": "1",
            "tracked_at": "2020-01-02 09:25:34"
        }
    ]
    DATA;

    public function testImport(): void
    {
        $siteId = Site::CV;

        /** @var UserContact $contact1 */
        $contact1 = $this->haveRecord(UserContacts::class, [
            'site_id' => $siteId,
            'user_id' => 1,
            'type' => UserContact::TYPE_EMAIL,
            'value' => '<EMAIL>',
        ]);

        /** @var UserContact $contact2 */
        $contact2 = $this->haveRecord(UserContacts::class, [
            'site_id' => $siteId,
            'user_id' => 1,
            'type' => UserContact::TYPE_EMAIL,
            'value' => '<EMAIL>',
        ]);

        /** @var UserContact $contact3 */
        $contact3 = $this->haveRecord(UserContacts::class, [
            'site_id' => $siteId,
            'user_id' => 2,
            'type' => UserContact::TYPE_EMAIL,
            'value' => '<EMAIL>',
        ]);

        /** @var CrmLetter[] $letters */
        $letters = $this->haveRecords(CrmLetters::class, [
            [
                'id' => 1,
                'site_id' => $siteId,
                'user_id' => $contact1->user_id,
                'bulk_id' => 1,
                'status' => 0,
                'type' => 0,
                'contact_id' => $contact1->id,
                'sended_at' => new \DateTimeImmutable('2020-01-01 07:50:14'),
                'opened_at' => new \DateTimeImmutable('2020-01-01 08:50:14'),
                'clicked_at' => new \DateTimeImmutable('2020-01-01 08:55:14'),
            ],
            [
                'id' => 2,
                'site_id' => $siteId,
                'user_id' => $contact2->user_id,
                'bulk_id' => 1,
                'status' => 0,
                'type' => 0,
                'contact_id' => $contact2->id,
                'sended_at' => new \DateTimeImmutable('2020-01-01 07:50:14'),
                'opened_at' => null,
                'clicked_at' => null,
            ],
            [
                'id' => 3,
                'site_id' => $siteId,
                'user_id' => $contact3->user_id,
                'bulk_id' => 1,
                'status' => 0,
                'type' => 0,
                'contact_id' => $contact3->id,
                'sended_at' => new \DateTimeImmutable('2020-01-01 07:50:14'),
                'opened_at' => null,
                'clicked_at' => null,
            ],
        ]);

        $this->runTask('crm-letters-opens', Res::CRM, $this->debugFile(static::DATA));

        $this->seeRecordWithFields(CrmLetters::class, ['id' => $letters[0]->id], ['opened_at' => new \DateTimeImmutable('2020-01-01 08:50:14')]);
        $this->seeRecordWithFields(CrmLetters::class, ['id' => $letters[1]->id], ['opened_at' => new \DateTimeImmutable('2020-01-02 09:25:33')]);
        $this->seeRecordWithFields(CrmLetters::class, ['id' => $letters[2]->id], ['opened_at' => new \DateTimeImmutable('2020-01-02 09:25:34')]);
    }
}
