<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\components\DateTimeImmutableWithMicroseconds;
use app\back\entities\BaseEntity;
use app\back\entities\Site;
use app\back\entities\UserEventCheckout;
use app\back\modules\task\actions\update\UsersEventsCheckoutTask;
use app\back\repositories\UserEventCheckouts;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UsersEventsCheckoutTask::class)]
class UsersEventsCheckoutTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;
    use FakerCheckUnitTrait;

    private string $startTime;

    public function testUpdateOpenId(): void
    {
        $this->startTime = '2025-05-28 13:20';
        $siteId = Site::CV;

        $userId1 = self::uniqRuntimeId();
        $contextId1 = (string) self::uniqRuntimeId();
        $productOpenId1 = self::uniqRuntimeUuid();

        $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_INIT, null, $productOpenId1, $siteId, $userId1);
        $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_URL_RECEIVED, $contextId1, $productOpenId1, $siteId, $userId1);
        $eventProduct1 = $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_URL_STARTED, $contextId1, $productOpenId1, $siteId, $userId1);
        $eventsS2p1 = [
            $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_OPENED, $contextId1),
            $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_SELECTED, $contextId1),
            $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_TRY_PAY, $contextId1),
        ];

        $userId2 = self::uniqRuntimeId();
        $contextId2 = (string) self::uniqRuntimeId();
        $productOpenId2 = self::uniqRuntimeUuid();

        $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_INIT, null, $productOpenId2, $siteId, $userId2);
        $eventProduct2 = $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_URL_RECEIVED, $contextId2, $productOpenId2, $siteId, $userId2);
        $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_IFRAME_LOADED, null, $productOpenId2, $siteId, $userId2);
        $eventS2p2 = $this->haveUserEventCheckoutsRecord(UserEventCheckout::EVENT_TYPE_OPENED, $contextId2);

        $this->runTask('update-users-events-checkout', 'CV', $this->startTime, 'PT1M');

        foreach ($eventsS2p1 as $event) {
            $this->seeRecordWithFields(
                UserEventCheckouts::class,
                ['event_id' => $event->event_id],
                ['product_open_id' => $eventProduct1->product_open_id, 'site_id' => $eventProduct1->site_id, 'user_id' => $eventProduct1->user_id]
            );
        }
        $this->seeRecordWithFields(
            UserEventCheckouts::class,
            ['event_id' => $eventS2p2->event_id],
            ['product_open_id' => $eventProduct2->product_open_id, 'site_id' => $eventProduct2->site_id, 'user_id' => $eventProduct2->user_id]
        );
    }

    private function haveUserEventCheckoutsRecord(int $eventType, ?string $contextId, ?string $productOpenId = null, ?int $siteId = null, ?int $userId = null): BaseEntity
    {
        return $this->haveRecord(UserEventCheckouts::class, [
            'event_id' => self::uniqRuntimeId(),
            'site_id' => $siteId,
            'user_id' => $userId,
            'event_type' => $eventType,
            'context_id' => $contextId,
            'inserted_at' => $this->startTime,
            'product_open_id' => $productOpenId,
            'client_created_at' => new DateTimeImmutableWithMicroseconds(),
            'server_received_at' => new DateTimeImmutableWithMicroseconds(),
        ]);
    }
}
