<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\send;

use app\back\components\Console;
use app\back\components\helpers\DateHelper;
use app\back\config\tasks\Res;
use app\back\entities\ContactServiceProvider;
use app\back\entities\Country;
use app\back\entities\Game;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\UserContact;
use app\back\entities\UserTransaction;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\modules\task\actions\send\ActiveUsersTask;
use app\back\modules\task\actions\send\BaseSendTask;
use app\back\modules\task\actions\send\CheckboxReceiptsTask;
use app\back\modules\task\actions\send\CohortDailyGgbBaseTask;
use app\back\modules\task\actions\send\CrmOrTask;
use app\back\modules\task\actions\send\CrmTopPaymentsCountriesTask;
use app\back\modules\task\actions\send\DepsMetricsTask;
use app\back\modules\task\actions\send\DynamicFdTask;
use app\back\modules\task\actions\send\DynamicFdVpTask;
use app\back\modules\task\actions\send\IncorrectAffiliatesRefcodesTask;
use app\back\modules\task\actions\send\NewCanonicalPaySysTask;
use app\back\modules\task\actions\send\NotifyBigDepUsersTask;
use app\back\modules\task\actions\send\NotifyCgBigDepsTask;
use app\back\modules\task\actions\send\NotifyNewBigDepUsersTask;
use app\back\modules\task\actions\send\NotifyVipInactiveDepositTask;
use app\back\modules\task\actions\send\NotifyWeekBigDepUsersTask;
use app\back\modules\task\actions\send\NotifyWeekendBigDepsTask;
use app\back\modules\task\actions\send\OutPercentByCountryTask;
use app\back\modules\task\actions\send\OutPercentDailyTask;
use app\back\modules\task\actions\send\OutPercentMonthlyTask;
use app\back\modules\task\actions\send\OutPercentWeeklyTask;
use app\back\modules\task\actions\send\PmCidContactsTask;
use app\back\modules\task\actions\send\RevenueDistributionTask;
use app\back\modules\task\actions\send\RokeenteTagsTask;
use app\back\modules\task\actions\send\S2pNotifyDocumentsWaitTask;
use app\back\modules\task\actions\send\SpeedOutTransactionsTask;
use app\back\modules\task\actions\send\StpPaymentsTask;
use app\back\modules\task\actions\send\TopPayCountryPtgWeekTask;
use app\back\modules\task\actions\send\TrustlyUsersTask;
use app\back\modules\task\actions\send\UsersDepsTopDailyTask;
use app\back\modules\task\actions\send\UsersDepTask;
use app\back\modules\task\actions\send\UsersGamesCasinoWeeklyTask;
use app\back\modules\task\actions\send\UsersRegProtonTask;
use app\back\modules\task\actions\send\VipUsersNightCallsTask;
use app\back\modules\task\TaskConfigs;
use app\back\repositories\ContactServiceProviders;
use app\back\repositories\Games;
use app\back\repositories\HhsUserGameSessions;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class ReportValidationSendTaskTest extends TestCase
{
    use DbTransactionalUnitTrait;

    private const array SKIP_NO_REPORTS_TASKS = [
        ActiveUsersTask::class,
        DepsMetricsTask::class,
        DynamicFdTask::class,
        DynamicFdVpTask::class,
        IncorrectAffiliatesRefcodesTask::class,
        NewCanonicalPaySysTask::class,
        NotifyBigDepUsersTask::class,
        NotifyCgBigDepsTask::class,
        NotifyNewBigDepUsersTask::class,
        NotifyVipInactiveDepositTask::class,
        NotifyWeekBigDepUsersTask::class,
        NotifyWeekendBigDepsTask::class,
        OutPercentByCountryTask::class,
        PmCidContactsTask::class,
        RokeenteTagsTask::class,
        S2pNotifyDocumentsWaitTask::class,
        SpeedOutTransactionsTask::class,
        StpPaymentsTask::class,
        TopPayCountryPtgWeekTask::class,
        TrustlyUsersTask::class,
        UsersDepsTopDailyTask::class,
        UsersDepTask::class,
        UsersRegProtonTask::class,
        VipUsersNightCallsTask::class,
        OutPercentDailyTask::class,
        OutPercentMonthlyTask::class,
        OutPercentWeeklyTask::class,
        CheckboxReceiptsTask::class,
    ];

    #[DataProvider('tasksConfigs')]
    public function testAll(string $taskName, array $config, ?\Closure $beforeTask): void
    {
        self::consoleOutputToDebug(function () use ($taskName, $config, $beforeTask) {
            $beforeTask && ($beforeTask->bindTo($this))($config);
            $this->runTask($taskName, $this->container()->getFromConfig($config));
        });
    }

    private function runTask(string $taskName, BaseSendTask $task): void
    {
        self::assertTrue(
            BaseReportConfig::isSomeReportExecuted(static function () use ($task) {
                $task->getContent();
                $task->getAttachments();
            }),
            Console::format("No report executed for task '$taskName', " .
                "check if really this task exec report and do somethings to make it work, " .
                "typically add to SKIP_NO_REPORTS_TASKS if no reports or add conditions to beforeTaskClosures()", Console::BG_RED, Console::FG_GREY)
        );
    }

    public static function tasksConfigs(): iterable
    {
        $tasks = array_filter(self::taskConfigsForSend(), static fn($config) => !in_array($config['class'] ?? $config, self::SKIP_NO_REPORTS_TASKS, true));
        $beforeTasksClosures = self::beforeTaskClosures($tasks);

        foreach ($tasks as $taskName => $config) {
            if (is_string($config)) {
                $config = ['class' => $config];
            }

            $config = [
                ...$config,
                'resource' => Res::SEND,
                'from' => 'today',
                'period' => 'P1D',
                'skipEvents' => true,
                'debug' => true,
            ];
            yield [$taskName, $config, $beforeTasksClosures[$taskName] ?? null];
        }
    }

    private static function taskConfigsForSend()
    {
        return (new class extends TaskConfigs {
            public function taskConfigsForSend(): array
            {
                $configs = $this->findConfigByResource(Res::SEND)[Res::SEND];
                return array_filter($configs, static fn($config) => is_subclass_of($config['class'] ?? $config, BaseSendTask::class));
            }
        })->taskConfigsForSend();
    }

    private static function beforeTaskClosures(array $tasks): array
    {
        $beforeTasksClosures = [
            CohortDailyGgbBaseTask::class => fn(array &$config) => $config['fromTimeOffset'] = '-2 days',
            CrmOrTask::class => fn() => $this->haveServiceProviders(),
            RevenueDistributionTask::class => fn() => $this->haveBettingGames(),
            CrmTopPaymentsCountriesTask::class => fn($config) => $this->haveUserPaymentsFromCountry($config),
            UsersGamesCasinoWeeklyTask::class => fn() => $this->haveBettingGames(),
            'send-daily-games' => fn() => $this->haveUsersGameTokens(),
            'send-users-games-comparison' => fn() => $this->haveHhsGameSessions(),
            'send-stp-payments-month' => function () {
                $this->haveRates();
                $this->haveUserTransactionRecord([
                    'site_id' => Site::CV,
                    'updated_at' => DateHelper::yesterday(),
                ]);
            },
        ];

        self::taskClassInKeyToClassName($beforeTasksClosures, $tasks);

        return $beforeTasksClosures;
    }

    private static function taskClassInKeyToClassName(&$beforeTasksClosures, $tasks): void
    {
        foreach (array_diff_key($beforeTasksClosures, $tasks) as $taskClass => $taskClosure) {
            $found = false;
            foreach ($tasks as $taskName => $taskConfig) {
                $curClass = $taskConfig['class'] ?? $taskConfig;
                if ($curClass === $taskClass || is_subclass_of($curClass, $taskClass)) {
                    $beforeTasksClosures[$taskName] = $taskClosure;
                    $found = true;
                }
            }
            if (!$found) {
                Console::write(Console::format(self::class . " Invalid before tasks closure, please remove from beforeTaskClosures(): $taskClass", Console::BG_YELLOW, Console::FG_BLACK));
            }
            unset($beforeTasksClosures[$taskClass]);
        }
    }

    private function haveUserPaymentsFromCountry(array $taskConfig): void
    {
        $this->haveRates();
        $userRecord = $this->haveUserRecord([
            'site_id' => $taskConfig['siteId'],
            'country' => Country::UA,
        ]);
        $this->haveUserTransactionRecord([
            'site_id' => $userRecord->site_id,
            'user_id' => $userRecord->user_id,
            'op_id' => UserTransaction::OP_IN,
            'updated_at' => DateHelper::yesterday(),
        ]);
    }

    private function haveUsersGameTokens(): void
    {
        $this->haveUserGameTokenRecord([
            'site_id' => Site::CV,
            'last_action_at' => date('Y-m-d', strtotime('- 10 day')),
            'bet_amount_eur' => 101,
        ]);
    }

    private function haveHhsGameSessions(): void
    {
        $this->haveRecord(HhsUserGameSessions::class, [
            'site_id' => Site::CV,
            'date' => DateHelper::today(),
            'created_at' => DateHelper::today(),
            'updated_at' => date('Y-m-d', strtotime('- 10 day')),
            'session_id' => 1,
            'project_id' => 1,
            'is_aggregator' => false,
            'user_id_raw' => 1,
            'game_id' => 1,
            'tag_id' => 1,
            'currency' => Rate::EUR,
        ]);
    }

    private function haveBettingGames(): void
    {
        $this->haveRecord(Games::class, [
            'name' => Game::BETTING_NAME,
            'type' => Game::TYPE_BETTING
        ]);
    }

    private function haveServiceProviders(): void
    {
        array_map(
            fn($name, $id) => $this->haveRecord(ContactServiceProviders::class, ['id' => $id, 'title' => $name, 'type' => UserContact::TYPE_EMAIL]),
            [ContactServiceProvider::SP_MAIL_RU, ContactServiceProvider::SP_YANDEX, ContactServiceProvider::SP_GOOGLE],
            [1, 2, 3]
        );
    }
}
