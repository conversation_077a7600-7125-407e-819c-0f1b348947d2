<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\sync;

use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserSpecialInfo;
use app\back\modules\task\actions\sync\BaseSmenBlackListTask;
use app\back\modules\task\components\FetchTaskFactory;
use app\back\modules\task\requests\SmenRequest;
use app\back\repositories\UserSpecialInfos;
use app\tests\libs\CmdUnitTrait;
use app\tests\libs\ContainerUnitTrait;
use app\tests\libs\mock\FormBodyStream;
use app\tests\libs\mock\JsonBodyStream;
use app\tests\libs\mock\MockServer;
use app\tests\libs\mock\PostRequest;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use Nyholm\Psr7\Response;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(BaseSmenBlackListTask::class)]
class BonusBlackListTest extends TestCase
{
    use CmdUnitTrait;
    use ContainerUnitTrait;
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;

    private const array USER_STATUSES_FOR_BBL = [
        User::STATUS_PAID,
        User::STATUS_NORMAL,
        User::STATUS_PLAYED,
        User::STATUS_PRE_NORMAL,
    ];

    public function testAddCidUsersTask(): void
    {
        $u1 = $this->haveUser(['cid' => 1]);
        $this->haveUserSpecialInfo($u1, [
            'dep_lt_rub' => '10000',
            'wd_lt_rub' => '40000',
            'dep_last_at' => new \DateTimeImmutable('-1 day'),
        ]);
        $u2 = $this->haveUser(['cid' => $u1->cid]);
        $this->haveUserSpecialInfo($u2, [
            'wd_lt_rub' => '40000',
        ]);

        MockServer::with(function (MockServer $mock) use ($u1, $u2) {
            $mockRequest = $this->smenRequest(Res::CV, 'users-add-to-bonus-black-list', [
                'userIds' => [$u1->user_id, $u2->user_id],
                'auto' => 1,
                'loyaltyBonusEnabled' => 1,
            ]);
            $mock->expectRequest($mockRequest, $this->smenResponse());
            $this->runTask('sync-bonus-black-list-add-cid-users', Res::CV);
        });
    }

    public function testAddUsersTask(): void
    {
        $u1 = $this->haveUser();
        $this->haveUserSpecialInfo($u1, [
            'dep_lt_rub' => '10000',
            'wd_lt_rub' => '40000',
            'dep_last_at' => new \DateTimeImmutable('-1 day'),
        ]);
        $u2 = $this->haveUser();
        $this->haveUserSpecialInfo($u2, [
            'dep_lt_rub' => '10000',
            'wd_lt_rub' => '39999',
            'dep_last_at' => new \DateTimeImmutable('-1 day'),
        ]);

        MockServer::with(function (MockServer $mock) use ($u1) {
            $mockRequest = $this->smenRequest(Res::CV, 'users-add-to-bonus-black-list', [
                'userIds' => [$u1->user_id],
                'auto' => 1,
                'loyaltyBonusEnabled' => 1,
            ]);
            $mock->expectRequest($mockRequest, $this->smenResponse());
            $this->runTask('sync-bonus-black-list-add-users', Res::CV);
        });
    }

    public function testRemoveUsersTask(): void
    {
        $u1 = $this->haveUser(['bbl_status' => User::BONUS_BL_STATUS_YES_AUTO]);
        $this->haveUserSpecialInfo($u1, [
            'dep_lt_rub' => '10000',
            'wd_lt_rub' => '10000',
            'dep_last_at' => new \DateTimeImmutable('-1 day'),
        ]);
        $u2 = $this->haveUser(['bbl_status' => User::BONUS_BL_STATUS_YES_AUTO]);
        $this->haveUserSpecialInfo($u2, [
            'dep_lt_rub' => '10000',
            'wd_lt_rub' => '10001',
            'dep_last_at' => new \DateTimeImmutable('-1 day'),
        ]);

        MockServer::with(function (MockServer $mock) use ($u1) {
            $mockRequest = $this->smenRequest(Res::CV, 'users-remove-from-bonus-black-list', [
                'userIds' => [$u1->user_id],
                'auto' => 1,
            ]);
            $mock->expectRequest($mockRequest, $this->smenResponse());
            $this->runTask('sync-bonus-black-list-remove-users', Res::CV);
        });
    }

    private function haveUser(array $params = []): User
    {
        return $this->haveUserRecord([
            'site_id' => Site::CV,
            'status' => self::USER_STATUSES_FOR_BBL[array_rand(self::USER_STATUSES_FOR_BBL)],
            'bbl_status' => User::BONUS_BL_STATUS_NO,
            ...$params,
        ]);
    }

    protected function smenRequest(int|string $siteIdOrResource, string $taskName, array $params): PostRequest
    {
        $requestSettings = $this->container()
            ->get(FetchTaskFactory::class)
            ->createFetchTask($taskName, $siteIdOrResource)
            ->request;

        $sign = SmenRequest::urlAndSortedParamsHash($requestSettings['url'], $requestSettings['authKey'], $params);
        return new PostRequest("/{$requestSettings['url']}?signature=$sign", new FormBodyStream($params));
    }

    protected function smenResponse(array $data = ['success' => true]): Response
    {
        return new Response(200, [], new JsonBodyStream($data));
    }

    protected function haveUserSpecialInfo(User $user, array $props): UserSpecialInfo
    {
        $props['user_id'] = $user->user_id;
        $props['site_id'] = $user->site_id;
        $usi = $this->haveRecord(UserSpecialInfos::class, $props);
        self::assertInstanceOf(UserSpecialInfo::class, $usi);
        return $usi;
    }
}
