<?php

declare(strict_types=1);

namespace app\tests\functional\bonuses;

use app\back\components\helpers\UuidHelper;
use app\back\entities\BonusLog;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\UserWallet;
use app\back\modules\finance\components\bonus\BonusDictionaryGi;
use app\back\modules\finance\components\bonus\forms\BonusGinMoneyForm;
use app\back\modules\finance\components\bonus\WebBonusForm;
use app\back\modules\task\requests\GinRequest;
use app\back\repositories\UserWallets;
use app\tests\libs\mock\FormBodyStream;
use app\tests\libs\mock\JsonBodyStream;
use app\tests\libs\mock\MockServer;
use app\tests\libs\mock\ParametricUri;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use Nyholm\Psr7\Request;
use Nyholm\Psr7\Response;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;
use Psr\SimpleCache\CacheInterface;

#[
    CoversClass(GinRequest::class),
    CoversClass(BonusGinMoneyForm::class),
    CoversClass(WebBonusForm::class),
]
class BonusGinMoneyTest extends TestCase
{
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;

    public function testSendBonusPlayerCard(): void
    {
        $this->container()->get(CacheInterface::class)->clear();
        $siteId = Site::GGB;
        $userId = self::uniqRuntimeId();
        $currency = Rate::EUR;
        $employee = $this->haveEmployee();
        $this->haveRates();

        $user = $this->haveUserRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
        ]);

        $userWallet = $this->haveRecord(UserWallets::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'wallet_id' => self::uniqRuntimeId(),
            'type' => UserWallet::TYPE_REAL,
            'currency' => $currency,
        ]);

        $idempotenceId = UuidHelper::v5(UuidHelper::nil(), '1_bonus_test');
        MockServer::with(function () use ($user, $employee, $idempotenceId) {
            $form = $this->container()->get(WebBonusForm::class);
            self::consoleOutputToDebug(static function () use ($form, $user, $employee, $idempotenceId) {
                $bonusType = BonusDictionaryGi::GI_MONEY;
                $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
                $form->validateOrException([
                    'siteId' => $user->site_id,
                    'userId' => (string) $user->user_id,
                    'remoteBonusType' => $bonusType,
                ]);

                $form->bonusFormMode = WebBonusForm::BONUS_FORM_VALIDATE;
                $form->validateOrException([
                    'siteId' => $user->site_id,
                    'userId' => (string) $user->user_id,
                    'remoteBonusType' => $bonusType,
                    'values' => [
                        'userId' => (string) $user->user_id,
                        'sum' => '200.00',
                        'wager' => 2,
                        'timeToLive' => 'P5D',
                        'balanceGroups' => BonusDictionaryGi::BALANCE_GROUP_CASINO,
                        'adminComment' => '1_bonus_test',
                        'idempotenceId' => $idempotenceId,
                    ],
                ]);

                $form->bonusForm->send(BonusLog::TYPE_MANUAL_BONUS, $employee, null);
            });
        }, [
            [
                new Request('GET', new ParametricUri('/api/brands/4/bonus-list', ['signature' => '3180c8a55a57772ccae253e2c581f4f4', 'api_login' => 'analytics'])),
                new Response(200, [], new JsonBodyStream([
                    'balance-action.create_bonus_balance_analytics' => [
                        'schema' => [
                            'title' => 'legacy_analytics_action_request',
                            'type' => 'object',
                            'properties' => [
                                'balance_groups' => [
                                    'type' => 'array',
                                    'items' =>  [
                                        'enum' => [
                                            'live-casino',
                                            'instant-games',
                                            'betting',
                                            'casino'
                                        ],
                                        'enum_titles' => [
                                            "live-casino",
                                            "instant-games",
                                            "betting",
                                            "casino"
                                        ],
                                    ],
                                    'title' => 'balance_groups',
                                ],
                            ],
                        ],
                    ],
                ])),
            ],
            [
                new Request(
                    'POST',
                    new ParametricUri("/api/brands/4/players/{$user->user_id}/balance-action.create_bonus_balance_analytics", ['signature' => '031416b9ce6fbd1c97cd25ea23849e8e']),
                    [],
                    new FormBodyStream([
                        'api_login' => 'analytics',
                        'payload' => [
                            'amount' => '200.00',
                            'wager_multiplier' => 2,
                            'time_to_live' => 'P5D',
                            'balance_groups' => ['casino'],
                            'currency_code' => $userWallet->currency,
                            'meta_data' => [
                                'anal/reason' => '1_bonus_test',
                            ],
                        ],
                        'idempotence_id' => $idempotenceId,
                    ])
                ),
                new Response(200, [], new JsonBodyStream([
                    'success' => true,
                    'message' => null,
                    'error' => null,
                ])),
            ]
        ]);
    }
}
