<?php

declare(strict_types=1);

namespace app\tests\functional\services;

use app\back\components\Notifier;
use app\back\entities\Contact;
use app\back\repositories\Contacts;
use app\tests\libs\DbTransactionalUnitTrait;
use Nyholm\Psr7\Response;
use PHPUnit\Framework\TestCase;
use app\tests\libs\mock\FormBodyStream;
use app\tests\libs\mock\JsonBodyStream;
use app\tests\libs\mock\MockServer;
use app\tests\libs\mock\PostRequest;

class NotifyTest extends TestCase
{
    use DbTransactionalUnitTrait;

    public function testSms(): void
    {
        $contactName = 'sms_test';
        $phones = ['123456789', '0987654321'];
        $this->haveContact(Contact::TYPE_PHONE, $contactName, implode(', ', $phones));
        $notifier = $this->container()->get(Notifier::class);

        MockServer::with(static function (MockServer $mock) use ($contactName, $phones, $notifier) {
            $message = 'test message';
            $request = static fn(string $phone) => new PostRequest('/api', new FormBodyStream([
                'action' => 'sendsms',
                'project_id' => $_ENV['CRM_PROJECT'],
                'sign' => sha1('sendsms' . $_ENV['CRM_PROJECT'] . $_ENV['CRM_AUTH_KEY']),
                'phone' => $phone,
                'text' => $message
            ]));
            $response = new Response(200, [], new JsonBodyStream(['success' => true]));

            $mock->expectRequest($request($phones[0]), $response);
            $mock->expectRequest($request($phones[1]), $response);

            $messagesSent = $notifier->notify($contactName, 'test title', $message);
            self::assertSame(2, $messagesSent);
        });
    }

    public function testTelegram(): void
    {
        $tgChannelsContact = 'telegram_users_test';
        $tgUsersContact = 'telegram_channels_test';
        $users = [mt_rand(), mt_rand()];
        $channels = ['@channel1', '@channel2'];
        $this->haveContact(Contact::TYPE_TELEGRAM_CHANNEL, $tgChannelsContact, "$channels[0], $channels[1]");
        $this->haveContact(Contact::TYPE_TELEGRAM_USER, $tgUsersContact, "$users[0], $users[1]");
        $notifier = $this->container()->get(Notifier::class);

        MockServer::with(static function (MockServer $mock) use ($tgUsersContact, $tgChannelsContact, $users, $channels, $notifier) {
            $subject = 'test title';
            $message = 'test message';
            $request = static fn(string $type, array $recipients) => new PostRequest('/send', new JsonBodyStream([
                $type => $recipients,
                'text' => "$subject\n$message",
                'key' => $_ENV['TG_KEY'],
            ]));
            $response = new Response(200, [], new JsonBodyStream(['message_id' => mt_rand()]));

            $mock->expectRequest($request('channels', [$channels[0]]), $response);
            $mock->expectRequest($request('channels', [$channels[1]]), $response);
            $mock->expectRequest($request('chat_user_ids', [$users[0]]), $response);
            $mock->expectRequest($request('chat_user_ids', [$users[1]]), $response);

            $messagesSent = $notifier->notify($tgChannelsContact, $subject, $message);
            $messagesSent += $notifier->notify($tgUsersContact, $subject, $message);
            self::assertSame(4, $messagesSent);
        });
    }

    public function testElementChat(): void
    {
        $contactName = 'rc_test';
        $recipients = ['!first_room:domain.com', '!second_room:domain.com'];
        $this->haveContact(Contact::TYPE_ELEMENT_CHAT, $contactName, implode(',', $recipients));
        $notifier = $this->container()->get(Notifier::class);

        MockServer::with(static function (MockServer $mock) use ($contactName, $recipients, $notifier) {
            $subject = 'test title';
            $message = 'test message';
            $request = static fn(string $recipient) => new PostRequest(
                "/webhook/$recipient?formatter=slack",
                new JsonBodyStream([
                    'key' => $_ENV['ELEMENT_KEY'],
                    'text' => "$subject\n$message",
                ]),
                ['Content-Type' => 'application/json']
            );
            $response = new Response(200, [], new JsonBodyStream(['status' => 200]));

            $mock->expectRequest($request($recipients[0]), $response);
            $mock->expectRequest($request($recipients[1]), $response);

            $messagesSent = $notifier->notify($contactName, $subject, $message);
            self::assertSame(2, $messagesSent);
        });
    }

    private function haveContact(int $type, string $name, string $recipients): void
    {
        $this->haveRecord(Contacts::class, [
            'type' => $type,
            'name' => $name,
            'recipients' => $recipients
        ]);
    }
}
