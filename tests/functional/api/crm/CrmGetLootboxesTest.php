<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\LootboxPrize;
use app\back\entities\Rate;
use app\back\entities\UseragentPlatform;
use app\back\repositories\Lootboxes;
use app\back\repositories\LootboxPrizes;
use app\back\repositories\LootboxUserPrizeLogs;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;

class CrmGetLootboxesTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testUserLootboxesAllFilters(): void
    {
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();
        $prizeId = self::uniqRuntimeUuid();
        $lootboxId = self::uniqRuntimeUuid();
        $lootboxPrizeLogId1 = self::uniqRuntimeUuid();
        $lootboxPrizeLogId2 = self::uniqRuntimeUuid();

        $this->haveRecords(Lootboxes::class, [
            [
                'site_id' => $siteId,
                'lootbox_id' => $lootboxId,
                'title' => 'Абордаж',
                'enabled' => true,
            ],
        ]);

        $this->haveRecords(LootboxPrizes::class, [
            [
                'site_id' => $siteId,
                'prize_id' => $prizeId,
                'title' => 'х2 баллов на 2 часа',
                'type' => LootboxPrize::TYPE_X2,
                'prize_text' => '120 minutes',
            ],
        ]);

        $this->haveRecords(LootboxUserPrizeLogs::class, [
            [
                'site_id' => $siteId,
                'log_id' => $lootboxPrizeLogId1,
                'user_id' => $userId1,
                'lootbox_id' => $lootboxId,
                'prizes' => [$prizeId],
                'created_at' => '2023-12-20 23:01:30',
                'platform_group_id' => UseragentPlatform::GROUP_MOBILE,
                'amount' => 6000,
                'currency' => Rate::RUB,
                'multiplier' => 1.00
            ],
            [
                'site_id' => $siteId,
                'log_id' => $lootboxPrizeLogId2,
                'user_id' => $userId2,
                'lootbox_id' => $lootboxId,
                'prizes' => [$prizeId],
                'created_at' => '2023-12-20 23:01:30',
                'platform_group_id' => UseragentPlatform::GROUP_MOBILE,
                'amount' => 6000,
                'currency' => Rate::RUB,
                'multiplier' => 1.00
            ],
        ]);

        $this->sendAPI('crm', 'get/lootboxes', [
            'site_id' => ['=' => $siteId],
            'lootbox_id' => ['=' => $lootboxId],
            'type' => ['=' => LootboxPrize::TYPE_X2],
            'prize_text' => ['=' => '120 minutes'],
            'currency' => ['=' => Rate::RUB],
            'platform_group_id' => ['=' => UseragentPlatform::GROUP_MOBILE],
            'price' => ['=' => 6000],
            'created_at' => ['<=' => '2023-12-20 23:01:30'],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $userId1],
            ['user_id' => $userId2],
        ]);
    }
}
