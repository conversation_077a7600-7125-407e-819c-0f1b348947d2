<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\UserContact;
use app\back\modules\api\clients\crm\ContactForProcessingMethod;
use app\back\repositories\UserContacts;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;

/** @see ContactForProcessingMethod */
class CrmGetContactForProcessingTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testDomain(): void
    {
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();
        $domain1 = 'test.ua';
        $domain2 = 'google.com';

        $this->haveRecord(UserContacts::class, [
            'site_id' => $siteId,
            'user_id' => $userId1,
            'type' => UserContact::TYPE_EMAIL,
            'value' => "test@$domain1",
            'status' => UserContact::STATUS_NEW,
            'private' => false,
        ]);

        $this->haveRecord(UserContacts::class, [
            'site_id' => $siteId,
            'user_id' => $userId2,
            'type' => UserContact::TYPE_EMAIL,
            'status' => UserContact::STATUS_NEW,
            'value' => "test@$domain2",
            'private' => false,
        ]);

        $apiRequestArray = [
            'site_id' => ['=' => $siteId],
            'type' => ['=' => UserContact::TYPE_EMAIL],
            'domain' => ['=' => $domain1],
            'status' => ['=' => UserContact::STATUS_NEW],
        ];

        $this->sendAPI('crm', 'get/contact-for-processing', $apiRequestArray);
        $this->seeResponseIsCsv();
        $this->seeRowsInCsv([['site_id' => $siteId, 'user_id' => $userId1]]);
        $this->seeRowsNotInCsv([['site_id' => $siteId, 'user_id' => $userId2]]);
    }

    public function testDomainWithoutTypeEmailAndOtherStatus(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();
        $domain = 'test.ua';

        $this->haveRecord(UserContacts::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'type' => UserContact::TYPE_EMAIL,
            'value' => "test@$domain",
            'status' => UserContact::STATUS_NEW,
            'private' => false,
        ]);

        $this->sendAPI('crm', 'get/contact-for-processing', [
            'site_id' => ['=' => $siteId],
            'type' => ['=' => UserContact::TYPE_PHONE],
            'domain' => ['=' => $domain],
        ], 422);

        $this->sendAPI('crm', 'get/contact-for-processing', [
            'site_id' => ['=' => $siteId],
            'type' => ['=' => UserContact::TYPE_EMAIL],
            'status' => ['=' => UserContact::STATUS_ACTIVE],
            'domain' => ['=' => $domain],
        ]);
        $this->seeResponseEquals('');
    }
}
