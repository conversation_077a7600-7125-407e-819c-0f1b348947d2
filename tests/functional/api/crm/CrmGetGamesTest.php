<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\modules\api\clients\crm\filter\GamesMethod;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(GamesMethod::class)]
class CrmGetGamesTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testGetNumberGamesPlayed(): void
    {
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();

        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId1, 'game_id' => self::uniqRuntimeId()]);
        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId1, 'game_id' => self::uniqRuntimeId()]);
        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId2, 'game_id' => self::uniqRuntimeId()]);

        $this->sendAPI('crm', 'get/games', [
            'site_id' => ['=' => $siteId],
            'number_of_games_played' => ['>' => 1],
        ]);

        $this->seeResponseIsCsv();
        $this->seeRowsInCsv([['user_id' => $userId1]]);
        $this->seeRowsNotInCsv([['user_id' => $userId2]]);

        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId2, 'game_id' => self::uniqRuntimeId()]);

        $this->sendAPI('crm', 'get/games', [
            'site_id' => ['=' => $siteId],
            'number_of_games_played' => ['>' => 1],
        ]);
        $this->seeRowsInCsv([['user_id' => $userId1], ['user_id' => $userId2]]);
    }

    public function testGetPlayedAtTogetherNumberGamesPlayed(): void
    {
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();

        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId1, 'created_at' => new \DateTimeImmutable('2025-06-23 08:00:00'), 'game_id' => self::uniqRuntimeId()]);
        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId1, 'created_at' => new \DateTimeImmutable('2025-06-23 08:10:00'), 'game_id' => self::uniqRuntimeId()]);
        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId2, 'created_at' => new \DateTimeImmutable('2025-06-24 09:00:00'), 'game_id' => self::uniqRuntimeId()]);
        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId2, 'created_at' => new \DateTimeImmutable('2025-06-24 10:00:00'), 'game_id' => self::uniqRuntimeId()]);

        $this->sendAPI('crm', 'get/games', [
            'site_id' => ['=' => $siteId],
            'number_of_games_played' => ['>' => 1],
            'played_at' => ['>=' => '2025-06-23 07:00:00', '<' => '2025-06-24'],
        ]);

        $this->seeResponseIsCsv();
        $this->seeRowsInCsv([['user_id' => $userId1]]);
        $this->seeRowsNotInCsv([['user_id' => $userId2]]);

        $this->sendAPI('crm', 'get/games', [
            'site_id' => ['=' => $siteId],
            'number_of_games_played' => ['>' => 1],
            'played_at' => ['>=' => '2025-06-23 07:00:00', '<' => '2025-06-25'],
        ]);

        $this->seeRowsInCsv([['user_id' => $userId1], ['user_id' => $userId2]]);
    }
}
