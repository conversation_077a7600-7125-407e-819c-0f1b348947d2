<?php

declare(strict_types=1);

namespace app\tests\libs\mock;

use app\back\components\helpers\Arr;
use <PERSON><PERSON>holm\Psr7\Request;
use <PERSON><PERSON>holm\Psr7\Stream;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

class HttpServer
{
    public static function up(int $port, \Closure $onRequest, int $timeout = -1): void
    {
        $address = "tcp://0.0.0.0:$port";
        $socket = stream_socket_server($address, $errNo, $errStr);

        if (!$socket) {
            throw new \Error("Socket error: $errStr ($errNo)");
        }

        while ($conn = stream_socket_accept($socket, $timeout)) {
            stream_set_blocking($conn, false);
            $request = static::buildRequest($conn);
            /** @var ResponseInterface $response */
            $response = $onRequest($request);
            static::sendResponse($conn, $response);
        }
    }

    private static function sendResponse($conn, ResponseInterface $response): void
    {
        $headers = [];
        foreach ($response->getHeaders() as $header => $messages) {
            foreach ($messages as $message) {
                $headers[] = "$header: $message";
            }
        }

        $headers = implode("\r\n", [
            "HTTP/1.1 {$response->getStatusCode()} {$response->getReasonPhrase()}",
            ...$headers
        ]);
        fwrite($conn, "$headers\r\n\r\n{$response->getBody()}");
        if (!fclose($conn)) {
            throw new \RuntimeException('CLOSE');
        }
    }

    private static function buildRequest($conn): RequestInterface
    {
        $request = '';
        stream_set_blocking($conn, false);
        do {
            $request .= fread($conn, 1024);
        } while (false === ($headersEnd = strpos($request, "\r\n\r\n")));

        $headers = explode("\r\n", substr($request, 0, $headersEnd));
        // host header ignored
        [$requestLine] = array_splice($headers, 0, 2);
        [$method, $uri] = explode(' ', $requestLine);
        $headers = array_map(static fn($line) => explode(': ', $line, 2), $headers);
        $headers = array_map(static fn($h) => array_column($h, 1), Arr::groupBy($headers, [0]));

        $requestObject = new Request($method, $uri, $headers);

        $body = substr($request, $headersEnd + 4);
        $readLength = max(0, (int)($requestObject->getHeaderLine('Content-Length') ?? 0) - strlen($body));

        stream_set_blocking($conn, true);
        if ($readLength) {
            $body .= fread($conn, $readLength);
        }

        return $requestObject->withBody(Stream::create($body));
    }
}
