<?php

declare(strict_types=1);

namespace app\tests\libs\mock;

use <PERSON><PERSON>holm\Psr7\Uri;
use Psr\Http\Message\RequestInterface;

class ParametricUri extends Uri
{
    public function __construct(
        private readonly string $path,
        private array $params,
    ) {
        parse_str(http_build_query($params), $this->params); // To cast all params like in real url
        parent::__construct($path . '?' . http_build_query($params));
    }

    public function check(RequestInterface $receivedRequest): bool
    {
        $parsedParams = [];
        $receivedUri = $receivedRequest->getUri();

        if ($receivedUri->getPath() !== $this->path) {
            return false;
        }

        parse_str($receivedUri->getQuery(), $parsedParams);

        return $this->checkRecursive($this->params, $parsedParams);
    }

    private function checkRecursive(mixed $exp, mixed $act): bool
    {
        if (is_scalar($exp)) {
            if (is_scalar($act)) {
                return $exp === $act;
            }

            return false;
        }

        if ($exp instanceof RegexpUriParam) {
            if (!is_string($act)) {
                return false;
            }

            return $exp->test($act);
        }


        if (!is_array($act)) {
            return false;
        }

        if (array_is_list($act) && array_is_list($exp)) {
            foreach ($exp as $expV) {
                if (!in_array($expV, $act, true)) {
                    return false;
                }
            }
            return true;
        }

        foreach ($exp as $expP => $expV) {
            if ($this->checkRecursive($expV, $act[$expP]) === false) {
                return false;
            }
        }

        return true;
    }
}
