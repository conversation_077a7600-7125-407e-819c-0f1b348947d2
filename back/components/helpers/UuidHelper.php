<?php

declare(strict_types=1);

namespace app\back\components\helpers;

class UuidHelper
{
    /**
     * Generate v5 UUID
     *
     * Version 5 UUIDs are named based. They require a namespace (another
     * valid UUID) and a value (the name). Given the same namespace and
     * name, the output is always the same.
     */
    public static function v5(string $namespace, string|int $name): string
    {
        if (!self::isValid($namespace)) {
            throw new \InvalidArgumentException('Invalid namespace');
        }
        // Get hexadecimal components of namespace
        $nhex = str_replace(['-', '{', '}'], '', $namespace);
        // Binary Value
        $nstr = '';
        // Convert Namespace UUID to bits
        for ($i = 0, $iMax = \strlen($nhex); $i < $iMax; $i += 2) {
            $nstr .= \chr(hexdec($nhex[$i] . $nhex[$i + 1]));
        }
        // Calculate hash value
        $hash = sha1($nstr . $name);

        return sprintf(
            '%08s-%04s-%04x-%04x-%12s',
            // 32 bits for "time_low"
            substr($hash, 0, 8),
            // 16 bits for "time_mid"
            substr($hash, 8, 4),
            // 16 bits for "time_hi_and_version",
            // four most significant bits holds version number 5
            (hexdec(substr($hash, 12, 4)) & 0x0fff) | 0x5000,
            // 16 bits, 8 bits for "clk_seq_hi_res",
            // 8 bits for "clk_seq_low",
            // two most significant bits holds zero and one for variant DCE1.1
            (hexdec(substr($hash, 16, 4)) & 0x3fff) | 0x8000,
            // 48 bits for "node"
            substr($hash, 20, 12)
        );
    }

    public static function nil(): string
    {
        return '00000000-0000-0000-0000-000000000000';
    }

    public static function isValid(string $uuid): bool
    {
        return preg_match('#^[a-f\d]{8}(-[a-f\d]{4}){4}[a-f\d]{8}$#i', $uuid) === 1;
    }

    // 123 => 00000000-0000-0000-0000-000000000123
    public static function cast(int|string $value): string
    {
        $res = str_pad(str_replace('-', '', (string) $value), 32, '0', STR_PAD_LEFT);

        if ((is_numeric($value) && $value < 0) || $res === str_repeat('0', 32) || !preg_match('#^[a-f\d]{32}$#', $res)) {
            throw new \InvalidArgumentException("Invalid hex value '$value'");
        }

        $parts = str_split($res, 4);
        return "{$parts[0]}{$parts[1]}-$parts[2]-$parts[3]-$parts[4]-{$parts[5]}{$parts[6]}{$parts[7]}";
    }
}
