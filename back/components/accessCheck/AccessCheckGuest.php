<?php

declare(strict_types=1);

namespace app\back\components\accessCheck;

use app\back\components\BaseAuthAccess;
use app\back\components\BaseController;
use app\back\components\Container;
use app\back\components\exceptions\NotFoundException;
use app\back\components\exceptions\UnauthorizedException;
use app\back\components\Request;

#[\Attribute(\Attribute::TARGET_METHOD | \Attribute::TARGET_CLASS)]
class AccessCheckGuest implements AccessCheckInterface
{
    public function check(Container $container, BaseController $controller, Request $request): bool
    {
        try {
            $auth = $container->get(BaseAuthAccess::class);
            $auth->employee();
            return false;
        } catch (UnauthorizedException | NotFoundException) {
            return true;
        }
    }
}
