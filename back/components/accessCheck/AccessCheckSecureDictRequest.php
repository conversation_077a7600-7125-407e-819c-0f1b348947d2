<?php

declare(strict_types=1);

namespace app\back\components\accessCheck;

use app\back\components\BaseAuthAccess;
use app\back\components\BaseController;
use app\back\components\Container;
use app\back\components\helpers\Arr;
use app\back\components\Request;
use app\back\repositories\PermissionSecuredDict;

#[\Attribute(\Attribute::IS_REPEATABLE | \Attribute::TARGET_METHOD | \Attribute::TARGET_CLASS)]
class AccessCheckSecureDictRequest implements AccessCheckInterface
{
    public function __construct(
        private readonly string $secureDictClass,
        private readonly string $requestPath
    ) {
    }

    public function check(Container $container, BaseController $controller, Request $request): bool
    {
        $path = explode('.', $this->requestPath);
        $method = array_shift($path);
        $data = match ($method) {
            'GET' => $request->query->all(),
            'POST' => $request->request->all(),
            'JSON' => $request->json(),
            'ROUTE' => $request->routeParams(),
        };

        $id = Arr::getValueByPath($data, implode('.', $path));
        if ($id === null) {
            return false;
        }

        $secureDict = $container->get($this->secureDictClass);

        if (!$secureDict instanceof PermissionSecuredDict) {
            throw new \InvalidArgumentException('Invalid SecureDict class');
        }

        return $container
            ->get(BaseAuthAccess::class)
            ->can($secureDict->permissionPrefix() . $id);
    }
}
