<?php

declare(strict_types=1);

namespace app\back\components;

use Monolog\Logger;

class Console
{
    // foreground color control codes
    public const int FG_BLACK = 30;
    public const int FG_RED = 31;
    public const int FG_GREEN = 32;
    public const int FG_YELLOW = 33;
    public const int FG_BLUE = 34;
    public const int FG_PURPLE = 35;
    public const int FG_CYAN = 36;
    public const int FG_GREY = 37;
    // background color control codes
    public const int BG_BLACK = 40;
    public const int BG_RED = 41;
    public const int BG_GREEN = 42;
    public const int BG_YELLOW = 43;
    public const int BG_BLUE = 44;
    public const int BG_PURPLE = 45;
    public const int BG_CYAN = 46;
    public const int BG_GREY = 47;
    // fonts style control codes
    public const int RESET = 0;
    public const int NORMAL = 0;
    public const int BOLD = 1;
    public const int ITALIC = 3;
    public const int UNDERLINE = 4;
    public const int BLINK = 5;
    public const int NEGATIVE = 7;
    public const int CONCEALED = 8;
    public const int CROSSED_OUT = 9;
    public const int FRAMED = 51;
    public const int ENCIRCLED = 52;
    public const int OVERLINED = 53;

    public const string CURSOR_UP = "\033[1F";
    public const string CLEAR_LINE = "\033[2K";

    // https://man.openbsd.org/sysexits
    /** The command completed successfully. */
    public const int EXIT_CODE_OK = 0;
    /** The command exited with an error code that says nothing about the error. */
    public const int EXIT_CODE_UNSPECIFIED_ERROR = 1;
    /** The command was used incorrectly, e.g., with the wrong number of arguments, a bad flag, a bad syntax in a parameter, or whatever. */
    public const int EXIT_CODE_USAGE = 64;
    /** The input data was incorrect in some way. This should only be used for user's data and not system files. */
    public const int EXIT_CODE_DATAERR = 65;
    /** An input file (not a system file) did not exist or was not readable. This could also include errors like ``No message'' to a mailer (if it cared to catch it). */
    public const int EXIT_CODE_NOINPUT = 66;
    /** The user specified did not exist. This might be used for mail addresses or remote logins. */
    public const int EXIT_CODE_NOUSER = 67;
    /** The host specified did not exist. This is used in mail addresses or network requests. */
    public const int EXIT_CODE_NOHOST = 68;
    /** A service is unavailable. This can occur if a support program or file does not exist. This can also be used as a catchall message when something you wanted to do does not work, but you do not know why. */
    public const int EXIT_CODE_UNAVAILABLE = 69;
    /** An internal software error has been detected. This should be limited to non-operating system related errors as possible. */
    public const int EXIT_CODE_SOFTWARE = 70;
    /** An operating system error has been detected. This is intended to be used for such things as ``cannot fork'', ``cannot create pipe'', or the like. It includes things like getuid returning a user that does not exist in the passwd file. */
    public const int EXIT_CODE_OSERR = 71;
    /** Some system file (e.g., /etc/passwd, /var/run/utx.active, etc.) does not exist, cannot be opened, or has some sort of error (e.g., syntax error). */
    public const int EXIT_CODE_OSFILE = 72;
    /** A (user specified) output file cannot be created. */
    public const int EXIT_CODE_CANTCREAT = 73;
    /** An error occurred while doing I/O on some file. */
    public const int EXIT_CODE_IOERR = 74;
    /** Temporary failure, indicating something that is not really an error. In sendmail, this means that a mailer (e.g.) could not create a connection, and the request should be reattempted later. */
    public const int EXIT_CODE_TEMPFAIL = 75;
    /** The remote system returned something that was ``not possible'' during a protocol exchange. */
    public const int EXIT_CODE_PROTOCOL = 76;
    /** You did not have sufficient permission to perform the operation. This is not intended for file system problems, which should use NOINPUT or CANTCREAT, but rather for higher level permissions. */
    public const int EXIT_CODE_NOPERM = 77;
    /** Something was found in an unconfigured or misconfigured state. */
    public const int EXIT_CODE_CONFIG = 78;
    /** phpunit adds this code on to exit code ob tests */
    public const int PHPUNIT_SIGNAL_MODIFY_CODE = 128;

    private static array $writers = [];

    public static function inline(string $text): void
    {
        static::write($text, lineBreak: '');
    }

    public static function out(string $text, string $lineBreak = "\n"): void
    {
        static::write($text, lineBreak: $lineBreak);
    }

    public static function nl(): void
    {
        static::write('');
    }

    public static function info(string $text, string $lineBreak = "\n"): void
    {
        $prefix = self::format('INFO', static::BG_CYAN, static::FG_BLACK);
        static::write($prefix . '  ' . $text, lineBreak: $lineBreak);
    }

    public static function warning(string $text, string $lineBreak = "\n"): void
    {
        $prefix = self::format('WARN', static::BG_YELLOW, static::FG_BLACK);
        static::write($prefix . '  ' . $text, lineBreak: $lineBreak);
    }

    public static function error(string $text, string $lineBreak = "\n"): void
    {
        $prefix = self::format('ERROR', static::BG_RED);
        static::write($prefix . ' ' . $text, \STDERR, $lineBreak);
    }

    public static function table(array $data): void
    {
        ConsoleTable::dump($data);
    }

    public static function confirm(string $text): bool
    {
        do {
            static::info($text . ' (Y\n): ', '');
            $l = mb_strtolower(static::readLine());
        } while (!in_array($l, ['y', 'n'], true));

        return $l === 'y';
    }

    private static function readLine(mixed $stream = \STDIN): string
    {
        return trim(fgets($stream, 1024) ?: '');
    }

    public static function write(string $msg, mixed $stream = \STDOUT, string $lineBreak = "\n"): void
    {
        if (empty(self::$writers)) {
            fwrite($stream, $msg . $lineBreak);
            return;
        }
        $restore = self::$writers;
        $writer = array_pop(self::$writers);
        try {
            ($writer)($msg);
        } finally {
            self::$writers = $restore;
        }
    }

    public static function rewrite(string $msg, mixed $stream = \STDOUT, string $lineBreak = "\n", int $lines = 1): void
    {
        $msg = str_repeat(self::CURSOR_UP . self::CLEAR_LINE, $lines) . $msg;
        self::write($msg, $stream, $lineBreak);
    }

    public static function format(string $msg, int ...$codes): string
    {
        return self::formatStart(...$codes) . $msg . self::formatReset();
    }

    public static function formatStart(int ...$codes): string
    {
        return "\033[" . implode(';', $codes) . 'm';
    }

    public static function formatReset(): string
    {
        return "\033[0m";
    }

    public static function withWriter(\Closure $writer, \Closure $do): mixed
    {
        $restore = self::$writers;
        try {
            self::$writers[] = $writer;
            return $do();
        } finally {
            self::$writers = $restore;
        }
    }

    public static function ansiToHtml(string $string): string
    {
        $classMap = [
            static::FG_BLUE => 'text-blue',
            static::FG_CYAN => 'text-teal',
            static::FG_GREEN => 'text-green',
            static::FG_GREY => 'text-gray',
            static::FG_PURPLE => 'text-purple',
            static::FG_RED => 'text-red',
            static::FG_YELLOW => 'text-yellow',

            static::BG_BLUE => 'bg-blue',
            static::BG_CYAN => 'bg-teal',
            static::BG_GREEN => 'bg-green',
            static::BG_GREY => 'bg-gray',
            static::BG_PURPLE => 'bg-purple',
            static::BG_RED => 'bg-red',
            static::BG_YELLOW => 'bg-yellow',
        ];

        $tags = 0;
        $result = preg_replace_callback(
            '/\033\[([\d;]+)m/',
            static function ($ansi) use (&$tags, $classMap) {
                $classes = [];
                $reset = false;
                foreach (explode(';', $ansi[1]) as $controlCode) {
                    if ($controlCode === (string)static::RESET) {
                        $classes = [];
                        $reset = true;
                    } elseif (isset($classMap[$controlCode])) {
                        $classes[] = $classMap[$controlCode];
                    }
                }

                $return = '';
                while ($reset && $tags > 0) {
                    $return .= '</span>';
                    $tags--;
                }

                if (empty($classes)) {
                    return $return;
                }

                $classes = implode(' ', $classes);
                $tags++;
                return "$return<span class=\"$classes\">";
            },
            $string
        );

        while ($tags > 0) {
            $result .= '</span>';
            $tags--;
        }

        return $result;
    }

    public static function callWithStats(string $message, callable $callback): void
    {
        self::info($message . ': ', '');
        $startTime = microtime(true);
        $affectedRows = self::format((string) $callback(), static::BG_YELLOW, static::FG_BLACK);
        $endTime = microtime(true);
        $timeTaken = self::format((string) round($endTime - $startTime, 3), static::BG_YELLOW, static::FG_BLACK);
        self::write("$affectedRows rows in $timeTaken s");
    }

    public static function taskOutputFormat(\DateTimeInterface $date, string $level, string $messageText): string
    {
        $dateStr = $date->format('H:i:s');

        return "$dateStr $level $messageText";
    }

    public static function logLevelColor(int $level): int
    {
        return match ($level) {
            Logger::EMERGENCY,
            Logger::CRITICAL,
            Logger::ALERT,
            Logger::ERROR =>    self::BG_RED,
            Logger::WARNING =>  self::BG_YELLOW,
            Logger::NOTICE,
            Logger::INFO =>     self::BG_CYAN,
            default =>          self::BG_GREY,
        };
    }
}
