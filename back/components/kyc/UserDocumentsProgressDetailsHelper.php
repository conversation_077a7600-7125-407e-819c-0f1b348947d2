<?php

declare(strict_types=1);

namespace app\back\components\kyc;

use app\back\components\helpers\Json;
use app\back\components\SecondaryConnection;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;

class UserDocumentsProgressDetailsHelper
{
    use UserDocumentsProgressDetailsRequestHelper;
    use UserDocumentsProgressDetailsWaitHelper;

    public function __construct(
        private readonly SecondaryConnection $db,
    ) {
    }

    public function detailsColumnExpression(string $progressTableAlias, string ...$detailsColumns): string
    {
        $progressTableAlias = $this->db->getQuoter()->quoteTableName($progressTableAlias);
        $columns = implode(', ', array_map(fn($c) => $this->db->getQuoter()->quoteColumnName($c), $detailsColumns)) ?: "$progressTableAlias.details";
        return 'jsonb_build_object(' .
                   "'action', $progressTableAlias.action, " .
                   "'kyc_status', $progressTableAlias.kyc_status, " .
                   "'data', coalesce($columns)" .
               ')';
    }

    public function detailsDecorate(?string $details): array
    {
        $details = Json::decode($details ?? '{}');
        if (!isset($details['action'], $details['kyc_status'], $details['data'])) {
            return [];
        }
        return match ($details['action']) {
            UserDocumentProgress::ACTION_STATUS_CHANGE => $this->decorateKycStatusDetails($details['kyc_status'], $details['data']),
            UserDocumentProgress::ACTION_DOC_UPLOAD,
            UserDocumentProgress::ACTION_DOC_RESTORE,
            UserDocumentProgress::ACTION_DOC_DELETE => $this->detailsDecorateFileActions($details['data']),
        };
    }

    private function decorateKycStatusDetails(int $kycStatus, array $details): array
    {
        if (empty($details)) {
            return [];
        }

        return match ($kycStatus) {
            UserKyc::KYC_WAIT,
            UserKyc::KYC_WAIT_WITHOUT_REQUEST => $this->detailsDecorateDocsWait($details),
            UserKyc::KYC_REQUEST,
            UserKyc::KYC_REQUEST_REPEAT=> $this->detailsDecorateDocsRequest($details),
            UserKyc::KYC_FAILED => $this->detailsDecorateFailedComment($details),
            default => [],
        };
    }

    private function detailsDecorateFileActions(array $detailsData): array
    {
        $detailsViewList = [];
        foreach ($detailsData as $values) {
            foreach ($values as $name => $value) {
                $detailsViewList[] = "$name: " . match ($name) {
                    UserDocumentProgress::DOC_DETAIL_FIELD_TAGS => implode(', ', $value),
                    UserDocumentProgress::DOC_DETAIL_FIELD_COMMENT,
                    UserDocumentProgress::DOC_DETAIL_FIELD_ID,
                    UserDocumentProgress::DOC_DETAIL_FIELD_DELETED_AT,
                    UserDocumentProgress::DOC_DETAIL_FIELD_CREATED_AT => $value,
                    default => null,
                };
            }
        }

        return $detailsViewList;
    }

    private function detailsDecorateFailedComment(array $detailsData): array
    {
        return array_column($detailsData, 'comment');
    }

    public static function kycDetailsDecorateForApi(int $kycStatus, ?string $detailsJson): ?array
    {
        if (empty($detailsJson)) {
            return null;
        }

        if (!in_array($kycStatus, [UserKyc::KYC_REQUEST, UserKyc::KYC_REQUEST_REPEAT], true)) {
            return null;
        }

        $result = [];
        foreach (Json::decode($detailsJson) as $d) {
            $type = $d[UserDocumentProgress::DOC_DETAIL_FIELD_TYPE];
            $reasons = $d[UserDocumentProgress::DOC_DETAIL_FIELD_REASONS] ?? [UserDocumentProgress::REASON_OTHER];
            $row = [
                UserDocumentProgress::DOC_DETAIL_FIELD_TYPE => UserDocumentProgress::TYPES_API_NAMES[$type],
                UserDocumentProgress::DOC_DETAIL_FIELD_REASONS => array_map(static fn(int $r) => UserDocumentProgress::REASONS_API_NAMES[$r], $reasons),
                UserDocumentProgress::DOC_DETAIL_FIELD_API_FILES_LIMIT => UserDocumentProgress::FILES_LIMIT_BY_TYPE[$type] ?? UserDocumentProgress::FILES_LIMIT_DEFAULT,
            ];
            if (array_key_exists(UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID, $d)) {
                $row[UserDocumentProgress::DOC_DETAIL_FIELD_API_TRANSACTION_ID] = $d[UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID];
            }
            $result[] = $row;
        }

        return $result;
    }
}
