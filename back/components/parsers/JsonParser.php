<?php

declare(strict_types=1);

namespace app\back\components\parsers;

class JsonParser implements Parser
{
    public function parse(string $response): iterable
    {
        try {
            $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

            if (!is_array($result)) {
                throw new \RuntimeException("Not an array parsed");
            }

            return $result;
        } catch (\Throwable $e) {
            throw new \RuntimeException('JSON decode error: ' . $e->getMessage() . ' .  Response begin: ' . mb_substr($response, 0, 5000));
        }
    }
}
