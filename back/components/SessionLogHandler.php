<?php

declare(strict_types=1);

namespace app\back\components;

use Monolog\Logger;

class SessionLogHandler extends BaseLogHandler
{
    public function __construct(
        private readonly SessionMessages $sessionMessages,
        int $level = Logger::WARNING,
        bool $bubble = true
    ) {
        parent::__construct(APP_DEBUG ? Logger::DEBUG : $level, $bubble);
    }

    public function write(array $record): void
    {
        $message = $record['message'];
        match (true) {
            $record['level'] < Logger::WARNING => $this->sessionMessages->info($message),
            $record['level'] === Logger::WARNING => $this->sessionMessages->warning($message),
            $record['level'] > Logger::WARNING => $this->sessionMessages->error($message),
            default => $this->sessionMessages->success($message),
        };
    }
}
