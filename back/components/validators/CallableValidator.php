<?php

declare(strict_types=1);

namespace app\back\components\validators;

#[\Attribute(\Attribute::IS_REPEATABLE | \Attribute::TARGET_PROPERTY)]
class CallableValidator extends BaseValidator
{
    public const bool VALIDATE_ON_NULL = true;

    private \Closure $closure;
    private array $args;

    public function __construct(callable $callback, ...$args)
    {
        $this->closure = $callback(...);
        $this->args = $args;
    }

    public function validate(mixed $value, $form, array $context): ?string
    {
        return ($this->closure)($value, $form, $context, ...$this->args);
    }
}
