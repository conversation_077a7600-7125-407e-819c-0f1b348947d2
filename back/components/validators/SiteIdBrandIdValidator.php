<?php

declare(strict_types=1);

namespace app\back\components\validators;

#[\Attribute]
class SiteIdBrandIdValidator extends MatchValidator
{
    private const string PATTERN = "#^(?<site_id>[1-9]\d*)(?:-(?<brand_id>[1-9]\d*))?$#i";

    public function __construct()
    {
        parent::__construct(self::PATTERN);
    }

    protected function invalidMessage(): string
    {
        return 'is not SiteId-BrandId or SiteId';
    }

    public static function asCondition(null|string|array $value, string $siteIdColumn = 'site_id', $brandIdColumn = 'brand_id'): array|null
    {
        if ($value === null) {
            return [$siteIdColumn => null];
        }

        $values = (array)$value;

        $sites = [];
        $sitesBrands = [];
        foreach ($values as $v) {
            if (!preg_match(self::PATTERN, (string)$v, $matches)) {
                throw new \UnexpectedValueException("Unable to parse already validated siteIdBrandId: $v");
            }

            if (empty($matches['brand_id'])) {
                $sites[] = (int)$matches['site_id'];
            } else {
                $sitesBrands[] = [$siteIdColumn => (int)$matches['site_id'], $brandIdColumn => (int)$matches['brand_id']];
            }
        }

        $condition = [
            'OR',
        ];

        if (!empty($sites)) {
            $condition[] = [$siteIdColumn => $sites];
        }

        if (!empty($sitesBrands)) {
            $condition[] = ['IN', [$siteIdColumn, $brandIdColumn], $sitesBrands];
        }

        if (count($condition) === 1) {
            throw new \UnexpectedValueException("No filters left after SiteIdBrandId composing");
        }

        if (count($condition) === 2) {
            return $condition[1]; // Single conditions without OR wrapper
        }

        return $condition;
    }
}
