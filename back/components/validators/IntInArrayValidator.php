<?php

declare(strict_types=1);

namespace app\back\components\validators;

#[\Attribute]
class IntInArrayValidator extends BaseInArrayValidator
{
    public function __construct(
        mixed $haystackOrCallback,
        bool $useValues = false,
        private readonly bool $emptyStringToNull = true,
    ) {
        parent::__construct($haystackOrCallback, $useValues);
    }

    public function prepare(mixed $value, $form, array $context): mixed
    {
        if ($this->emptyStringToNull && $value === '') {
            return null;
        }

        return $value;
    }

    public function cast(mixed $value, $form): int
    {
        return (int) $value;
    }
}
