<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\helpers\UuidHelper;
use app\back\components\validators\BaseValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\NestedValidator;
use app\back\components\validators\StringLimitedValidator;
use app\back\components\validators\StringValidator;
use app\back\components\validators\UuidValidator;

class Tournament extends BaseEntity
{
    public const int STATUS_NEW = 1;
    public const int STATUS_COMING = 2;
    public const int STATUS_IN_PROCESS = 3;
    public const int STATUS_COMPLETED = 4;

    public const array STATUSES = [
        self::STATUS_NEW => 'new',
        self::STATUS_COMING => 'coming',
        self::STATUS_IN_PROCESS => 'in process',
        self::STATUS_COMPLETED => 'completed',
    ];

    public const int TYPE_SLOT = 1;
    public const int TYPE_BETTING = 2;

    public const array TYPES = [
        self::TYPE_SLOT => 'slot',
        self::TYPE_BETTING => 'betting',
    ];

    #[IdValidator]
    public int $site_id;
    #[NestedValidator([self::class, 'tournamentIdValidator'])]
    #[StringValidator(1, 36)]
    public string $tournament_id;
    #[StringLimitedValidator(100)]
    public ?string $title;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $started_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $finished_at;
    #[IntInArrayValidator(self::STATUSES)]
    public int $status;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $upserted_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[IntInArrayValidator(self::TYPES)]
    public ?int $type;

    public static function tournamentIdValidator(mixed $value): BaseValidator
    {
        return (UuidHelper::isValid((string)$value)) ? (new UuidValidator()) : (new IdValidator());
    }
}
