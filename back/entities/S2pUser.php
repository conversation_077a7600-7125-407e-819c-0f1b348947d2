<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;

class S2pUser extends BaseEntity
{
    public const int RANK_FREE = 1;
    public const int RANK_PAID = 2;
    public const int RANK_PLAYED = 3;
    public const int RANK_NEW_VIP = 4;
    public const int RANK_PRE_VIP = 5;
    public const int RANK_PRE_NORMAL = 6;
    public const int RANK_NORMAL = 7;
    public const int RANK_VIP = 8;
    public const int RANK_SUPER_VIP = 9;
    public const int RANK_ULTRA_VIP = 10;

    public const array RANK_NAMES = [
        self::RANK_FREE => 'Free',
        self::RANK_PAID => 'Paid',
        self::RANK_PLAYED => 'Played',
        self::RANK_NEW_VIP => 'New-Vip',
        self::RANK_PRE_VIP => 'Pre-Vip',
        self::RANK_PRE_NORMAL => 'Pre-Normal',
        self::RANK_NORMAL => 'Normal',
        self::RANK_VIP => 'Vip',
        self::RANK_SUPER_VIP => 'Super-vip',
        self::RANK_ULTRA_VIP => 'Ultra-vip',
    ];

    public const array RANKS_SCORES = [
        self::RANK_FREE => 1,
        self::RANK_PAID => 10,
        self::RANK_PLAYED => 40,
        self::RANK_NEW_VIP => 1000,
        self::RANK_PRE_VIP => 10000,
        self::RANK_PRE_NORMAL => 70,
        self::RANK_NORMAL => 100,
        self::RANK_VIP => 100000,
        self::RANK_SUPER_VIP => 1000000,
        self::RANK_ULTRA_VIP => 10000000,
    ];

    #[IdValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $dep_first_at;
    #[IntValidator]
    public ?int $rank;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $rank_updated_at;
}
