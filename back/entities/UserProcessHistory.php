<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringValidator;
use Yiisoft\Db\Expression\Expression;

class UserProcessHistory extends BaseEntity
{
    public const int CT_NO_CONTACT = 0;
    public const int CT_LETTER = 1;
    public const int CT_TRY_CALL = 2;
    public const int CT_CALL = 3;
    public const int CT_CHAT = 4;
    public const int CT_SMS = 5;
    public const int CT_OFFLINE_PUSH = 6;
    public const int CT_SOCIAL_MEDIA_CALL = 7;
    public const int CT_SOCIAL_MEDIA_MESSAGE = 8;
    public const int CT_TRY_LETTER = 9;
    public const int CT_TRY_CHAT = 10;
    public const int CT_ACTIVE_OFFER = 11;
    public const int CT_ACTIVE_DP = 12;
    public const int CT_ACTIVE_CASH_BACK = 13;
    public const int CT_NO_CALL = 14;
    public const int CT_NO_WRITE = 15;
    public const int CT_UNBLOCK_NO_CALL = 16;
    public const int CT_UNBLOCK_NO_WRITE = 17;
    public const int CT_BAN = 18;
    public const int CT_UN_BAN = 19;
    public const int CT_OFFLINE_MESSAGE = 20;
    public const int CT_CHAT_SHORT_COMMENT = 21;
    public const int CT_TRY_CALL_LOST_BASKET = 22;
    public const int CT_CALL_LOST_BASKET = 23;
    public const int CT_TRY_CALL_RETENTION_LIST = 24;
    public const int CT_CALL_RETENTION_LIST = 25;
    public const int CT_NO_WRITE_TO_CONTACTS = 26;
    public const int CT_UNBLOCK_NO_WRITE_TO_CONTACTS = 27;
    public const int CT_INCOMING_CALL = 28;
    public const int CT_OFFLINE_REQUEST = 29;
    public const int CT_LETTER_OPENED = 30;
    public const int CT_INTERNAL_MESSAGE = 31;
    public const int CT_TICKET = 32;
    public const int CT_REDIRECTED_CALL = 33;
    public const int CT_OFFLINE_REQUEST_DELETED = 34;
    public const int CT_MISSED_CALL = 35;
    public const int CT_SOCIABLE = 36;
    public const int CT_DEFERRED_TG_MESSAGE = 37;
    public const int CT_DEFERRED_TG_CHAT = 38;
    public const int CT_OUT_SOCIAL_MEDIA_MESSAGE = 39;
    public const int CT_OUT_SOCIAL_MEDIA_CALL = 40;
    public const int CT_DEFERRED_MESSAGE_TG_DELETED = 41;

    public const array CONNECT_TYPES = [
        self::CT_NO_CONTACT => 'No contact',

        self::CT_LETTER => 'Email',
        self::CT_TRY_LETTER => 'Email (try)',
        self::CT_NO_WRITE_TO_CONTACTS => 'No write to email',
        self::CT_UNBLOCK_NO_WRITE_TO_CONTACTS => 'Unblock no write to email',

        self::CT_CALL => 'Call',
        self::CT_TRY_CALL => 'Call (try)',
        self::CT_UNBLOCK_NO_CALL => 'Unblock NoCall',
        self::CT_NO_CALL => 'No Call',

        self::CT_CALL_LOST_BASKET => 'Call lost basket',
        self::CT_TRY_CALL_LOST_BASKET => 'Call lost basket (try)',

        self::CT_CALL_RETENTION_LIST => 'Call retention list',
        self::CT_TRY_CALL_RETENTION_LIST => 'Call retention list (try)',

        self::CT_CHAT => 'Chat',
        self::CT_TRY_CHAT => 'Chat (try)',
        self::CT_CHAT_SHORT_COMMENT => 'Short chat comment',
        self::CT_NO_WRITE => 'No Write',
        self::CT_UNBLOCK_NO_WRITE => 'Unblock NoWrite',

        self::CT_SMS => 'SMS',
        self::CT_OFFLINE_PUSH => 'Offline push',
        self::CT_OFFLINE_MESSAGE => 'Offline message',
        self::CT_SOCIAL_MEDIA_CALL => 'Social media call',
        self::CT_SOCIAL_MEDIA_MESSAGE => 'Social media message',

        self::CT_ACTIVE_OFFER => 'Active Sale offer',
        self::CT_ACTIVE_DP => 'Active Sale DP',
        self::CT_ACTIVE_CASH_BACK => 'Active Sale cash back',

        self::CT_BAN => 'Ban user',
        self::CT_UN_BAN => 'UnBan User',

        self::CT_INCOMING_CALL => 'Incoming call',
        self::CT_INTERNAL_MESSAGE => 'Internal message',
        self::CT_TICKET => 'Ticket',
        self::CT_REDIRECTED_CALL => 'Redirected call',
        self::CT_OFFLINE_REQUEST_DELETED => 'Offline request deleted',

        self::CT_MISSED_CALL => 'Missed call',
        self::CT_SOCIABLE => 'PM first contact',
        self::CT_DEFERRED_TG_MESSAGE => 'Deferred Telegram message',
        self::CT_DEFERRED_TG_CHAT => 'Deferred Telegram chat',
        self::CT_OUT_SOCIAL_MEDIA_MESSAGE => 'Outgoing social media message',
        self::CT_OUT_SOCIAL_MEDIA_CALL => 'Outgoing social media call',
        self::CT_DEFERRED_MESSAGE_TG_DELETED => 'Deferred Telegram message deleted',
    ];

    public const int REASON_DEFAULT = 1;
    public const int REASON_MULTI_ACCOUNT = 2;

    public const array REASONS = [
        self::REASON_DEFAULT => 'Обычный',
        self::REASON_MULTI_ACCOUNT => 'Звонок по мультиаккаунту'
    ];

    public const string TYPE_GROUP_SMS = 'SMS';
    public const string TYPE_GROUP_EMAIL = 'Email';
    public const string TYPE_GROUP_CALL = 'Call';
    public const string TYPE_GROUP_CHAT = 'Chat';
    public const string TYPE_GROUP_OTHER = 'Other';
    public const string TYPE_GROUP_INTERNAL = 'Internal';
    public const string TYPE_GROUP_ACTIVE_OFFER = 'Active offer';

    public const array TYPE_GROUPS = [
        self::TYPE_GROUP_EMAIL => [
            self::CT_LETTER,
            self::CT_TRY_LETTER,
            self::CT_LETTER_OPENED,
        ],
        self::TYPE_GROUP_SMS => [
            self::CT_SMS,
        ],
        self::TYPE_GROUP_CALL => [
            self::CT_TRY_CALL,
            self::CT_CALL,
            self::CT_INCOMING_CALL,
            self::CT_MISSED_CALL,
            self::CT_SOCIAL_MEDIA_CALL,
            self::CT_TRY_CALL_LOST_BASKET,
            self::CT_CALL_LOST_BASKET,
            self::CT_TRY_CALL_RETENTION_LIST,
            self::CT_CALL_RETENTION_LIST,
            self::CT_REDIRECTED_CALL,
            self::CT_OUT_SOCIAL_MEDIA_CALL,
        ],
        self::TYPE_GROUP_CHAT => [
            self::CT_TRY_CHAT,
            self::CT_CHAT,
            self::CT_OFFLINE_PUSH,
            self::CT_CHAT_SHORT_COMMENT,
            self::CT_OFFLINE_MESSAGE,
            self::CT_OFFLINE_REQUEST,
            self::CT_OFFLINE_REQUEST_DELETED,
            self::CT_SOCIAL_MEDIA_MESSAGE,
            self::CT_DEFERRED_TG_MESSAGE,
            self::CT_DEFERRED_TG_CHAT,
            self::CT_OUT_SOCIAL_MEDIA_MESSAGE,
            self::CT_DEFERRED_MESSAGE_TG_DELETED,
        ],
        self::TYPE_GROUP_INTERNAL => [
            self::CT_INTERNAL_MESSAGE,
        ],
        self::TYPE_GROUP_ACTIVE_OFFER => [
            self::CT_ACTIVE_OFFER,
            self::CT_ACTIVE_DP,
            self::CT_ACTIVE_CASH_BACK,
        ],
        self::TYPE_GROUP_OTHER => [
            self::CT_NO_CONTACT,
            self::CT_NO_CALL,
            self::CT_NO_WRITE,
            self::CT_UNBLOCK_NO_CALL,
            self::CT_UNBLOCK_NO_WRITE,
            self::CT_BAN,
            self::CT_UN_BAN,
            self::CT_NO_WRITE_TO_CONTACTS,
            self::CT_UNBLOCK_NO_WRITE_TO_CONTACTS,
            self::CT_TICKET,
            self::CT_SOCIABLE,
        ]
    ];

    #[IdValidator]
    public int $id;
    #[IdValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[IntValidator]
    public ?int $operator_id;
    #[IntValidator]
    public int $type_id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $created;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $updated;
    #[IntInArrayValidator(self::REASONS)]
    public ?int $reason_id;
    #[IntValidator]
    public ?int $item_id;
    #[StringValidator(0, 10000)]
    public ?string $comment;

    public static function getGroupById(int $id): string
    {
        foreach (self::TYPE_GROUPS as $type => $group) {
            if (in_array($id, $group, true)) {
                return $type;
            }
        }

        throw new \InvalidArgumentException("Unknown group type.");
    }

    public static function getTypeById(int $id): string
    {
        return self::CONNECT_TYPES[$id] ?? (string)$id;
    }

    public static function getContactsGroupsExpression(): Expression
    {
        $conditions = '(CASE ';

        foreach (self::TYPE_GROUPS as $group => $events) {
            if ($group === self::TYPE_GROUP_OTHER) {
                $conditions .= " ELSE '$group' ";
                break;
            }
            $conditions .= 'WHEN type_id IN (' . implode(',', $events) . ') THEN \'' . $group . '\' ';
        }

        $conditions .= "END)";

        return new Expression($conditions);
    }

    public static function getTypeGroup(string $type): array
    {
        if (isset(self::TYPE_GROUPS[$type])) {
            return self::TYPE_GROUPS[$type];
        }

        throw new \InvalidArgumentException("Unknown group type name '{$type}'.");
    }
}
