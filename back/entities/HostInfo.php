<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\CurrencyFormatValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\DateValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\SafeValidator;
use app\back\components\validators\StringValidator;

class HostInfo extends BaseEntity
{
    public const int DEP_WP = 1;
    public const int DEP_HUFFSON = 2;
    public const int DEP_CRM = 3;
    public const int DEP_S2P = 4;
    public const int DEP_SEO = 5;
    public const int DEP_SMEN = 6;
    public const int DEP_GI = 7;
    public const int DEP_GGB = 8;
    /** @deprecated  */
    public const int DEP_ME = 9;

    public const int LEG_WHITE = 1;
    public const int LEG_GREY = 2;
    public const int LEG_BLACK = 3;

    public const int VALIDITY_UNKNOWN = 1;
    public const int VALIDITY_VALID = 2;
    public const int VALIDITY_INVALID = 3;

    public const int STATUS_UNKNOWN = 1;
    public const int STATUS_ACTIVE = 2;
    public const int STATUS_INACTIVE = 3;

    public const int WHOIS_UNKNOWN = 1;
    public const int WHOIS_VISIBLE = 2;
    public const int WHOIS_HIDDEN = 3;

    public const array DEPARTMENTS = [
        self::DEP_WP => 'Welcome partners',
        self::DEP_HUFFSON => 'Huffson',
        self::DEP_CRM => 'CRM',
        self::DEP_S2P => 'S2P',
        self::DEP_SEO => 'SEO',
        self::DEP_SMEN => 'SMEN',
        self::DEP_GI => 'Game Inspire',
        self::DEP_GGB => 'GGbet',
        // self::DEP_ME => 'MaxEnt',
    ];

    public const array LEGALITY = [
        self::LEG_WHITE => 'White',
        self::LEG_GREY => 'Grey',
        self::LEG_BLACK => 'Black',
    ];

    public const array VALIDITY = [
        self::VALIDITY_UNKNOWN => 'Unknown',
        self::VALIDITY_VALID => 'Valid',
        self::VALIDITY_INVALID => 'Invalid',
    ];

    public const array STATUSES = [
        self::STATUS_UNKNOWN => 'Unknown',
        self::STATUS_ACTIVE => 'Active',
        self::STATUS_INACTIVE => 'Inactive',
    ];

    public const array WHOIS = [
        self::WHOIS_UNKNOWN => 'Unknown',
        self::WHOIS_VISIBLE => 'Visible',
        self::WHOIS_HIDDEN => 'Hidden',
    ];

    #[IdValidator]
    public int $host_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[IntValidator]
    public ?int $legality;
    #[IntValidator]
    public ?int $pnl;
    #[IntValidator]
    public ?int $department;
    #[StringValidator(1, 100)]
    public ?string $purpose;
    #[StringValidator(1, 50)]
    public ?string $account_id;
    #[StringValidator(1, 1000)]
    public ?string $comment;
    #[StringValidator(1, 1000)]
    public ?string $external_comment;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $external_created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $external_updated_at;
    #[DateValidator]
    public ?string $registered_at;
    #[DateValidator]
    public ?string $expired_at;
    #[MoneyValidator]
    public ?string $renewal_price;
    #[CurrencyFormatValidator]
    public ?string $renewal_currency;
    #[SafeValidator]
    public ?array $log = null;
    #[IntInArrayValidator(self::VALIDITY)]
    public int $validity = self::VALIDITY_UNKNOWN;
    #[IntInArrayValidator(self::STATUSES)]
    public int $status = self::STATUS_UNKNOWN;
    #[IntInArrayValidator(self::WHOIS)]
    public int $whois = self::WHOIS_UNKNOWN;

    public static function getDepartmentNameById(?int $id): ?string
    {
        return self::DEPARTMENTS[$id] ?? null;
    }

    public static function getLegalityNameById(?int $id): ?string
    {
        return self::LEGALITY[$id] ?? null;
    }

    public static function getValidityNameById(?int $id): ?string
    {
        return self::VALIDITY[$id] ?? null;
    }

    public static function getStatusNameById(?int $id): ?string
    {
        return self::STATUSES[$id] ?? null;
    }

    public static function getWhoisNameById(?int $id): ?string
    {
        return self::WHOIS[$id] ?? null;
    }

    public static function getDepartmentIdByName(?string $name): ?int
    {
        $id = array_search($name, self::DEPARTMENTS, true);

        return $id === false ? null : $id;
    }

    public static function getLegalityIdByName(?string $name): ?int
    {
        $id = array_search($name, self::LEGALITY, true);

        return $id === false ? null : $id;
    }

    public static function getValidityIdByName(?string $name): ?int
    {
        $id = array_search($name, self::VALIDITY, true);

        return $id === false ? null : $id;
    }

    public static function getStatusIdByName(?string $name): ?int
    {
        $id = array_search($name, self::STATUSES, true);

        return $id === false ? null : $id;
    }
}
