<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;

class UserStatusNormalThreshold extends BaseEntity
{
    #[StringValidator(2, 2)]
    public string $country;
    #[MoneyValidator]
    public string $ngr_amount_active;
    #[MoneyValidator]
    public string $ngr_amount_high;
    #[IntValidator(1, 100)]
    public int $ngr_cloud_percent;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[IdValidator]
    public ?int $updated_by;
}
