<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringValidator;

class UseragentPlatform extends BaseEntity
{
    public const int GROUP_MOBILE = 1;
    public const int GROUP_DESKTOP = 2;
    public const int GROUP_OTHER = 3;

    public const array GROUPS = [
        self::GROUP_MOBILE => 'Mobile',
        self::GROUP_DESKTOP => 'Desktop',
        self::GROUP_OTHER => 'Other',
    ];

    public const string PLATFORM_ANDROID = 'Android';
    public const string PLATFORM_IOS = 'iOS';
    public const string PLATFORM_OTHER = 'Other';

    #[IdValidator]
    public int $id;
    #[StringValidator]
    public string $name;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[IntInArrayValidator(self::GROUPS)]
    public int $group = self::GROUP_OTHER;

    public static function getGroupNameById(?int $id): string
    {
        return static::GROUPS[$id] ?? self::GROUPS[self::GROUP_OTHER];
    }
}
