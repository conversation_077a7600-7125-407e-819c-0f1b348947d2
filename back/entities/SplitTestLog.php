<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringValidator;

class SplitTestLog extends BaseEntity
{
    #[IdValidator]
    public int $site_id;
    #[StringValidator(1, 32)]
    public string $log_id;
    #[StringValidator(1, 32)]
    public ?string $identity_key;
    #[StringValidator(1, 100)]
    public ?string $split_slug;
    #[StringValidator(1, 100)]
    public ?string $split_group_slug;
    #[StringValidator(1, 100)]
    public ?string $event_slug;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[StringValidator(1, 500)]
    public ?string $extra_data;
    #[BigIdValidator]
    public ?int $user_id;
    #[IntValidator]
    public ?int $refcode_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $user_updated_at;
}
