<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;

class S2pUserPaySystem extends BaseEntity
{
    #[IdValidator]
    public int $project_id;
    #[IdValidator]
    public ?int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[IdValidator]
    public int $pay_sys_id;
    #[BooleanValidator]
    public bool $allowed;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
}
