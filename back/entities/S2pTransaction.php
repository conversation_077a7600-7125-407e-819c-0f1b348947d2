<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\CurrencyFormatValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;

class S2pTransaction extends BaseEntity
{
    public const int STATUS_NEW = 1;
    public const int STATUS_PROCESS = 2;
    public const int STATUS_SUCCESS = 3;
    public const int STATUS_FAIL = 4;
    public const int STATUS_MANUAL = 5;
    public const int STATUS_HANDLE = 6;
    public const int STATUS_SKIP = 7;

    public const int TYPE_BILL = 1;
    public const int TYPE_REBILL = 2;
    public const int TYPE_WITHDRAW = 3;
    public const int TYPE_ENROLLMENT = 4;
    public const int TYPE_PURCHASE = 5;
    public const int TYPE_CHARGEBACK = 6;
    public const int TYPE_REFUND = 7;
    public const int TYPE_CAPTURE = 8;
    public const int TYPE_AUTHORIZE = 9;
    public const int TYPE_CANCEL = 10;
    public const int TYPE_CHARGEBACK_REVERSAL = 11;
    public const int TYPE_REVERSAL = 12;

    public const int DIR_IN = 1;
    public const int DIR_OUT = 2;

    public const string COL_CREATED_AT = 'created_at';
    public const string COL_UPDATED_AT = 'updated_at';
    public const string COL_SUCCESS_AT = 'success_at';

    public static function tableName(): string
    {
        return 's2p_transactions';
    }

    public const array STATUSES = [
        self::STATUS_NEW => 'New',
        self::STATUS_PROCESS => 'Process',
        self::STATUS_SUCCESS => 'Success',
        self::STATUS_FAIL => 'Fail',
        self::STATUS_MANUAL => 'Manual',
        self::STATUS_HANDLE => 'Handle',
        self::STATUS_SKIP => 'Skip',
    ];

    public const array TYPES = [
        self::TYPE_BILL => 'Bill',
        self::TYPE_REBILL => 'Rebill',
        self::TYPE_WITHDRAW => 'Withdraw',
        self::TYPE_ENROLLMENT => 'Enrollment',
        self::TYPE_PURCHASE => 'Purchase',
        self::TYPE_CHARGEBACK => 'Chargeback',
        self::TYPE_REFUND => 'Refund',
        self::TYPE_CAPTURE => 'Capture',
        self::TYPE_AUTHORIZE => 'Authorize',
        self::TYPE_CANCEL => 'Cancel',
        self::TYPE_CHARGEBACK_REVERSAL => 'Chargeback Reversal',
        self::TYPE_REVERSAL => 'Reversal',
    ];

    public const array DIRS = [
        self::DIR_IN => 'In',
        self::DIR_OUT => 'Out',
    ];

    #[StringValidator(1, 32)]
    public string $id;
    #[IdValidator]
    public int $project_id;
    #[IdValidator]
    public ?int $site_id;
    #[IntInArrayValidator(self::TYPES)]
    public ?int $type;
    #[IntInArrayValidator(self::DIRS)]
    public ?int $dir;
    #[IntInArrayValidator(self::STATUSES)]
    public ?int $status;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $success_at;
    #[MoneyValidator]
    public ?string $amount;
    #[CurrencyFormatValidator]
    public ?string $currency;
    #[MoneyValidator]
    public ?string $amount_usd;
    #[MoneyValidator]
    public ?string $amount_rub;
    #[MoneyValidator(6)]
    public ?string $rate;
    #[MoneyValidator]
    public ?string $tr_fee_amount;
    #[CurrencyFormatValidator]
    public ?string $tr_fee_currency;
    #[MoneyValidator]
    public ?string $tr_fee_amount_usd;
    #[MoneyValidator]
    public ?string $tr_fee_amount_rub;
    #[MoneyValidator(6)]
    public ?string $tr_fee_rate;
    #[MoneyValidator]
    public ?string $mid_fee_amount;
    #[CurrencyFormatValidator]
    public ?string $mid_fee_currency;
    #[MoneyValidator]
    public ?string $mid_fee_amount_usd;
    #[MoneyValidator]
    public ?string $mid_fee_amount_rub;
    #[MoneyValidator(6)]
    public ?string $mid_fee_rate;
    #[StringValidator(1, 32)]
    public ?string $order_id;
    #[StringValidator(1, 32)]
    public ?string $parent_id;
    #[IdValidator]
    public ?int $status_id;
    #[IdValidator]
    public ?int $pay_sys_id;
    #[IdValidator]
    public ?int $pay_class_id;
    #[MoneyValidator]
    public ?string $amount_eur;
    #[IntInArrayValidator(S2pOrder::INSTANCES)]
    public ?int $instance_id;

    public static function getStatusById(int $id): ?string
    {
        return static::STATUSES[$id] ?? null;
    }

    public static function getTypeById(?int $id): ?string
    {
        return static::TYPES[$id] ?? null;
    }

    public static function getDirById(int $id): ?string
    {
        return static::DIRS[$id] ?? null;
    }
}
