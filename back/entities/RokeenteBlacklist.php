<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\StringValidator;

class RokeenteBlacklist extends BaseEntity
{
    private const string BLACKLIST_PREFIX = 'blacklist_';

    #[IdValidator]
    public int $id;
    #[StringValidator(1, 255)]
    public string $name;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;

    public static function getBlacklistIdByAlias(string $alias): int
    {
        return (int) str_replace(self::BLACKLIST_PREFIX, '', $alias);
    }

    public static function isItBlacklistField(string $fieldAlias): bool
    {
        return str_starts_with($fieldAlias, self::BLACKLIST_PREFIX);
    }
}
