<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\CallableValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\enums\VipaffAffSource;
use app\back\entities\enums\VipaffProgType;

class VipaffRefcode extends BaseEntity
{
    #[IdValidator]
    public int $refcode_id;
    #[CallableValidator([self::class, 'validateProgType'])] // TODO: make specific enum validator with check inside
    #[StringInArrayValidator([VipaffProgType::class, 'values'], useValues: true)]
    public string $prog_type;
    #[CallableValidator([self::class, 'validateAffSource'])] // TODO: make specific enum validator with check inside
    #[StringInArrayValidator([VipaffAffSource::class, 'values'], useValues: true)]
    public ?string $aff_source;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $updated_at;

    public static function validateProgType(?string $value): ?string
    {
        VipaffProgType::check($value);
        return null;
    }

    public static function validateAffSource(?string $value): ?string
    {
        VipaffAffSource::check($value);
        return null;
    }
}
