<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringLimitedValidator;
use app\back\components\validators\StringValidator;

class BettingEvent extends BaseEntity
{
    public const int STATUS_NOT_STARTED = 1;
    public const int STATUS_LIVE = 2;
    public const int STATUS_SUSPENDED = 3;
    public const int STATUS_ENDED = 4;
    public const int STATUS_ABANDONED = 5;
    public const int STATUS_CLOSED = 6;
    public const int STATUS_DELAYED = 7;
    public const int STATUS_CANCELLED = 8;
    public const int STATUS_UNKNOWN = 9;

    public const int TYPE_MATCH = 1;
    public const int TYPE_OUTRIGHT = 2;

    public const array STATUSES = [
        self::STATUS_NOT_STARTED => 'Not started',
        self::STATUS_SUSPENDED => 'Suspended',
        self::STATUS_ABANDONED => 'Abandoned',
        self::STATUS_LIVE => 'Live',
        self::STATUS_ENDED => 'Ended',
        self::STATUS_CLOSED => 'Closed',
        self::STATUS_DELAYED => 'Delayed',
        self::STATUS_CANCELLED => 'Cancelled',
        self::STATUS_UNKNOWN => 'Unknown',
    ];

    public const array TYPES = [
            self::TYPE_MATCH => 'Match',
            self::TYPE_OUTRIGHT => 'Outright',
    ];

    #[StringValidator(0, 50)]
    public string $id;
    #[IntInArrayValidator(self::TYPES)]
    public int $type;
    #[IntInArrayValidator(self::STATUSES)]
    public int $status;
    #[StringValidator(0, 200)]
    public ?string $tournament_id;
    #[IntValidator]
    public ?int $sport_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $start_at;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $updated_at;
    #[StringLimitedValidator(250)]
    public ?string $name;

    public static function getStatusNameById(?int $id): ?string
    {
        return self::STATUSES[$id] ?? null;
    }
}
