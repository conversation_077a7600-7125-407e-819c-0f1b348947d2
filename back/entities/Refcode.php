<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\helpers\Str;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\FilterValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringValidator;

class Refcode extends BaseEntity
{
    public const array WP_PREFIXES = [
        'ap',
        'cp',
        'ga',
        'gg',
        'vb',
        'vc',
        'vp',
        'vu',
        'wp',
        'ca',
        'va',
        'mk',
        'sc', // For toxic
        'sn',
        'tf',
        'cu',
        'sa', // 7aff <PERSON><PERSON>
        'sk',
        'at',
        'ba', // 7slots Basari
        'gu', // GGbet UA
        'ys', // 7slots Masal.Bet.
        'ab', // AbeBet.Partners https://abebet.partners/
        'ox', // on-x.casino Vdelux
        'ma', // smen maggico.casino
        'nv', // Nv.partners команди Nekit
        'bp', // Maggico Affiliates
        'sl', // SPAN
    ]; // Run ./cli task/run update-refcodes-webmaster task after adding new elements here

    public const array YS_AFF_PREFIXES = [
        'aff',
    ];
    public const array VIP_AFF_PREFIXES = [
        'vip',
        'CP',
    ];

    /* @deprecated */
    public const string SLOTTY_AFF_PREFIX = 'netref_';

    public const int CRM_CHANNEL_EMAIL = 1;
    public const int CRM_CHANNEL_WEB_PUSH = 2;
    public const int CRM_CHANNEL_APP_PUSH = 3;
    public const int CRM_CHANNEL_SMS = 4;
    public const int CRM_CHANNEL_INTERNAL = 5;
    public const int CRM_CHANNEL_TELEGRAM = 6;
    public const int CRM_CHANNEL_TELEGRAM_BOT = 7;
    public const int CRM_CHANNEL_VIBER = 8;
    public const int CRM_CHANNEL_VIBER_BOT = 9;
    public const int CRM_CHANNEL_VKONTAKTE = 10;
    public const int CRM_CHANNEL_VKONTAKTE_BOT = 11;
    public const int CRM_CHANNEL_WHATSAPP = 12;
    public const int CRM_CHANNEL_FACEBOOK = 13;
    public const int CRM_CHANNEL_FACEBOOK_BOT = 14;
    public const int CRM_CHANNEL_INSTAGRAM = 15;
    public const int CRM_CHANNEL_TWITTER = 16;
    public const int CRM_CHANNEL_ODNOKLASSNIKI = 17;
    public const int CRM_CHANNEL_SALEBOT_FACEBOOK = 18;
    public const int CRM_CHANNEL_SALEBOT_INSTAGRAM = 19;
    public const int CRM_CHANNEL_LINE_BOT = 20;
    public const int CRM_CHANNEL_DISCORD_BOT = 21;

    public const string CRM_CHANNELS_PREFIX = 'crm_';
    public const array CRM_CHANNELS = [
        self::CRM_CHANNEL_EMAIL => 'Email',
        self::CRM_CHANNEL_WEB_PUSH => 'Web push',
        self::CRM_CHANNEL_APP_PUSH => 'App push',
        self::CRM_CHANNEL_SMS => 'Sms',
        self::CRM_CHANNEL_INTERNAL => 'Internal',
        self::CRM_CHANNEL_TELEGRAM => 'Telegram',
        self::CRM_CHANNEL_TELEGRAM_BOT => 'Telegram bot',
        self::CRM_CHANNEL_TWITTER => 'Twitter',
        self::CRM_CHANNEL_VIBER => 'Viber',
        self::CRM_CHANNEL_VIBER_BOT => 'Viber bot',
        self::CRM_CHANNEL_VKONTAKTE => 'Vkontakte',
        self::CRM_CHANNEL_VKONTAKTE_BOT => 'Vkontakte bot',
        self::CRM_CHANNEL_WHATSAPP => 'Whatsapp',
        self::CRM_CHANNEL_FACEBOOK => 'Facebook',
        self::CRM_CHANNEL_FACEBOOK_BOT => 'Facebook bot',
        self::CRM_CHANNEL_INSTAGRAM => 'Instagram',
        self::CRM_CHANNEL_ODNOKLASSNIKI => 'Odnoklassniki',
        self::CRM_CHANNEL_SALEBOT_FACEBOOK => 'Salebot Facebook',
        self::CRM_CHANNEL_SALEBOT_INSTAGRAM => 'Salebot Instagram',
        self::CRM_CHANNEL_LINE_BOT => 'Linebot',
        self::CRM_CHANNEL_DISCORD_BOT => 'Discord',
    ];

    public const array CRM_CHANNELS_POST_PREFIXES = [
        self::CRM_CHANNEL_EMAIL => ['mail', 'api', 'action_mail'],
        self::CRM_CHANNEL_WEB_PUSH => ['webpush', 'action_webpush'],
        self::CRM_CHANNEL_APP_PUSH => ['apppush', 'action_apppush'],
        self::CRM_CHANNEL_SMS => ['sms'],
        self::CRM_CHANNEL_INTERNAL => ['int'],
        self::CRM_CHANNEL_TELEGRAM => ['tg', 'tgchannel'],
        self::CRM_CHANNEL_TELEGRAM_BOT => ['tgbot'],
        self::CRM_CHANNEL_TWITTER => ['twitter'],
        self::CRM_CHANNEL_VIBER => ['viber'],
        self::CRM_CHANNEL_VIBER_BOT => ['viberbot'],
        self::CRM_CHANNEL_VKONTAKTE => ['vk', 'vkgroup'],
        self::CRM_CHANNEL_VKONTAKTE_BOT => ['vkbot'],
        self::CRM_CHANNEL_WHATSAPP => ['wapp'],
        self::CRM_CHANNEL_FACEBOOK => ['fb'],
        self::CRM_CHANNEL_FACEBOOK_BOT => ['fbbot'],
        self::CRM_CHANNEL_INSTAGRAM => ['insta'],
        self::CRM_CHANNEL_ODNOKLASSNIKI => ['od'],
        self::CRM_CHANNEL_SALEBOT_FACEBOOK => ['salebotfacebook'],
        self::CRM_CHANNEL_SALEBOT_INSTAGRAM => ['salebotinstagram'],
        self::CRM_CHANNEL_LINE_BOT => ['linebot'],
        self::CRM_CHANNEL_DISCORD_BOT => ['discord'],
    ];

    public const array SEGMENTS = [
        'free',
        'paid1',
        'paid2',
        'paid3',
        'paid4',
        'toxic',
        'paid_low_0',
        'paid_low_1',
        'paid_low_2',
        'paid_low_3',
        'paid_1',
        'paid_2',
        'paid_3',
        'paid_4',
        'paid_5',
        'paid_6',
        'paid_7',
        'paid_8',
    ];

    public const string DEFAULT_SEGMENT = 'other';

    public int $id;
    #[FilterValidator([self::class, 'clean'])]
    public string $code;
    #[IntValidator(0, 2 ** 15 - 1)]
    public ?int $ts_id;
    #[IdValidator]
    public ?int $source_site_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[IntValidator(0, 2 ** 31 - 1)]
    public ?int $ref_type;
    #[StringValidator]
    public ?string $webmaster_id;

    public static function clean(?string $val): ?string
    {
        if ($val === null) {
            return null;
        }

        $val = trim(Str::cleanString($val));

        // Fix refcodes like mb_CYKR4_em1412?refCode=mb_CYKR4_em1412
        $parts = explode('?refCode=', trim($val));
        if (count($parts) >= 2 && $parts[0] === $parts[1]) {
            $val = $parts[0];
        }

        // wp_w11979p43_seo1000_vulcan_seoincubator_2409/?uri=/push-notification/mobile-application/save?x-app-token-id=drDvxnoSwa8%3AAPA91bGKjmu2ajV_03pbsjLXmQbeaatcBsHZ1-4OnjrQ2xCEd9FAtCR4X6Xhef-JwEvyl3SRX04nUYi7MexlNuEkpjXCAmHLAjP0HdLt7BY_80SBt8strOB6U7m8CzsLO
        if (str_contains($val, '/?uri')) {
            $val = mb_substr($val, 0, mb_strpos($val, '/?uri='));
        }

        // 654847_9C4F9C1352E34CADB6D29DE2F6FD8C5F -> netref_654847
        $val = preg_replace('#^(?:' . self::SLOTTY_AFF_PREFIX . ')?(\d{4,6}|0)_[A-Z\d]{32}$#i', self::SLOTTY_AFF_PREFIX . '$1', $val);

        if (mb_strlen($val, 'UTF-8') > 255) {
            $val = mb_substr($val, 0, 255, 'UTF-8');
        }

        return $val;
    }

    public static function crmChannelById(?int $id): string
    {
        return static::CRM_CHANNELS[$id] ?? 'Other';
    }
}
