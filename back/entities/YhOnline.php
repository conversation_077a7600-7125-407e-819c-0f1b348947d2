<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;

class YhOnline extends BaseEntity
{
    public const int USER_TYPE_USER = 1;
    public const int USER_TYPE_GUEST = 2;

    public const int PERIOD_HOUR = 1;
    public const int PERIOD_DAY = 2;
    public const int PERIOD_MONTH = 3;
    public const int PERIOD_YEAR = 4;

    public const array USER_TYPES = [
        self::USER_TYPE_USER => 'User',
        self::USER_TYPE_GUEST => 'Guest',
    ];

    public const array PERIODS = [
        self::PERIOD_HOUR => 'Hour',
        self::PERIOD_DAY => 'Day',
        self::PERIOD_MONTH => 'Month',
        self::PERIOD_YEAR => 'Year',
    ];

    #[IdValidator]
    public int $id;
    #[IdValidator]
    public int $site_id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $created_at;
    #[IntInArrayValidator(self::USER_TYPES)]
    public int $user_type;
    #[IntInArrayValidator(self::PERIODS)]
    public int $period_id;
    #[IntValidator]
    public int $count = 0;
}
