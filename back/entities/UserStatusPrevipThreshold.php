<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;

class UserStatusPrevipThreshold extends BaseEntity
{
    #[StringValidator(2, 2)]
    public string $country;
    #[MoneyValidator]
    public string $in_amount_initial;
    #[MoneyValidator]
    public string $in_amount_week;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[IdValidator]
    public ?int $updated_by;
}
