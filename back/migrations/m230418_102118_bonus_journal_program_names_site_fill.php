<?php

declare(strict_types=1);

namespace app\back\migrations;

use app\back\components\Console;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\BatchQueryResultInterface;
use Yiisoft\Db\Query\Query;

class m230418_102118_bonus_journal_program_names_site_fill
{
    private const string DATETIME_FORMAT = 'Y-m-d\TH:i:s';
    private const int BATCH_UPDATE_LIMIT = 1000;

    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    public function up(): void
    {
        [$from, $to] = $this->getFromTo();
        if (!isset($from, $to)) {
            return;
        }
        $interval = new \DateInterval('PT1H');
        $updatedCount = 0;

        $this->logUpdates($from, $to, $updatedCount);
        while ($from <= $to) {
            foreach ($this->batchSelectFromPeriod($from, $interval) as $rows) {
                $updatedData = $this->batchUpdateBonusJournal($rows);
                $updatedCount += $updatedPartCount = count($updatedData);
                $this->logUpdates($from, $to, $updatedCount, true);

                if ($updatedPartCount !== count($rows)) {
                    foreach ($this->diffBySiteJournalId($rows, $updatedData) as $notUpdated) {
                        $this->log("NOT UPDATED {$notUpdated['site_id']} {$notUpdated['journal_id']} {$notUpdated['name']}\n");
                    }
                }
            }
            $from = $from->add($interval);
        }

        $deletedCount = 0;
        $this->log("DELETE OLD NAMES: $deletedCount");
        do {
            $deletedCount += $deletedNow = $this->deleteNotUsedOldNames();
            $this->log("DELETE OLD NAMES: $deletedCount", true);
        } while ($deletedNow === self::BATCH_UPDATE_LIMIT);
    }

    public function down(): void
    {
        $query = (new Query($this->db))
            ->select(['sites' => 'json_object_agg(site_id, id)', 'name'])
            ->from('bonus_journal_program_names')
            ->where(['IS NOT', 'site_id', null])
            ->groupBy(['name'])
            ->orderBy(['name' => SORT_ASC])
            ->indexBy('name');

        foreach ($query->column() as $name => $sites) {
            $replaceWithId = $this->getProgramNameId($name);

            foreach (json_decode($sites, true, 512, JSON_THROW_ON_ERROR) as $siteId => $removeId) {
                $this->removeProgramNameCascade($name, $siteId, $removeId, $replaceWithId);
            }
        }
    }

    private function getFromTo(): ?array
    {
        ['from' => $from, 'to' => $to] = (new Query($this->db))
            ->select([
                'from' => 'MIN(created_at)',
                'to' => 'MAX(created_at)',
            ])
            ->from('bonus_journal')
            ->one();

        if (!isset($from, $to)) {
            return [null, null];
        }

        return [new \DateTimeImmutable($from), new \DateTimeImmutable($to)];
    }

    private function batchSelectFromPeriod(\DateTimeImmutable $dateTime, \DateInterval $interval): BatchQueryResultInterface
    {
        static $query;
        $query ??= (new Query($this->db))
            ->select([
                'bj.site_id',
                'bj.journal_id',
                'remove_id' => 'n.id',
                'n.name',
            ])
            ->from(['bj' => 'bonus_journal'])
            ->innerJoin(['n' => 'bonus_journal_program_names'], 'n.id = bj.program_name_id')
            ->where(['n.site_id' => null])
            ->andWhere("bj.created_at >= :date_from AND bj.created_at < :date_to");

        $query->addParams([
            ':date_from' => $dateTime->format('Y-m-d H:i:s'),
            ':date_to' => $dateTime->add($interval)->format('Y-m-d H:i:s'),
        ]);
        return $query->batch(self::BATCH_UPDATE_LIMIT);
    }

    private function diffBySiteJournalId(array $firstArray, array $secondArray): array
    {
        $secondArray = array_flip(array_map(static fn($r) => "$r[site_id]-$r[journal_id]", $secondArray));
        $rowKeys = array_flip(array_map(static fn($r) => "$r[site_id]-$r[journal_id]", $firstArray));
        $diff = [];
        foreach (array_diff_key($rowKeys, $secondArray) as $index) {
            $diff[] = $firstArray[$index];
        }
        return $diff;
    }

    private function batchUpdateBonusJournal(array $rows): array
    {
        static $quoter;
        $quoter ??= $this->db->getQuoter();

        $values = [];
        foreach ($rows as $row) {
            $siteId = (int)$row['site_id'];
            $values[] = "($siteId, {$quoter->quoteValue($row['journal_id'])}, {$this->getProgramNameId($row['name'], $row['site_id'])})";
        }
        $values = 'VALUES ' . implode(', ', $values);

        return $this->db->createCommand(<<<SQL
            UPDATE bonus_journal bj
            SET program_name_id=upd.program_name_id
            FROM ($values) AS upd(site_id, journal_id, program_name_id)
            WHERE bj.site_id = upd.site_id
              AND bj.journal_id = upd.journal_id
            RETURNING bj.site_id, bj.journal_id
        SQL)->queryAll();
    }

    private function deleteNotUsedOldNames(): int
    {
        static $selectToDelQuery;

        $selectToDelQuery ??= $this->db->createCommand()->delete('bonus_journal_program_names', [
            'id' => (new Query($this->db))
                ->select(['bn.id'])
                ->from(['bn' => 'bonus_journal_program_names'])
                ->leftJoin(['bj' => 'bonus_journal'], 'bn.id = bj.program_name_id')
                ->where(['bj.site_id' => null])
                ->limit(self::BATCH_UPDATE_LIMIT)
        ]);

        return $selectToDelQuery->execute();
    }

    private function log(string $msg, bool $rewrite = false): void
    {
        $msg = date('H:i:s') . ' ' . $msg;
        $rewrite ? Console::rewrite($msg) : Console::write($msg);
    }

    private function logUpdates(\DateTimeInterface $from, \DateTimeInterface $to, int $updated, bool $rewrite = false): void
    {
        $progress = $from->format(self::DATETIME_FORMAT) . '/' . $to->format(self::DATETIME_FORMAT);
        $this->log("   $progress    UPDATES: $updated", $rewrite);
    }

    private function removeProgramNameCascade(string $name, int $siteId, int $removeId, int $replaceWithId): void
    {
        $updatedPerName = 0;
        Console::write(sprintf("site:%-3s rows:%-7s %-14s %s", $siteId, '', "$removeId=>$replaceWithId", $name));
        do {
            $updatedPerName += $updated = $this->db->createCommand()
                ->update(
                    'bonus_journal',
                    ['program_name_id' => $replaceWithId],
                    ['IN', ['site_id', 'journal_id'], $this->bonusJournalByProgramQuery($removeId, $siteId)]
                )
                ->execute();
            Console::rewrite(sprintf("site:%-3s rows:%-7s %-14s %s", $siteId, $updatedPerName, "$removeId=>$replaceWithId", $name));
        } while ($updated === self::BATCH_UPDATE_LIMIT);

        $this->db->createCommand()
            ->delete('bonus_journal_program_names', ['id' => $removeId])
            ->execute();
    }

    private function getProgramNameId(string $name, ?int $siteId = null): int
    {
        static $names = [];
        $key = $siteId . '-' . $name;

        if (!array_key_exists($key, $names)) {
            $insertCmd = $this->db->createCommand()->insert('bonus_journal_program_names', [
                'name' => $name,
                'site_id' => $siteId,
            ]);

            $newId = $this->db->createCommand()
                ->setRawSql($insertCmd->getRawSql() . ' ON CONFLICT(name, site_id) DO NOTHING RETURNING id')
                ->queryScalar();

            $names[$key] = $newId ?: (new Query($this->db))
                ->select(['id'])
                ->from('bonus_journal_program_names')
                ->where(['name' => $name])
                ->andWhere(['site_id' => $siteId])
                ->scalar();
        }

        return $names[$key];
    }

    private function bonusJournalByProgramQuery(int $programNameId, int $siteId): Query
    {
        return (new Query($this->db))
            ->select(['site_id', 'journal_id'])
            ->from('bonus_journal')
            ->where([
                'site_id' => $siteId,
                'program_name_id' => $programNameId,
            ])
            ->limit(self::BATCH_UPDATE_LIMIT);
    }
}
