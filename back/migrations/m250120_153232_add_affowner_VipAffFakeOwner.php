<?php

declare(strict_types=1);

namespace app\back\migrations;

use app\back\entities\enums\WpAffOwner;

class m250120_153232_add_affowner_VipAffFakeOwner extends BaseMigration
{
    public function up(): void
    {
        $this->sql("ALTER TYPE wp_aff_owner ADD VALUE IF NOT EXISTS '" . WpAffOwner::VIPAFF_FAKE_OWNER . "'");
        $this->sql("INSERT INTO wp_aff_owner_groups (name, \"group\") VALUES ('" . WpAffOwner::VIPAFF_FAKE_OWNER . "', " . WpAffOwner::GROUP_VIPAFF . ")");
    }
}
