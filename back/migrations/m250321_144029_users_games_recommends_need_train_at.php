<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250321_144029_users_games_recommends_need_train_at extends BaseMigration
{
    public function safeUp(): void
    {
        $this->sql("ALTER TABLE users_games_recommends RENAME COLUMN need_updated_at TO need_train_at");
        $this->sql("ALTER TABLE users_games_recommends ADD COLUMN need_infer_at timestamp(0)");
    }

    public function safeDown(): void
    {
        $this->sql("ALTER TABLE users_games_recommends DROP COLUMN need_infer_at");
        $this->sql("ALTER TABLE users_games_recommends RENAME COLUMN need_train_at TO need_updated_at");
    }
}
