<?php

declare(strict_types=1);

namespace app\back\migrations;

use Yiisoft\Db\Connection\ConnectionInterface;

class m230418_102117_bonus_journal_program_names_site
{
    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    public function up(): void
    {
        $this->db->createCommand()->addColumn(
            'bonus_journal_program_names',
            'site_id',
            'integer',
        )->execute();

        $this->db->createCommand(
            'CREATE UNIQUE INDEX CONCURRENTLY bonus_journal_program_names_name_site_id ' .
            'ON bonus_journal_program_names(name, site_id)'
        )->execute();

        $this->db->createCommand()->dropIndex(
            'bonus_journal_program_names',
            'bonus_journal_program_names_name',
        )->execute();

        $this->db->createCommand()->addColumn(
            'bonus_journal_program_names',
            'created_at',
            'timestamp(0) not null default now()',
        )->execute();
    }

    public function down(): void
    {
        $this->db->createCommand(
            'CREATE UNIQUE INDEX CONCURRENTLY bonus_journal_program_names_name ' .
            'ON bonus_journal_program_names(name)'
        )->execute();

        $this->db->createCommand()->dropIndex(
            'bonus_journal_program_names',
            'bonus_journal_program_names_name_site_id',
        )->execute();

        $this->db->createCommand()->dropColumn(
            'bonus_journal_program_names',
            'site_id',
        )->execute();

        $this->db->createCommand()->dropColumn(
            'bonus_journal_program_names',
            'created_at',
        )->execute();
    }
}
