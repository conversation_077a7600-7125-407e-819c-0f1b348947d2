<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240702_134130_ads_credetials_table_convert_value_to_jsonb extends BaseMigration
{
    public function safeUp(): void
    {
        $this->db->createCommand("UPDATE ads_credentials SET value = jsonb_build_object('access_token', value) WHERE value NOT LIKE '{%}'")->execute();
        $this->db->createCommand("ALTER TABLE ads_credentials ALTER COLUMN value TYPE jsonb USING value::jsonb")->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand("ALTER TABLE ads_credentials ALTER COLUMN value TYPE text USING value::text")->execute();
    }
}
