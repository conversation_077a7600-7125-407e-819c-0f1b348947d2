<?php

declare(strict_types=1);

namespace app\back\migrations;

class m230706_121541_archive_tables extends BaseMigration
{
    public function safeUp(): void
    {
        $tables = [
            'bonus_log_user',
            'old_users_hash',
            'logs_old',
            'checks_log_s2p_orders',
            'users_activity',
            'users_landing',
            'users_stats_fake'
        ];

        foreach ($tables as $table) {
            $this->sql("COMMENT ON TABLE $table IS 'archive'");
        }
    }
}
