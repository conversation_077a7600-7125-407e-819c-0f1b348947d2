<?php

declare(strict_types=1);

namespace app\back\migrations;

use Yiisoft\Db\Connection\ConnectionInterface;

class m230525_121929_auth_phone_confirmation_requests
{
    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    public function up(): void
    {
        $tr = $this->db->beginTransaction();

        //Renaming of DB table
        $this->db->createCommand()->renameTable('users_security_requests', 'phone_confirmation_requests')->execute();

        //Changing permissions items names for users and roles because of renaming report and path to it
        //Item name change for USER rights
        $this->db->createCommand()->update(
            'auth_assignment',
            ['item_name' => '/reports/phone-confirmation-requests'],
            ['item_name' => '/reports/security-requests']
        )->execute();

        //Item name change for ROLE rights
        $this->db->createCommand()->update(
            'auth_item_child',
            ['child' => '/reports/phone-confirmation-requests'],
            ['child' => '/reports/security-requests']
        )->execute();

        $tr->commit();
    }

    public function down(): void
    {
        $tr = $this->db->beginTransaction();

        $this->db->createCommand()->renameTable('phone_confirmation_requests', 'users_security_requests')->execute();

        $this->db->createCommand()->update(
            'auth_assignment',
            ['item_name' => '/reports/security-requests'],
            ['item_name' => '/reports/phone-confirmation-requests']
        )->execute();

        $this->db->createCommand()->update(
            'auth_item_child',
            ['child' => '/reports/security-requests'],
            ['child' => '/reports/phone-confirmation-requests']
        )->execute();

        $tr->commit();
    }
}
