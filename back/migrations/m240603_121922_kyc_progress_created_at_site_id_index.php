<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240603_121922_kyc_progress_created_at_site_id_index extends BaseMigration
{
    public function up(): void
    {
        $this->db->createCommand()
            ->createIndex('users_documents_kyc_progress', 'users_documents_kyc_progress_created_at_site_id', ['created_at', 'site_id'])
            ->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()
            ->dropIndex('users_documents_kyc_progress', 'users_documents_kyc_progress_created_at_site_id')
            ->execute();
    }
}
