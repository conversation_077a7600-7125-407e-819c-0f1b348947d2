<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250129_000002_users_documents_eyes_and_face_slave_columns extends BaseMigration
{
    private const string TABLE = 'users_documents_faces';

    public function up(): void
    {
        $this->db->createCommand()->addColumn(self::TABLE, 'eyes', 'int[]')->execute();
        $this->db->createCommand()->addColumn(self::TABLE, 'inherited_face', 'boolean')->execute();
        $this->db->createCommand()->addColumn(self::TABLE, 'inherited_similarity', 'boolean')->execute();
        $this->createIndexConcurrentlyWhereTrue('inherited_face');
    }

    public function down(): void
    {
        $this->db->createCommand()->dropColumn(self::TABLE, 'eyes')->execute();
        $this->db->createCommand()->dropColumn(self::TABLE, 'inherited_face')->execute();
        $this->db->createCommand()->dropColumn(self::TABLE, 'inherited_similarity')->execute();
    }

    private function createIndexConcurrentlyWhereTrue(string $column): void
    {
        $table = self::TABLE;
        $this->sql("CREATE INDEX CONCURRENTLY {$table}_{$column} ON {$table}({$column}) WHERE ({$column} is true)");
    }
}
