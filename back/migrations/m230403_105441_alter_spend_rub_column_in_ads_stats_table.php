<?php

declare(strict_types=1);

namespace app\back\migrations;

use Yiisoft\Db\Connection\ConnectionInterface;

class m230403_105441_alter_spend_rub_column_in_ads_stats_table
{
    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    public function up(): void
    {
        $this->db->createCommand('ALTER TABLE ads_stats ALTER COLUMN spend_rub TYPE NUMERIC(20,10)')->execute();
    }

    public function down(): void
    {
        $this->db->createCommand('ALTER TABLE ads_stats ALTER COLUMN spend_rub TYPE NUMERIC(20,2)')->execute();
    }
}
