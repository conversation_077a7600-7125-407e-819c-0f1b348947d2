<?php

declare(strict_types=1);

namespace app\back\migrations;

class m230830_151109_new_site_brucebet extends BaseMigration
{
    public function up(): void
    {
        $this->db->createCommand()->insert('sites', [
            'site_id' => 80,
            'site_name' => 'BruceBet',
            'short_name' => 'BB',
            'group' => 'YS',
            'domain' => 'bruce.bet',
        ])->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()->delete('sites', ['site_id' => 80])->execute();
    }
}
