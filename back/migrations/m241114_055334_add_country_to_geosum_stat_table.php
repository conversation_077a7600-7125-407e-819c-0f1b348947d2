<?php

declare(strict_types=1);

namespace app\back\migrations;

class m241114_055334_add_country_to_geosum_stat_table extends BaseMigration
{
    public const string TABLE_NAME = 'lyra_country_geo_sum_stat';

    public function safeUp(): void
    {
        $this->db->createCommand()->createTable(self::TABLE_NAME, [
            'country' => 'varchar(2) not null',
            'geosum' => 'varchar(50) not null',
            'users_count' => 'int not null default 0',
            'updated_at' => 'timestamp default now()',
        ])->execute();
        $this->db->createCommand()->addPrimaryKey(self::TABLE_NAME, self::TABLE_NAME . '_pk', ['country', 'geosum'])->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropTable(self::TABLE_NAME)->execute();
    }
}
