<?php

declare(strict_types=1);

namespace app\back\migrations;

use Yiisoft\Db\Expression\Expression;

class m241001_123425_sites_fill_tracking_aliases_column extends BaseMigration
{
    private const array DATA =  [
        // SMEN
        1  => ['gmsl'], // Site::GMS
        7  => ['club-vulkan', 'cv'], // Site::CV
        13 => ['cvo', 'rubin'], // Site::RUBIN
        1007 => ['smen-space-09'], // Site::SMEN_SPACE_09
        10 => ['gmsdeluxe', 'gmsd'], // Site::GMSD
        14 => ['adm'], // Site::ADM
        15 => ['ph'], // Site::PHB
        19 => ['vip-club', 'vip-vulkan', 'vip'], // Site::VIP
        25 => ['el'], // Site::EL
        26 => ['v24'], // Site::V24
        30 => ['joycasino', 'joy'], // Site::JC
        32 => ['vd'], // Site::VD
        33 => ['vulkanroyal', 'royalcasino'], // Site::RC
        59 => ['vua'], // Site::V777
        61 => ['mbs'], // Site::MS
        62 => ['szl'], // Site::SZL
        28 => ['vs'], // Site::VS
        67 => ['7slots'], // Site::S7
        72 => ['los'], // Site::LOS
        75 => ['vtr'], // Site::VTR
        76 => ['awi'], // Site::AWI
        79 => ['7k'], // Site::K7
        84 => ['vp'], // Site::VP
        87 => ['msl'], // Site::MSL
        // GI
        17 => ['vulkanstavka'], // Site::GIV
        27 => ['vulkanvegas'], // Site::VV
        22 => ['vulkanbet'], // Site::VBC
        29 => ['ggbet'], // Site::GGB
        57 => ['vbet'], // Site::VBET
        65 => ['ggbetua'], // Site::GGUA
        44 => ['csbet'], // Site::CSBET
        40 => ['icecasino'], // Site::ICG
        42 => ['verdecasino'], // Site::VERDE
        73 => ['hitnspin'], // Site::HIT
        78 => ['slotoro'], // Site::SLTR
        85 => ['betario'], // Site::BTR
        86 => ['vulkanspiele'], // Site::VSP
        89 => ['nvcasino'], // Site::NVC
        // YS
        70 => ['mrbet'], // Site::MRB
        71 => ['spincity'], // Site::SPC
        74 => ['ggbuk'], // Site::GGBUK
        77 => ['syndicate'], // Site::SND
        80 => ['brucebet'], // Site::BB
        88 => ['karavanbet'], // Site::KRV
    ];

    public function safeUp(): void
    {
        $this->db->createCommand()->update('sites', ['tracking_aliases' => $this->updateCondition()])->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->update('sites', ['tracking_aliases' => null])->execute();
    }

    private function updateCondition(): Expression
    {
        $conditions = [];
        foreach (self::DATA as $siteId => $aliases) {
            $alias = implode(',', array_map(fn($a) => $this->db->quoteValue($a), $aliases));
            $conditions[] = "WHEN $siteId THEN ARRAY[$alias] ";
        }
        return new Expression('CASE site_id ' . implode(' ', $conditions) . ' END');
    }
}
