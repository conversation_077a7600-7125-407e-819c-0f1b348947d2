<?php

declare(strict_types=1);

namespace app\back\migrations;

class m241002_141601_tasks_schedules extends BaseMigration
{
    public function safeUp(): void
    {
        $this->db->createCommand()->createTable('tasks_schedules', [
            'cron_at timestamp(0) not null primary key',
            'created_at timestamp not null default now()',
        ])->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropTable('tasks_schedules')->execute();
    }
}
