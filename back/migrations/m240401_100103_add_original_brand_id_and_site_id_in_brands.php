<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240401_100103_add_original_brand_id_and_site_id_in_brands extends BaseMigration
{
    private const string TABLE_NAME = 'brands';

    public function safeUp(): void
    {
        $this->db->createCommand()->addColumn(self::TABLE_NAME, 'site_id', 'integer')->execute();
        $this->db->createCommand()->addColumn(self::TABLE_NAME, 'original_brand_id', 'integer')->execute();
        $this->db->createCommand()->addUnique(self::TABLE_NAME, 'site_id_original_brand_id_key', ['site_id', 'original_brand_id'])->execute();
        $this->db->createCommand('ALTER TABLE ' . self::TABLE_NAME . ' DROP CONSTRAINT brands_name_key')->execute();
        $this->db->createCommand()->createIndex(self::TABLE_NAME, 'brands_name_key', 'name')->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropColumn(self::TABLE_NAME, 'site_id')->execute();
        $this->db->createCommand()->dropColumn(self::TABLE_NAME, 'original_brand_id')->execute();
        $this->db->createCommand()->dropIndex(self::TABLE_NAME, 'brands_name_key', 'name')->execute();
        $this->db->createCommand()->addUnique(self::TABLE_NAME, 'brands_name_key', ['name'])->execute();
    }
}
