<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240223_091325_sites_fill_columns_for_bonuses_and_withdrawals extends BaseMigration
{
    private const array SITES_WITH_BONUS = [
        1,  // Site::GMS,
        7,  // Site::CV,
        10, // Site::GMSD,
        13, // Site::RUBIN,
        14, // Site::ADM,
        15, // Site::PHB,
        19, // Site::VIP,
        25, // Site::EL,
        26, // Site::V24,
        30, // Site::JC,
        32, // Site::VD,
        33, // Site::RC,
        61, // Site::MBS,
        62, // Site::SZL,
        59, // Site::V777,
        28, // Site::VS,
        67, // Site::S7,
        17, // Site::GIV,
        57, // Site::VBET,
        27, // Site::VV,
        31, // Site::VCH,
        64, // Site::VCUA,
        40, // Site::ICG,
        42, // Site::VERDE,
        29, // Site::GGB,
        72, // Site::LOS,
        79, // Site::K7,
        76, // Site::AWI,
        78, // Site::SLTR,
        73, // Site::HIT,
        84, // Site::VP,
    ];
    private const array SITES_WITH_WITHDRAWALS = [
        17, // Site::GIV,
        27, // Site::VV,
        29, // Site::GGB,
        31, // Site::VCH,
        40, // Site::ICG,
        42, // Site::VERDE,
        43, // Site::PLF,
        64, // Site::VCUA,
        73, // Site::HIT,
        78, // Site::SLTR,
         1, // Site::GMS,
         7, // Site::CV,
        10, // Site::GMSD,
        13, // Site::RUBIN,
        14, // Site::ADM,
        15, // Site::PHB,
        19, // Site::VIP,
        25, // Site::EL,
        26, // Site::V24,
        30, // Site::JC,
        32, // Site::VD,
        33, // Site::RC,
        59, // Site::V777,
        61, // Site::MBS,
        62, // Site::SZL,
        28, // Site::VS,
        67, // Site::S7,
        72, // Site::LOS,
        75, // Site::VTR,
        76, // Site::AWI,
        79, // Site::K7,
        84, // Site::VP,
        1007, // Site::SMEN_SPACE_09,
    ];

    public function safeUp(): void
    {
        $this->db->createCommand()->update('sites', ['is_bonuses_enabled' => true], ['site_id' => self::SITES_WITH_BONUS])->execute();
        $this->db->createCommand()->update('sites', ['is_withdrawals_enabled' => true], ['site_id' => self::SITES_WITH_WITHDRAWALS])->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->update('sites', ['is_bonuses_enabled' => false]);
        $this->db->createCommand()->update('sites', ['is_withdrawals_enabled' => false]);
    }
}
