<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240312_131420_s2p_mid_amount_length extends BaseMigration
{
    public function up(): void
    {
        $this->db->createCommand()->addColumn('s2p_orders', 'mid_amount', 'numeric(20,8)')->execute();
        $this->db->createCommand()->addColumn('s2p_orders', 'mid_currency', 'varchar(5)')->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()->dropColumn('s2p_orders', 'mid_amount')->execute();
        $this->db->createCommand()->dropColumn('s2p_orders', 'mid_currency')->execute();
    }
}
