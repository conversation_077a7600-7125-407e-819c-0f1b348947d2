<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250306_142621_delete_balance_column_from_users_table extends BaseMigration
{
    private const string TABLE_NAME = 'users';

    public function safeUp(): void
    {
        $this->db->createCommand()->dropColumn(self::TABLE_NAME, 'balance')->execute();
        $this->db->createCommand()->dropColumn(self::TABLE_NAME, 'balance_eur')->execute();
        $this->db->createCommand()->dropColumn(self::TABLE_NAME, 'balance_orig')->execute();
        $this->db->createCommand()->dropColumn(self::TABLE_NAME, 'balance_updated_at')->execute();
        $this->db->createCommand()->dropColumn(self::TABLE_NAME, 'currency')->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->addColumn(self::TABLE_NAME, 'balance', 'NUMERIC(20,2)')->execute();
        $this->db->createCommand()->addColumn(self::TABLE_NAME, 'balance_eur', 'NUMERIC(20,2)')->execute();
        $this->db->createCommand()->addColumn(self::TABLE_NAME, 'balance_orig', 'NUMERIC(20,2)')->execute();
        $this->db->createCommand()->addColumn(self::TABLE_NAME, 'balance_updated_at', 'TIMESTAMP(0)')->execute();
        $this->db->createCommand()->addColumn(self::TABLE_NAME, 'currency', 'VARCHAR(10)')->execute();
    }
}
