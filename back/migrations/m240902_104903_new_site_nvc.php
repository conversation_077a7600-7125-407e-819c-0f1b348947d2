<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240902_104903_new_site_nvc extends BaseMigration
{
    public function up(): void
    {
        $this->db->createCommand()->insert('sites', [
            'site_id' => 89,
            'site_name' => 'NV Casino',
            'short_name' => 'NVC',
            'group' => 'GI',
            'domain' => 'nv.casino',
        ])->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()->delete('sites', ['site_id' => 89])->execute();
    }
}
