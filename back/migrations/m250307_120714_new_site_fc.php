<?php

declare(strict_types=1);

namespace app\back\migrations;

use Yiisoft\Db\Expression\Expression;

class m250307_120714_new_site_fc extends BaseMigration
{
    public function safeUp(): void
    {
        $this->db->createCommand()->insert('sites', [
            'site_id' => 93,
            'site_name' => 'FC Test',
            'short_name' => 'FC',
            'is_bonuses_enabled' => true,
            'is_withdrawals_enabled' => true,
            'tracking_aliases' => new Expression("'{\"fortunica\"}'"),
        ])->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->delete('sites', ['site_id' => 93])->execute();
    }
}
