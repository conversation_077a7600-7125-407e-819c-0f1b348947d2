<?php

declare(strict_types=1);

namespace app\back\migrations;

use Yiisoft\Db\Connection\ConnectionInterface;

class m230428_114410_site_sd_add
{
    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    public function up(): void
    {
        $this->db->createCommand()->insert('sites', [
            'site_id' => 77,
            'site_name' => 'SyndicateCasino',
            'short_name' => 'SND',
            'group' => 'YS',
        ])->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()->delete('sites', ['site_id' => 77])->execute();
    }
}
