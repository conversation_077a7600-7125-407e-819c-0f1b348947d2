<?php

declare(strict_types=1);

namespace app\back\migrations;

class m231019_074718_users_stats_ip_new_fill extends BaseMigration
{
    public function up(): void
    {
        $this->sql(<<<SQL
DO LANGUAGE plpgsql
$$
    DECLARE
        rows CURSOR FOR SELECT
                            site_id,
                            date_trunc('hour', date_update) as d
                        FROM
                            users_stats us
                        WHERE
                            us.ip IS NOT NULL AND
                            us.ip_new IS NULL
                        GROUP BY site_id, d
                        ORDER BY 1, 2;
        row        RECORD;
        affected   integer default 0;
        total_rows integer default 0;
        t          timestamp default NOW();
        slave_lag  interval;
    BEGIN
        FOR row IN rows
            LOOP
                UPDATE users_stats
                SET ip_new = ip::inet
                WHERE date_update >= row.d AND date_update < row.d + '1 hour'::interval AND site_id = row.site_id AND ip_new is null AND ip is not null;
                GET DIAGNOSTICS affected = ROW_COUNT;
                COMMIT;
                total_rows = total_rows + affected;

                RAISE INFO '% / % / % / %', row.d, now() - t, affected, total_rows;

                SELECT write_lag INTO slave_lag FROM pg_stat_replication;
                IF slave_lag > '1 second'::interval THEN
                    RAISE INFO 'Slave lag is %, sleeping...', slave_lag;
                    PERFORM pg_sleep(60);
                END IF;

                t = now();
            END LOOP;
    END
$$;
SQL
        );
    }
}
