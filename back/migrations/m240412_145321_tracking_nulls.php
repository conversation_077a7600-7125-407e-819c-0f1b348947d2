<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240412_145321_tracking_nulls extends BaseMigration
{
    public function safeUp(): void
    {
        $this->sql("ALTER TABLE tracking ALTER COLUMN host DROP NOT NULL");
        $this->sql("ALTER TABLE tracking ALTER COLUMN url DROP NOT NULL");
    }

    public function safeDown(): void
    {
        $this->sql("ALTER TABLE tracking ALTER COLUMN host SET NOT NULL");
        $this->sql("ALTER TABLE tracking ALTER COLUMN url SET NOT NULL");
    }
}
