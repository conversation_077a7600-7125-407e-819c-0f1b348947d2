<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringValidator;
use app\back\repositories\ChartFavorites;
use app\back\repositories\ChartPanels;
use app\back\repositories\Employees;
use Yiisoft\Db\Query\Query;

class PanelsFilterForm
{
    use RichTable;

    private const int FILTER_FAVORITE_ALL = 0;
    private const int FILTER_FAVORITE_ONLY = 1;

    #[StringValidator]
    public ?string $name = null;
    #[IntValidator]
    public ?int $favorite = null;

    public function __construct(
        private readonly SecondaryConnection $db,
        private readonly BaseAuthAccess $authAccess,
        private readonly ChartPanels $chartPanels,
    ) {
        $this->sort = 'id';
        $this->favorite = $this->getChartsQuery(static::FILTER_FAVORITE_ONLY)->count() > 0
            ? static::FILTER_FAVORITE_ONLY
            : static::FILTER_FAVORITE_ALL;
    }

    public function columns(array $context): array
    {
        return [
            ['name' => 'Id', 'code' => 'id', 'sortable' => true],
            ['name' => 'Name', 'code' => 'name', 'slotName' => 'name', 'sortable' => true],
            ['name' => 'Layout', 'slotName' => 'layout'],
            ['name' => 'Actions', 'slotName' => 'actions'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(10, 'name', 'Name'),
                $this->listCell(2, 'favorite', 'Favorites', [
                    'multiple' => false,
                    'list' => Arr::assocToIdName([
                        static::FILTER_FAVORITE_ALL => 'All',
                        static::FILTER_FAVORITE_ONLY => 'Favorite',
                    ]),
                ])
            ],
        ];
    }

    private function applyFilters(Query $query): Query
    {
        $query
            ->andFilterWhere(['like', 'name', $this->name . '%', null]);

        return $query;
    }

    private function getChartsQuery(?int $favorite): Query
    {
        $favoriteJoinType = $favorite ? 'INNER JOIN' : 'LEFT JOIN';
        return (new Query($this->db))
            ->select([
                'id',
                'name',
                'config',
                'createdBy' => 'e.email',
                'favorite' => '(cf.employee_id::BOOL)'
            ])
            ->from(['p' => ChartPanels::TABLE_NAME])
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = p.created_by')
            ->join($favoriteJoinType, ['cf' => ChartFavorites::TABLE_NAME], 'cf.employee_id = :current_employee AND cf.panel_id = p.id')
            ->addParams(['current_employee' => $this->authAccess->employeeId()]);
    }

    public function data(): array
    {
        $query = $this->getChartsQuery($this->favorite)
            ->orderBy($this->getOrderMap())
            ->limit($this->getLimit())
            ->offset($this->getOffset());

        $data = $this->applyFilters($query)->all();

        $data = array_filter($data, function ($panel) {
            return $this->authAccess->can($this->chartPanels->permissionPrefix() . $panel['id']);
        });

        foreach ($data as &$row) {
            $row['layout'] = (new ChartPanelLayout(Json::decode($row['config'])))->getChartsLayout();
            unset($row['config']);
        }

        return array_values($data);
    }

    protected function total(): int
    {
        $query = (new Query($this->db))
            ->select('COUNT(*)')
            ->from(['p' => ChartPanels::TABLE_NAME]);

        return $this->applyFilters($query)->scalar();
    }
}
