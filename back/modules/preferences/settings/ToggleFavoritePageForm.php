<?php

declare(strict_types=1);

namespace app\back\modules\preferences\settings;

use app\back\components\AccessChecker;
use app\back\components\BaseAuthAccess;
use app\back\components\Form;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\jsonTypes\EmployeeSettings;
use app\back\repositories\Employees;

class ToggleFavoritePageForm
{
    use Form;

    #[StringValidator(1, 100)]
    #[StringInArrayValidator([self::class, 'allowedUrl'])]
    public string $url;

    public function __construct(
        private readonly Employees $employeesRepo,
        private readonly AccessChecker $accessChecker,
        private readonly BaseAuthAccess $auth,
    ) {
    }

    public static function allowedUrl(self $form): array
    {
        return $form->accessChecker->allPages();
    }

    public function update(): void
    {
        $authEmployee = $this->auth->employee();
        $favoritePages = $authEmployee->settings[EmployeeSettings::SETTING_FAVORITE_PAGES] ?? [];

        $existsIndex = array_search($this->url, $favoritePages, true);
        if ($existsIndex !== false) {
            unset($favoritePages[$existsIndex]);
        } else {
            $favoritePages[] = $this->url;
        }

        $authEmployee->settings[EmployeeSettings::SETTING_FAVORITE_PAGES] = $favoritePages;
        $this->employeesRepo->update($authEmployee, ['settings']);
    }
}
