<?php

declare(strict_types=1);

namespace app\back\modules\preferences\settings;

use app\back\components\accessCheck\AccessCheckAuthorized;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;

#[AccessCheckAuthorized]
class SettingsController extends WebController
{
    public function actionForm(SettingsForm $form): array
    {
        return $form->response();
    }

    public function actionUpdate(SettingsForm $form, SessionMessages $messages, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->update();
        $messages->success('Setting saved successfully');
        $this->bl()->modify($request->json());
    }

    public function actionToggleFavoritePage(ToggleFavoritePageForm $form, SessionMessages $messages, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->update();
        $this->bl()->modify($request->json());
    }
}
