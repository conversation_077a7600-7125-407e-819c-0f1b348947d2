<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\validators\IntArrayValidator;
use app\back\repositories\UseragentBrowsers;
use Yiisoft\Db\Connection\ConnectionInterface;

class UseragentBrowserColumn extends BaseColumn implements Selected, Filtered, Decorated, Operators
{
    public string $column = 'browser_id';
    public string $title = 'Browser';

    public function __construct(private readonly UseragentBrowsers $useragentBrowsersRepo)
    {
    }

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'liveSearchUrl' => '/dictionaries/filtered/useragent-browser',
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $this->filterInNotIn($db, $query, $value, $operator);
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        $otherId = (int)$this->useragentBrowsersRepo->getOtherBrowserId();

        return "COALESCE({$this->tableAlias}.\"{$this->column}\", {$otherId})";
    }

    public function decorate($value, array $row)
    {
        return $this->useragentBrowsersRepo->getNameById($value);
    }

    public function operators(): array
    {
        return Operators::IN_NOT_IN;
    }
}
