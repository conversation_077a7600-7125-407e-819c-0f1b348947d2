<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\entities\AffParam;
use app\back\repositories\AffParams;
use Yiisoft\Db\Connection\ConnectionInterface;

class AffParamAppTokenColumn extends AffParamBaseColumn
{
    public string $title = 'App token';

    protected function paramKeyName(): string
    {
        return AffParam::P_CLICK_ID;
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        $paramKey = $this->paramKeyName();

        return AffParams::tokenIdExpression($this->tableAlias, $this->column . "->>'$paramKey'");
    }
}
