<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use Yiisoft\Db\Connection\ConnectionInterface;

class CommentColumn extends BaseColumn implements Selected, Filtered, Operators
{
    public string $column = 'comment';
    public string $title = 'Comment';

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
            'placeholder' => self::PLACEHOLDER,
        ];
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $values = Str::explodeText($value);
        $expression = $this->selectExpression($db, $query);

        $hasBlankColumn = false;

        foreach ($values as $key => $val) {
            if ($val === self::BLANK_FAKE_VALUE) {
                $hasBlankColumn = true;
                unset($values[$key]);
            }
        }

        $conditions = [];

        if ($hasBlankColumn) {
            $conditions[] = [$expression => ''];
            $conditions[] = [$expression => null];
        }

        if (!empty($values)) {
            $regExp = Db::prepareDbEnumRegex($values);
            $conditions[] = ['~*', $expression, $regExp];
        }

        $query->andWhere(['OR', ...$conditions]);
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public function operators(): array
    {
        return [Operators::IN];
    }
}
