<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\validators\IntArrayValidator;
use app\back\repositories\UseragentPlatforms;
use Yiisoft\Db\Connection\ConnectionInterface;

class UseragentPlatformColumn extends BaseColumn implements Selected, Filtered, Decorated, Operators
{
    public string $column = 'platform_id';
    public string $title = 'Platform';

    public function __construct(private readonly UseragentPlatforms $useragentPlatformsRepo)
    {
    }

    public function inputProps(): array
    {
        return array_merge([
            'type' => 'select',
        ], $this->useragentPlatformsRepo->getNamesWithGroups());
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, $this->useragentPlatformsRepo->getNames()];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $this->filterInNotIn($db, $query, $value, $operator);
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        $otherId = (int)$this->useragentPlatformsRepo->getOtherPlatformId();

        return "COALESCE({$this->tableAlias}.\"{$this->column}\", {$otherId})";
    }

    public function decorate($value, array $row)
    {
        return $this->useragentPlatformsRepo->getNameById($value);
    }

    public function operators(): array
    {
        return Operators::IN_NOT_IN;
    }
}
