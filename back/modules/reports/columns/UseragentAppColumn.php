<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\repositories\UseragentApps;
use Yiisoft\Db\Connection\ConnectionInterface;

class UseragentAppColumn extends BaseColumn implements Selected, Decorated, Filtered, Operators
{
    public string $title = 'App';

    public function __construct(private readonly UseragentApps $useragentAppsRepo)
    {
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return "{$this->tableAlias}.app_id";
    }

    public function decorate($value, array $row)
    {
        return $this->useragentAppsRepo->getNameById($value);
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $this->filterInNotIn($db, $query, $value, $operator);
    }

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName($this->useragentAppsRepo->getNames()),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, $this->useragentAppsRepo->getNames()];
    }

    public function operators(): array
    {
        return Operators::IN_NOT_IN;
    }
}
