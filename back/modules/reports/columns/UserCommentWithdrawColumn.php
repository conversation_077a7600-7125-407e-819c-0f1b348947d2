<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use Yiisoft\Db\Connection\ConnectionInterface;

class UserCommentWithdrawColumn extends BaseColumn implements Selected
{
    public string $title = 'User comment';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression('comment');
    }
}
