<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use Yiisoft\Db\Connection\ConnectionInterface;

class UseragentBrowserVersionColumn extends BaseColumn implements Selected
{
    public string $title = 'Browser version';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return "{$this->tableAlias}.browser_version";
    }
}
