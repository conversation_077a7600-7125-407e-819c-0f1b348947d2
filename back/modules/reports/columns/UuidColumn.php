<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Str;
use app\back\components\validators\UuidMultiLineValidator;
use Yiisoft\Db\Connection\ConnectionInterface;

class UuidColumn extends BaseColumn implements Selected, Filtered
{
    public string $title = 'Uuid';

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
            'placeholder' => 'Use ⏎ for multiple values',
        ];
    }

    public function rule(): array
    {
        return [UuidMultiLineValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andWhere([$this->selectExpression($db, $query) => Str::explodeText($value)]);
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
