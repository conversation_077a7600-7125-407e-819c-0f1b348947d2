<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\BettingOdd;

class BettingOddStatusColumn extends BaseColumn implements Decorated, Selected, Filtered
{
    use FilterAndSelectDefault;

    public string $column = 'status';
    public string $title = 'Odd Status';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(BettingOdd::STATUSES),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, BettingOdd::STATUSES];
    }

    public function decorate($value, array $row)
    {
        return BettingOdd::getStatusNameById($value);
    }
}
