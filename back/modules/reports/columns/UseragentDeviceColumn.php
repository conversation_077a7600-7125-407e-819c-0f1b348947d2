<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use app\back\repositories\DbLargeDictionary;
use app\back\repositories\UseragentDevices;
use Yiisoft\Db\Connection\ConnectionInterface;

class UseragentDeviceColumn extends BaseColumn implements Selected, Filtered, Decorated
{
    public string $title = 'Device';

    private readonly DbLargeDictionary $devicesDict;

    public function __construct(UseragentDevices $useragentDevicesRepo)
    {
        $this->devicesDict = new DbLargeDictionary(UseragentDevices::class, $useragentDevicesRepo->db);
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return "{$this->tableAlias}.device_id";
    }

    public function decorate($value, array $row)
    {
        return $this->devicesDict->getNameById($value);
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andWhere(["{$this->tableAlias}.name" => Str::explodeText($value)]);
    }

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
            'placeholder' => 'Use ⏎ for multiple values',
        ];
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }
}
