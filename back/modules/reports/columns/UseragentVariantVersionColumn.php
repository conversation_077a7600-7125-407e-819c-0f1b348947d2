<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use Yiisoft\Db\Connection\ConnectionInterface;

class UseragentVariantVersionColumn extends BaseColumn implements Selected
{
    public string $title = 'Variant version';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return "{$this->tableAlias}.variant_version";
    }
}
