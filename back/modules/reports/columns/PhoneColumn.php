<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\modules\reports\columns\decorators\MaskPhoneDecorator;
use Yiisoft\Db\Connection\ConnectionInterface;

class PhoneColumn extends BaseIdColumn implements Decorated
{
    use MaskPhoneDecorator;
    use FilterOperatorsInNotInPrefix;

    public bool $stringsAllowed = true;

    public string $column = 'phone';
    public string $title = 'Phone';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
