<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use app\back\modules\reports\columns\decorators\MaskDecorator;
use Yiisoft\Db\Connection\ConnectionInterface;

class RequisiteColumn extends BaseIdColumn implements Decorated
{
    use MaskDecorator;

    public string $column = 'requisite';
    public bool $stringsAllowed = true;

    public string $title = 'Requisite';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class, 'min' => 4];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andWhere([$operator === Operators::NOT_IN ? '!~*' : '~*', $this->selectExpression($db, $query), Db::prepareDbEnumRegex(Str::explodeText($value))]);
    }
}
