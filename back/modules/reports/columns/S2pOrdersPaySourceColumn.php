<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\entities\S2pOrder;
use Yiisoft\Db\Connection\ConnectionInterface;

class S2pOrdersPaySourceColumn extends BaseColumn implements Selected, Decorated
{
    public string $title = 'Pay source';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return '(' . S2pOrder::getComputedPaySource($this->tableAlias) . ')';
    }

    public function decorate($value, array $row): string
    {
        return S2pOrder::getPaySourceById($value);
    }
}
