<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\validators\IntArrayValidator;
use app\back\repositories\GameVendors;

class GameVendorColumn extends BaseColumn implements Selected, Filtered, Decorated
{
    use FilterAndSelectDefault;

    public string $column = 'vendor_id';
    public string $title = 'Game vendor';

    public function __construct(private readonly GameVendors $gameVendors)
    {
    }

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => $this->gameVendors->getIdNameDict(),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, $this->gameVendors->getNames()];
    }

    public function decorate($value, array $row)
    {
        return $this->gameVendors->getNameById($value);
    }
}
