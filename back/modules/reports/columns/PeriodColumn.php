<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use Yiisoft\Db\Connection\ConnectionInterface;

class PeriodColumn extends BaseColumn implements Selected
{
    public const int PERIOD_UNIT_HOUR = 1;
    public const int PERIOD_UNIT_DAY = 2;

    private const array PERIODS = [
        self::PERIOD_UNIT_HOUR => ['hours', 2, 3600],
        self::PERIOD_UNIT_DAY => ['days', 5, 3600 * 24],
    ];

    public string $innerExpression;
    public int $periodUnit = self::PERIOD_UNIT_DAY;
    public bool $appendUnit = true;

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        [$unit, $precision, $denominator] = self::PERIODS[$this->periodUnit];

        $expr = "(EXTRACT(epoch FROM ($this->innerExpression)::interval(0))/$denominator)::numeric(10,$precision)";
        if ($this->appendUnit) {
            $expr .= " || ' $unit'";
        }

        return $expr;
    }
}
