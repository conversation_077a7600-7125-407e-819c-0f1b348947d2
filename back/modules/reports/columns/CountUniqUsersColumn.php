<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use Yiisoft\Db\Connection\ConnectionInterface;

class CountUniqUsersColumn extends BaseColumn implements Selected
{
    public string $title = 'Count (uniq users)';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return "COUNT(DISTINCT {$this->tableAlias}.site_id || '-' || {$this->tableAlias}.user_id)";
    }
}
