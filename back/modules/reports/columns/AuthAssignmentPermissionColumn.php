<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\AccessChecker;
use app\back\components\helpers\Arr;
use app\back\components\validators\StringArrayValidator;

class AuthAssignmentPermissionColumn extends BaseColumn implements Selected, Filtered, Decorated
{
    use FilterAndSelectDefault;

    public string $column = 'permission';
    public string $title = 'Permission';

    public function __construct(
        private readonly AccessChecker $accessChecker
    ) {
    }

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName($this->accessChecker->allPermissionsAssoc()),
        ];
    }

    public function rule(): array
    {
        return [StringArrayValidator::class, $this->accessChecker->allPermissionsAssoc()];
    }

    public function decorate($value, array $row)
    {
        return $this->accessChecker->permissionDescriptionByName($value) ?: ($value . ' (OLD)');
    }
}
