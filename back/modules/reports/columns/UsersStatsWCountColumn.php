<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\entities\UserTransaction;
use app\back\modules\reports\columns\decorators\CountDecorator;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;

class UsersStatsWCountColumn extends BaseColumn implements Selected, Decorated
{
    use CountDecorator;

    public string $title = 'W count';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string | Expression
    {
        $t = $this->tableAlias;
        return new Expression("SUM(1) FILTER (WHERE $t.op_id = :op_out AND $t.status = :success)", [
            'op_out' => UserTransaction::OP_OUT,
            'success' => UserTransaction::STATUS_SUCCESS,
        ]);
    }
}
