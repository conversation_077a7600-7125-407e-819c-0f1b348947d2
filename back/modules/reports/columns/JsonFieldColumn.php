<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Json;
use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use Yiisoft\Db\Connection\ConnectionInterface;

class JsonFieldColumn extends BaseColumn implements Selected, Filtered
{
    public string $column = '';
    public string $title = '';
    public string $key = '';

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
            'placeholder' => 'Use ⏎ for multiple values',
        ];
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andWhere([$this->selectExpression($db, $query) => Str::explodeText($value)]);
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return Json::sqlStrGet($this->columnExpression(), $this->key);
    }
}
