<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\S2pOrder;

class S2pOrdersPaySysTypeColumn extends BaseColumn implements Selected, Decorated, Filtered
{
    use FilterAndSelectDefault;

    public string $column = 'pay_sys_type';
    public string $title = 'Pay sys type';

    public function decorate($value, array $row)
    {
        return S2pOrder::PAY_SYSTEM_TYPES[$value] ?? null;
    }

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(S2pOrder::PAY_SYSTEM_TYPES),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, S2pOrder::PAY_SYSTEM_TYPES];
    }
}
