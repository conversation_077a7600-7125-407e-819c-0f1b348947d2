<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\modules\reports\columns\decorators\PercentDecorator;
use app\back\modules\reports\columns\decorators\TrendStyled;

class PercentDiffColumn extends PercentColumn implements Tooltiped
{
    use PercentDecorator, TrendStyled {
        PercentDecorator::styles as percentStyles;
        TrendStyled::styles as trendStyles;
    }

    public function styles($value, array $row): array
    {
        return array_merge($this->percentStyles($value, $row), $this->trendStyles($value, $row));
    }

    public function tooltip($value, array $row): string
    {
        return $this->decorate($row[$this->diffValueKey], $row);
    }
}
