<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Cohort;

use app\back\components\helpers\Arr;
use app\back\components\validators\StringArrayValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Filtered;
use Yiisoft\Db\Connection\ConnectionInterface;

class CohortAgeColumn extends BaseColumn implements Filtered
{
    public string $usiAlias = 'usi';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName($this->getAges()),
        ];
    }

    public function rule(): array
    {
        return [StringArrayValidator::class, $this->getAges()];
    }


    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $col = "DATE_PART('year', AGE(COALESCE({$this->tableAlias}.birthday, {$this->usiAlias}.birthday)))";

        $ranges = array_map(
            static function ($age) use ($col) {
                preg_match('#(?<from>\d+)?-?(?<to>\d+)?#i', (string)$age, $matches);
                $res = ['AND'];
                if (!empty($matches['from'])) {
                    $res[] = ['>=', $col, $matches['from']];
                }
                if (!empty($matches['to'])) {
                    $res[] = ['<=', $col, $matches['to']];
                }

                if (empty($matches['from'])) {
                    $res = ['OR', $res, ['IS', $col, null]];
                }
                return $res;
            },
            $value
        );

        $query->andWhere(['OR', ...$ranges]);
    }

    public function getAges(): array
    {
        return [
            '18-24' => '18-24',
            '25-34' => '25-34',
            '35-44' => '35-44',
            '45-54' => '45-54',
            '55-64' => '55-64',
            '65-' => '65+',
            '-17' => 'Unknown',
        ];
    }
}
