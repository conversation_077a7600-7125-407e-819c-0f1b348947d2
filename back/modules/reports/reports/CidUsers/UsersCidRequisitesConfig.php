<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\CidUsers;

use app\back\entities\UserCloudSource;
use app\back\modules\reports\columns\RequisiteColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\UserCloudSources;

class UsersCidRequisitesConfig extends BaseQueryConfig
{
    protected function beforeQuery(): void
    {
        $this->request
            ->select('site_id', 'user_id')
            ->group('site_id', 'user_id');

        $usersTable = CidUsersConfig::USERS_TABLE;
        $this->query
            ->andWhere("{$usersTable}.site_id = ucsf.site_id AND {$usersTable}.user_id = ucsf.user_id")
            ->andWhere(['ucsf.type' => UserCloudSource::TYPE_REQUISITE]);
    }

    public function selects(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'ucsf'],
            'user_id' => [UserIdColumn::class, 'ucsf'],
            'requisites' => [RequisiteColumn::class, ['expr' => "STRING_AGG(ucsf.val, ', ')"]],
        ];
    }

    public function groups(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'ucsf'],
            'user_id' => [UserIdColumn::class, 'ucsf'],
        ];
    }

    public function filters(): array
    {
        return [];
    }

    public function tableMap(): array
    {
        return [
            'ucsf' => [UserCloudSources::TABLE_NAME],
        ];
    }
}
