<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusActivity;

use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class BonusNameColumn extends BaseColumn implements Selected
{
    public string $title = 'Bonus Name';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->tableAlias . '.name';
    }
}
