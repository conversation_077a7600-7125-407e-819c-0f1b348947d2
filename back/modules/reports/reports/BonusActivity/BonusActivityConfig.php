<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusActivity;

use app\back\components\helpers\DateHelper;
use app\back\modules\reports\columns\BonusIdColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\Bonuses;
use app\back\repositories\BonusUserActivities;

class BonusActivityConfig extends BaseReportConfig
{
    public function rules(): array
    {
        return [
            ['site_id', 'required'],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', DateHelper::monthBegin(), '>='],
            ['date', DateHelper::yesterday(), '<='],
            ['site_id', []],
            ['columns', ['site_id', 'user_id', 'date', 'bonus_id', 'bonus_name', 'status']],
            ['metrics', ['amount']],
        ];
    }

    public function filters(): array
    {
        return [
            'General' => [
                'date' => [DateColumn::class, ['bua' => 'created_at']],
                'site_id' => [SiteIdColumn::class, 'bua'],
                'user_id' => [UserIdColumn::class, 'bua'],
                'bonus_id' => [BonusIdColumn::class, 'bua'],
                'status' => [BonusUserActivityStatusColumn::class, 'bua'],
            ]
        ];
    }

    public function columns(): array
    {
        return [
            'General' => [
                'date' => [DateColumn::class, ['bua' => 'created_at']],
                'site_id' => [SiteIdColumn::class, 'bua'],
                'user_id' => [UserIdColumn::class, 'bua'],
                'bonus_id' => [BonusIdColumn::class, 'bua'],
                'bonus_name' => [BonusNameColumn::class, 'b'],
                'status' => [BonusUserActivityStatusColumn::class, 'bua'],
            ]
        ];
    }

    public function metrics(): array
    {
        return [
            'General' => [
                'amount' => [CountColumn::class, 'bua', 'title' => 'Amount'],
            ]
        ];
    }

    public function groups(): array
    {
        return [
            'General' => [
                'site_id' => [SiteIdColumn::class, 'bua'],
                'user_id' => [UserIdColumn::class, 'bua'],
                'bonus_id' => [BonusIdColumn::class, 'bua'],
                'status' => [BonusUserActivityStatusColumn::class, 'bua'],
            ]
        ];
    }

    public function tableMap(): array
    {
        return [
            'bua' => [BonusUserActivities::TABLE_NAME],
            'b' => [Bonuses::TABLE_NAME, 'b.site_id = bua.site_id and b.bonus_id = bua.bonus_id'],
        ];
    }
}
