<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusJournal;

use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class TemplateNameColumn extends BaseColumn implements Filtered, Selected, Operators
{
    public string $title = 'Program name';
    public string $column = 'name';

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andWhere(Db::filterWithDefault($this->columnExpression(), Str::explodeText($value), null, $operator));
    }

    public function inputProps(): array
    {
        return [
            'type' => 'text-input-live-search',
            'inputType' => 'text-area',
            'liveSearchUrl' => '/dictionaries/filtered/bonus-templates-names',
        ];
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public function width(): int
    {
        return 6;
    }

    public function operators(): array
    {
        return self::IN_NOT_IN;
    }
}
