<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusJournal;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\BonusJournal;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class RewardTypeColumn extends BaseColumn implements Filtered, Selected, Decorated
{
    use FilterAndSelectDefault;

    public string $column = 'reward_type';
    public string $title = 'Reward type';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(BonusJournal::REWARD_TYPES),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, BonusJournal::REWARD_TYPES];
    }

    public function decorate($value, array $row)
    {
        return BonusJournal::REWARD_TYPES[$value] ?? $value;
    }
}
