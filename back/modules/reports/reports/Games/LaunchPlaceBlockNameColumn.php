<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Games;

use app\back\modules\reports\columns\BaseIdColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use Yiisoft\Db\Connection\ConnectionInterface;

class LaunchPlaceBlockNameColumn extends BaseIdColumn
{
    public string $title = 'Launch place block name';
    public string $column = 'name';

    public bool $stringsAllowed = true;

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
