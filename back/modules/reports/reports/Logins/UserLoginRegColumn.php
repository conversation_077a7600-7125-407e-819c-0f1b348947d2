<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Logins;

use app\back\components\helpers\Arr;
use app\back\components\validators\BooleanArrayValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\decorators\YesNoDecorator;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class UserLoginRegColumn extends BaseColumn implements Decorated, Selected, Filtered
{
    use FilterAndSelectDefault;
    use YesNoDecorator;

    public string $column = 'reg';
    public string $title = 'Is reg';

    public function inputProps(): array
    {
        return [

            'type' => 'select',
            'list' => Arr::assocToIdName([
                0 => 'No',
                1 => 'Yes',
            ]),
        ];
    }

    public function rule(): array
    {
        return [BooleanArrayValidator::class];
    }
}
