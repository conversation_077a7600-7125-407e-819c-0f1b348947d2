<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\GamesComparison;

use app\back\modules\reports\columns\DateRangeColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\HourColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\UserGameTokens;
use app\back\repositories\UserIgnoreIds;

class GamesComparisonProductQueryConfig extends BaseQueryConfig
{
    public function selects(): array
    {
        return [
            GamesComparisonConfig::SUB_METRIC_BET_AMOUNT => [SimpleColumn::class, ['expr' => 'SUM(ugt.bet_amount_eur)', 'ugt']],
            GamesComparisonConfig::SUB_METRIC_WIN_AMOUNT => [SimpleColumn::class, ['expr' => 'SUM(ugt.win_amount_eur)', 'ugt']],
            GamesComparisonConfig::SUB_METRIC_USERS => [SimpleColumn::class, ['expr' => 'NULLIF(COUNT(DISTINCT ugt.user_id), 0)', 'ugt']],

            'site_id' => [SiteIdColumn::class, 'ugt'],
            'week' => [WeekColumn::class, ['ugt' => 'last_action_at']],
            'day' => [DayColumn::class, ['ugt' => 'last_action_at']],
            'hour' => [HourColumn::class, ['ugt' => 'last_action_at']],
        ];
    }

    public function filters(): array
    {
        return [
            'date_range' => [DateRangeColumn::class, ['ugt' => 'last_action_at']],
            'site_id' => [SiteIdColumn::class, 'ugt'],
        ];
    }

    public function groups(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'ugt'],
            'week' => [WeekColumn::class, ['ugt' => 'last_action_at']],
            'day' => [DayColumn::class, ['ugt' => 'last_action_at']],
            'hour' => [HourColumn::class, ['ugt' => 'last_action_at']],
        ];
    }

    public function tableMap(): array
    {
        return [
            'ugt' => [UserGameTokens::TABLE_NAME],
        ];
    }

    protected function beforeQuery(): void
    {
        $this->request->select('site_id');
        $this->request->group('site_id');

        UserIgnoreIds::excludeUsers($this->query, 'ugt');
    }
}
