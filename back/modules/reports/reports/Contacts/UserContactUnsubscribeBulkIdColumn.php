<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Contacts;

use app\back\modules\reports\columns\BaseIdColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use Yiisoft\Db\Connection\ConnectionInterface;

class UserContactUnsubscribeBulkIdColumn extends BaseIdColumn
{
    public string $title = 'Unsub. Bulk ID';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return "{$this->tableAlias}.unsubscribe_bulk_id";
    }
}
