<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Conversion;

use app\back\entities\UserKyc;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\UserKycs;

class ConversionVerificationQueryConfig extends BaseQueryConfig
{
    protected function beforeQuery(): void
    {
        $this->request
            ->select('site_id', 'user_id')
            ->group('site_id', 'user_id');

        $this->query->where([
            'kyc.kyc_status' => UserKyc::KYC_VERIFIED,
        ]);
    }

    public function selects(): array
    {
        return [
            'site_id' => [SimpleColumn::class, ['u' => 'site_id']],
            'user_id' => [SimpleColumn::class, ['u' => 'user_id']],
            'verify_count' => [CountColumn::class, 'kyc'],
        ];
    }

    public function groups(): array
    {
        return [
            'site_id' => [SimpleColumn::class, ['u' => 'site_id']],
            'user_id' => [SimpleColumn::class, ['u' => 'user_id']],
        ];
    }

    public function filters(): array
    {
        return [
            'date' => [DateColumn::class, ['kyc' => 'status_updated_at']],
        ];
    }

    public function tableMap(): array
    {
        return [
            'u' => [ConversionConfig::USERS_TABLE],
            'kyc' => [UserKycs::TABLE_NAME, 'kyc.site_id = u.site_id AND kyc.user_id = u.user_id', ['u'], 'INNER JOIN']
        ];
    }
}
