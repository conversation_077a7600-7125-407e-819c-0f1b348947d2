<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Conversion;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\DateHelper;
use app\back\entities\Rate;
use app\back\modules\reports\columns\AffParamAppTokenColumn;
use app\back\modules\reports\columns\AffParamClickIdColumn;
use app\back\modules\reports\columns\AffDataColumn;
use app\back\modules\reports\columns\AffParamsColumn;
use app\back\modules\reports\columns\AffParamSubIdColumn;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\CityColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CrmRuleProviderTypeColumn;
use app\back\modules\reports\columns\CrmRuleTitleColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\CurrencyFilterColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\HostColumn;
use app\back\modules\reports\columns\HourColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\LandingPageColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MarketingTidBannerColumn;
use app\back\modules\reports\columns\MarketingTidBannerIdColumn;
use app\back\modules\reports\columns\MarketingTidDealColumn;
use app\back\modules\reports\columns\MarketingTidPublisherColumn;
use app\back\modules\reports\columns\Minute10Column;
use app\back\modules\reports\columns\ModeColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\PercentColumn;
use app\back\modules\reports\columns\PeriodColumn;
use app\back\modules\reports\columns\RefcodeAppIdColumn;
use app\back\modules\reports\columns\RefcodeColumn;
use app\back\modules\reports\columns\RefcodeProgramIdColumn;
use app\back\modules\reports\columns\RefcodePublisherColumn;
use app\back\modules\reports\columns\SiteHostForToxicUserColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\TrafficSourceColumn;
use app\back\modules\reports\columns\UseragentAppColumn;
use app\back\modules\reports\columns\UseragentAppGroupColumn;
use app\back\modules\reports\columns\UseragentBrowserColumn;
use app\back\modules\reports\columns\UseragentBrowserVersionColumn;
use app\back\modules\reports\columns\UseragentColumn;
use app\back\modules\reports\columns\UseragentDeviceColumn;
use app\back\modules\reports\columns\UseragentPlatformColumn;
use app\back\modules\reports\columns\UseragentPlatformGroupColumn;
use app\back\modules\reports\columns\UseragentPlatformVersionColumn;
use app\back\modules\reports\columns\UseragentVariantColumn;
use app\back\modules\reports\columns\UseragentVariantVersionColumn;
use app\back\modules\reports\columns\UserCidColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UserIsAffHiddenColumn;
use app\back\modules\reports\columns\UserLocaleColumn;
use app\back\modules\reports\columns\UserPriorityTypeOfGamblingColumn;
use app\back\modules\reports\columns\UserRegistrationMethodColumn;
use app\back\modules\reports\columns\UserSocialIdColumn;
use app\back\modules\reports\columns\UserStatusColumn;
use app\back\modules\reports\columns\VipAffAffSourceColumn;
use app\back\modules\reports\columns\VipAffProgTypeColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerGroupColumn;
use app\back\modules\reports\columns\WebmasterIdColumn;
use app\back\modules\reports\columns\WebmasterProgramTypeColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\columns\WeekDayColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\modules\reports\components\QueryRequest;
use Yiisoft\Db\Query\Query;

class ConversionConfig extends BaseReportConfig
{
    public const string MODE_REG = 'reg';
    public const string MODE_FD = 'fd';

    public const array MODES = [
        self::MODE_REG => 'Reg',
        self::MODE_FD => 'FD',
    ];

    public const string USERS_TABLE = 'users';
    public const string STATS_TABLE = 'stats';
    public const string USERS_LIMIT_TABLE = 'users_limit';
    public const string USERS_VERIFY_TABLE = 'users_verify';

    public bool $sitesIsRequired = true;
    private bool $canViewConversionCpa = true;
    private bool $canViewConversionLetters = true;
    private bool $canViewAffHidden = true;

    public function init(): void
    {
        $authAccess = $this->container->get(BaseAuthAccess::class);
        $this->canViewConversionCpa = $authAccess->canViewConversionCpa();
        $this->canViewConversionLetters = $authAccess->canViewConversionLetters();
        $this->canViewAffHidden = $authAccess->canViewAffHidden();
    }

    public function filters(): array
    {
        $filters =  [
            'Main' => [
                'date' => [DateColumn::class, ['u:n', 'us:nl', 'ul:nl', 'uv:nl']],
                'site_id' => [SiteIdColumn::class, ['u:n', 'us:nl', 'l:nl', 'cpa:nl']],
                'mode' => [ModeColumn::class, 'u:n', 'title' => 'Filter type', 'modes' => static::MODES],
            ],
            'User (reg)' => [
                'site_user' => [SiteUserColumn::class, 'u:n'],
                'ignore' => [IgnoreColumn::class, 'u:n'],
                'user_id' => [UserIdColumn::class, 'u:n'],
                'country' => [CountryColumn::class, 'u:n'],
                'city' => [CityColumn::class, 'u:n'],
                'host' => [HostColumn::class, 'u:n'],
                'brand_id' => [BrandColumn::class, 'u:n'],
                'locale' => [UserLocaleColumn::class, 'u:n'],
                'social_net' => [UserSocialIdColumn::class, 'u:n'],
                'user_status' => [UserStatusColumn::class, 'u:n'],
                'user_currency' => [CurrencyFilterColumn::class, 'u:n', 'currencies' => Rate::currencies(), 'allowMultiple' => true],
                'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u:n'],
                'registration_method' => [UserRegistrationMethodColumn::class, 'u:n'],
                'location' => [LocationColumn::class, 'u:n'],
            ],
            'Traffic (reg)' => [
                'refcode' => [RefcodeColumn::class, 'u:n'],
                'traffic_source' => [TrafficSourceColumn::class, 'u:n'],
                'webmaster_id' => [WebmasterIdColumn::class, 'u:n'],
                'ref_app_id' => [RefcodeAppIdColumn::class, 'u:n'],
                'ref_program_id' => [RefcodeProgramIdColumn::class, 'u:n'],

                'click_id' => [AffParamClickIdColumn::class, 'u:n'],
                'app_token' => [AffParamAppTokenColumn::class, 'u:n'],

                'aff_owner' => [WebmasterAffOwnerColumn::class, 'u:n'],
                'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'u:n'],
                'program_type' => [WebmasterProgramTypeColumn::class, 'u:n'],

                'publisher' => [MarketingTidPublisherColumn::class, 'u:n'],

                'vip_aff_aff_source' => [VipAffAffSourceColumn::class, 'u:n'],
                'vip_aff_program_type' => [VipAffProgTypeColumn::class, 'u:n'],
            ],
            'Useragent (reg)' => [
                'useragent' => [UseragentColumn::class, 'u:n'],
                'platform_id' => [UseragentPlatformColumn::class, 'u:n'],
                'platform_group' => [UseragentPlatformGroupColumn::class, 'u:n'],
                'browser_id' => [UseragentBrowserColumn::class, 'u:n'],
                'variant_id' => [UseragentVariantColumn::class, 'u:n'],
                'app_id' => [UseragentAppColumn::class, 'u:n'],
                'app_group_id' => [UseragentAppGroupColumn::class, 'u:n'],
            ],
            'Payment' => [
                'action_date' => [DateColumn::class, 'us:n', 'reversedOperators' => true],
                'action_date_type' => [ModeColumn::class, 'us:n', 'title' => 'Date type', 'modes' => ConversionStatsQueryConfig::DATE_TYPES], // TODO Date type column
                'action_join_dates' => [ConversionConnectPeriodGroupsColumn::class, 'us:n', 'title' => 'Connect period groups'],
                'currency' => [CurrencyColumn::class, ['cpa:nl', 'us:nl']],
            ],
        ];

        if ($this->canViewConversionLetters) {
            $filters['Letters'] = [
                'letter_rule' => [CrmRuleTitleColumn::class, 'l:n'],
                'letter_provider_type' => [CrmRuleProviderTypeColumn::class, 'l:n'],
            ];
        }

        if ($this->canViewAffHidden) {
            $filters['Traffic']['is_aff_hidden'] = [UserIsAffHiddenColumn::class, 'u:n'];
        }

        return $filters;
    }

    public function columns(): array
    {
        return [];
    }

    public function metrics(): array
    {
        $countSiteUsersExpression = "COUNT(DISTINCT (u.site_id || '-' || u.user_id))";

        $metrics = [
            'User (reg)' => [
                'regs' => [CountColumn::class, ['expr' => $countSiteUsersExpression, 'u'], 'title' => 'Regs'],
                'confirms' => [CountColumn::class, ['expr' => "{$countSiteUsersExpression} FILTER (WHERE u.confirm)", 'u:n' => 'confirm'], 'title' => 'Confirms'],
            ],
            'Payment' => [
                'fd_tries' => [CountColumn::class, ['expr' => 'SUM(us.fd_tries)', 'us:n'], 'title' => 'F tries'],
                'fd_count' => [CountColumn::class, ['expr' => 'SUM(us.fd_count)', 'us:n'], 'title' => 'F count'],
                'fd_sum' => [MoneyColumn::class, ['expr' => 'SUM(us.fd_sum)', 'us:n'], 'title' => 'F sum'],
                'rd_count' => [CountColumn::class, ['expr' => 'SUM(us.rd_count)', 'us:n'], 'title' => 'R count'],
                'rd_sum' => [MoneyColumn::class, ['expr' => 'SUM(us.rd_sum)', 'us:n'], 'title' => 'R sum'],
                'd_count' => [CountColumn::class, ['expr' => 'SUM(us.d_count)', 'us:n'], 'title' => 'F+R count'],
                'd_sum' => [MoneyColumn::class, ['expr' => 'SUM(us.d_sum)', 'us:n'], 'title' => 'F+R sum'],
                'wd_count' => [CountColumn::class, ['expr' => 'SUM(us.wd_count)', 'us:n'], 'title' => 'W count'],
                'wd_sum' => [MoneyColumn::class, ['expr' => 'SUM(us.wd_sum)', 'us:n'], 'title' => 'W sum'],
            ],
            'Conversions' => [
                'reg_to_dep' => [PercentColumn::class, ['expr' => "SUM(us.fd_count) / NULLIF({$countSiteUsersExpression}, 0) * 100", 'us:n' => 'fd_count'], 'title' => 'Reg to dep (%)'],
                'reg_to_conf' => [PercentColumn::class, ['expr' => "{$countSiteUsersExpression} FILTER (WHERE u.confirm)::float / NULLIF({$countSiteUsersExpression}, 0) * 100", 'u:n' => 'confirm'], 'title' => 'Reg to conf (%)' ],
                'repeat_rate' => [PercentColumn::class, ['expr' => 'SUM(us.rd_count) / NULLIF(SUM(us.fd_count), 0) * 100', 'us:n' => ['rd_count', 'fd_count']], 'title' => 'Repeat rate (%)' ],
                'out_percent' => [PercentColumn::class, ['expr' => 'SUM(us.wd_sum) / NULLIF(SUM(us.d_sum), 0) * 100', 'us:n' => ['wd_sum', 'd_sum']], 'title' => 'W sum (%)' ],
                'repeat_user' => [CountColumn::class, ['expr' => "{$countSiteUsersExpression} FILTER (WHERE us.rd_count > 0)", 'us:n' => 'rd_count'], 'title' => 'Repeat users' ],
                'repeat_user_rate' => [PercentColumn::class, ['expr' => "({$countSiteUsersExpression} FILTER (WHERE us.rd_count > 0)::float / NULLIF({$countSiteUsersExpression} FILTER (WHERE us.fd_count > 0), 0)) * 100", 'us:n' => ['rd_count', 'fd_count']], 'title' => 'Repeat user rate (%)' ],
                'ngr' => [MoneyColumn::class, ['expr' => 'COALESCE(SUM(us.d_sum), 0) - COALESCE(SUM(us.wd_sum), 0)', 'us:n' => ['wd_sum', 'd_sum']], 'title' => 'NGR' ],
                'ngr_per_paid' => [MoneyColumn::class, ['expr' => '(COALESCE(SUM(us.d_sum), 0) - COALESCE(SUM(us.wd_sum), 0)) / NULLIF(SUM(us.fd_count), 0)', 'us:n' => ['wd_sum', 'd_sum', 'fd_count']], 'title' => 'NGR per paid' ],
                'rev_per_reg' => [MoneyColumn::class, ['expr' => "SUM(us.d_sum) / NULLIF({$countSiteUsersExpression}, 0)", 'us:n' => 'd_sum'], 'title' => 'Rev per reg' ],
                'rev_per_paid' => [MoneyColumn::class, ['expr' => 'SUM(us.d_sum) / NULLIF(SUM(us.fd_count), 0)', 'us:n' => ['fd_count', 'd_sum']], 'title' => 'Rev per paid' ],
                'fd_share' => [PercentColumn::class, ['expr' => 'SUM(us.fd_share) / SUM(SUM(us.fd_share)) OVER () * 100::float', 'us:n'], 'title' => 'F share (%)'],
                'reg_to_tries' => [PercentColumn::class, ['expr' => "SUM(us.fd_tries)::float / NULLIF({$countSiteUsersExpression}, 0) * 100", 'us:n' => 'fd_tries'], 'title' => 'Regs to F tries (%)'],
                'tries_to_fd' => [PercentColumn::class, ['expr' => "SUM(us.fd_count) / NULLIF(SUM(us.fd_tries), 0) * 100", 'us:n' => ['fd_tries', 'fd_count']], 'title' => 'F tries to F count (%)'],
                'reg_to_limits' => [PercentColumn::class, ['expr' => "SUM(ul.limit_exists)::float / NULLIF({$countSiteUsersExpression}, 0) * 100", 'ul:n' => 'limit_exists'], 'title' => 'Reg to limits (%)'],
                'reg_to_verify' => [PercentColumn::class, ['expr' => "SUM(uv.verify_count) / NULLIF({$countSiteUsersExpression}, 0) * 100", 'uv:n' => 'verify_count'], 'title' => 'Reg to verify (%)'],
            ],
            'Periods' => [
                'reg_to_first_dep_period' => [PeriodColumn::class, ['u', 'us:n' => 'dep_first_at', 'u:n' => 'date'], 'innerExpression' => 'PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY us.dep_first_at - u.date)', 'title' => 'Reg to dep period'],
                'fd_to_second_dep_period' => [PeriodColumn::class, ['u', 'us:n' => ['dep_first_at', 'dep_second_at']], 'innerExpression' => 'PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY us.dep_second_at - us.dep_first_at)', 'title' => 'First to second dep period'],
                'second_dep_to_third_dep_period' => [PeriodColumn::class, ['u', 'us:n' => ['dep_second_at', 'dep_third_at']], 'innerExpression' => 'PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY us.dep_third_at - us.dep_second_at)', 'title' => 'Second to third dep period'],
            ],
        ];

        if ($this->canViewConversionCpa) {
            $metrics['WP costs'] = [
                'hold_sum' => [MoneyColumn::class, ['expr' => 'SUM(cpa.hold_sum)', 'cpa:n'], 'title' => 'Hold sum'],
                'reject_sum' => [MoneyColumn::class, ['expr' => 'SUM(cpa.reject_sum)', 'cpa:n'], 'title' => 'Reject sum'],
                'approve_sum' => [MoneyColumn::class, ['expr' => 'SUM(cpa.approve_sum)', 'cpa:n'], 'title' => 'Approve sum'],
                'hold_count' => [CountColumn::class, ['expr' => 'SUM(cpa.hold_count)', 'cpa:n'], 'title' => 'Hold Count'],
                'reject_count' => [CountColumn::class, ['expr' => 'SUM(cpa.reject_count)', 'cpa:n'], 'title' => 'Reject Count'],
            ];
        }

        if ($this->canViewConversionLetters) {
            $metrics['Letters'] = [
                'l_count' => [CountColumn::class, ['expr' => 'SUM(l.l_count)', 'l:n'], 'title' => 'Letters count'],
                'lo_count' => [CountColumn::class, ['expr' => 'SUM(l.lo_count)', 'l:n'], 'title' => 'Opened letters count'],
                'lc_count' => [CountColumn::class, ['expr' => 'SUM(l.lc_count)', 'l:n'], 'title' => 'Clicked letters count'],
                'c_count' => [CountColumn::class, ['expr' => 'SUM(l.c_count)', 'l:n'], 'title' => 'Contacts count'],
                'lo_rate' => [PercentColumn::class, ['expr' => 'COALESCE(NULLIF(SUM(l.lo_count), 0) / NULLIF(SUM(l.l_count), 0) * 100, 0)', 'l:n' => ['l_count', 'lo_count']], 'title' => 'Opened to sent letters (%)' ],
                'lc_rate' => [PercentColumn::class, ['expr' => 'COALESCE(NULLIF(SUM(l.lc_count), 0) / NULLIF(SUM(l.l_count), 0) * 100, 0)', 'l:n' => ['l_count', 'lc_count']], 'title' => 'Clicked to sent letters (%)' ],
                'confirms_to_l_rate' => [PercentColumn::class, ['expr' => "COALESCE(NULLIF({$countSiteUsersExpression} FILTER (WHERE u.confirm), 0) / NULLIF(SUM(l.l_count), 0) * 100, 0)", 'l:n' => 'l_count', 'u:n' => 'confirm'], 'title' => 'Confirms to sent letters (%)' ],
            ];
        }

        return $metrics;
    }

    public function groups(): array
    {
        $groups = [
            'Main' => [
                'site_id' => [SiteIdColumn::class, 'u'],
                'month' => [MonthColumn::class, ['u' => 'date', 'u:ns' => 'date', 'us:ng']],
                'week' => [WeekColumn::class, ['u' => 'date', 'u:ns' => 'date', 'us:ng']],
                'weekday' => [WeekDayColumn::class, ['u' => 'date', 'u:ns' => 'date', 'us:ng']],
                'day' => [DayColumn::class, ['u' => 'date', 'u:ns' => 'date', 'us:ng']],
                'hour' => [HourColumn::class, ['u' => 'date', 'u:ns' => 'date', 'us:ng']],
                'minute_10' => [Minute10Column::class, ['u' => 'date', 'u:ns' => 'date', 'us:ng']],
            ],
            'User (reg)' => [
                'user_id' => [UserIdColumn::class, ['u' => 'user_id']],
                'site_user' => [SiteUserColumn::class, 'u'],
                'country' => [CountryColumn::class, ['expr' => 'u.country', 'u:ns']],
                'city' => [CityColumn::class, ['expr' => 'u.city', 'u:ns']],
                'cid' => [UserCidColumn::class, ['expr' => 'u.cid', 'u:ns']],
                'locale' => [UserLocaleColumn::class, ['expr' => 'u.locale', 'u:ns']],
                'social_net' => [UserSocialIdColumn::class, ['expr' => 'u.social_net', 'u:ns']],
                'user_currency' => [CurrencyColumn::class, ['expr' => 'u.user_currency', 'u:ns']],
                'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, ['expr' => 'u.priority_type_of_gambling', 'u:ns']],
                'registration_method' => [UserRegistrationMethodColumn::class, ['expr' => 'u.registration_method', 'u:ns']],
                'location' => [LocationColumn::class, ['expr' => 'u.location', 'u:ns']],
            ],
            'Traffic' => [
                'refcode' => [RefcodeColumn::class, ['expr' => 'u.refcode', 'u:ns']],
                'traffic_source' => [TrafficSourceColumn::class, ['expr' => 'u.traffic_source', 'u:ns']],
                'webmaster_id' => [WebmasterIdColumn::class, ['expr' => 'u.webmaster_id', 'u:ns']],
                'publisher_from_refcode' => [RefcodePublisherColumn::class, ['expr' => 'u.publisher_from_refcode', 'u:ns']],
                'ref_app_id' => [RefcodeAppIdColumn::class, ['expr' => 'u.ref_app_id', 'u:ns']],
                'ref_program_id' => [RefcodeProgramIdColumn::class, ['expr' => 'u.ref_program_id', 'u:ns']],
                'lp' => [LandingPageColumn::class, ['expr' => 'u.lp', 'u:ns']],

                'aff_data' => [AffDataColumn::class, ['expr' => 'u.aff_data', 'u:ns']],
                'aff_params' => [AffParamsColumn::class, ['expr' => 'u.aff_params', 'u:ns']],
                'click_id' => [AffParamClickIdColumn::class, ['expr' => 'u.click_id', 'u:ns']],
                'app_token' => [AffParamAppTokenColumn::class, ['expr' => 'u.app_token', 'u:ns']],
                'aff_sub_id_1' => [AffParamSubIdColumn::class, ['expr' => 'u.aff_sub_id_1', 'u:ns'], 'index' => 1],
                'aff_sub_id_2' => [AffParamSubIdColumn::class, ['expr' => 'u.aff_sub_id_2', 'u:ns'], 'index' => 2],
                'aff_sub_id_3' => [AffParamSubIdColumn::class, ['expr' => 'u.aff_sub_id_3', 'u:ns'], 'index' => 3],
                'aff_sub_id_4' => [AffParamSubIdColumn::class, ['expr' => 'u.aff_sub_id_4', 'u:ns'], 'index' => 4],
                'aff_sub_id_5' => [AffParamSubIdColumn::class, ['expr' => 'u.aff_sub_id_5', 'u:ns'], 'index' => 5],

                'aff_owner' => [WebmasterAffOwnerColumn::class, ['expr' => 'u.aff_owner', 'u:ns']],
                'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, ['expr' => 'u.aff_owner_group', 'u:ns']],
                'program_type' => [WebmasterProgramTypeColumn::class, ['expr' => 'u.program_type', 'u:ns']],
                'host' => [HostColumn::class, ['expr' => 'u.host', 'u:ns']],
                'host_toxic' => [SiteHostForToxicUserColumn::class, ['expr' => 'u.host_toxic', 'u:ns']],
                'brand_id' => [BrandColumn::class, ['expr' => 'u.brand_id', 'u:ns']],

                'publisher' => [MarketingTidPublisherColumn::class, ['expr' => 'u.publisher', 'u:ns']],
                'deal' => [MarketingTidDealColumn::class, ['expr' => 'u.deal', 'u:ns']],
                'banner' => [MarketingTidBannerColumn::class, ['expr' => 'u.banner', 'u:ns']],
                'banner_id' => [MarketingTidBannerIdColumn::class, ['expr' => 'u.banner_id', 'u:ns']],

                'vip_aff_aff_source' => [VipAffAffSourceColumn::class, ['expr' => 'u.vip_aff_aff_source', 'u:ns']],
                'vip_aff_program_type' => [VipAffProgTypeColumn::class, ['expr' => 'u.vip_aff_program_type', 'u:ns']],
            ],
            'Useragent' => [
                'useragent' => [UseragentColumn::class, ['expr' => 'u.useragent', 'u:ns']],
                'platform_id' => [UseragentPlatformColumn::class, ['expr' => 'u.platform_id', 'u:ns']],
                'platform_group' => [UseragentPlatformGroupColumn::class, ['expr' => 'u.platform_group', 'u:ns']],
                'platform_version' => [UseragentPlatformVersionColumn::class, ['expr' => 'u.platform_version', 'u:ns']],
                'browser_id' => [UseragentBrowserColumn::class, ['expr' => 'u.browser_id', 'u:ns']],
                'browser_version' => [UseragentBrowserVersionColumn::class, ['expr' => 'u.browser_version', 'u:ns']],
                'device_id' => [UseragentDeviceColumn::class, ['expr' => 'u.device_id', 'u:ns']],
                'variant_id' => [UseragentVariantColumn::class, ['expr' => 'u.variant_id', 'u:ns']],
                'variant_version' => [UseragentVariantVersionColumn::class, ['expr' => 'u.variant_version', 'u:ns']],
                'app_id' => [UseragentAppColumn::class, ['expr' => 'u.app_id', 'u:ns']],
                'app_group_id' => [UseragentAppGroupColumn::class, ['expr' => 'u.app_group_id', 'u:ns']],
            ],
        ];

        if ($this->canViewAffHidden) {
            $groups['Traffic']['is_aff_hidden'] = [UserIsAffHiddenColumn::class, ['expr' => 'u.is_aff_hidden', 'u:ns']];
        }

        return $groups;
    }

    public function tableMap(): array
    {
        return [
            'u' => [function (Query $query, QueryRequest $nestedRequest) {
                $query->withQuery((new ConversionUsersQueryConfig($this->container))->query($this->db, $nestedRequest), self::USERS_TABLE);
                return self::USERS_TABLE;
            }],
            'us' => [
                function (Query $query, QueryRequest $nestedRequest) {
                    $query->withQuery((new ConversionStatsQueryConfig($this->container))->query($this->db, $nestedRequest), self::STATS_TABLE);
                    return self::STATS_TABLE;
                },
                'us.site_id = u.site_id AND us.user_id = u.user_id',
            ],
            'l' => [ConversionLettersQueryConfig::class, 'u.site_id = l.site_id AND u.user_id = l.user_id'],
            'cpa' => [ConversionCpaQueryConfig::class, 'u.site_id = cpa.site_id AND u.user_id = cpa.user_id'],
            'ul' => [
                function (Query $query, QueryRequest $nestedRequest) {
                    $query->withQuery((new ConversionLimitsQueryConfig($this->container))->query($this->db, $nestedRequest), self::USERS_LIMIT_TABLE);
                    return self::USERS_LIMIT_TABLE;
                },
                'ul.site_id = u.site_id AND ul.user_id = u.user_id',
            ],
            'uv' => [
                function (Query $query, QueryRequest $nestedRequest) {
                    $query->withQuery((new ConversionVerificationQueryConfig($this->container))->query($this->db, $nestedRequest), self::USERS_VERIFY_TABLE);
                    return self::USERS_VERIFY_TABLE;
                },
                'uv.site_id = u.site_id AND uv.user_id = u.user_id',
            ],
        ];
    }

    public function rules(): array
    {
        return [
            ['site_id', 'required', 'when' => fn() => $this->sitesIsRequired],
            [['date', 'mode', 'currency'], 'required'],
            ['action_date_type', 'required', 'when' => fn() => $this->request->needColumn(
                'fd_count',
                'fd_tries',
                'rd_count',
                'wd_count',
                'd_sum',
                'wd_sum',
                'fd_sum',
                'rd_sum',
                'reg_to_dep',
                'repeat_rate',
                'out_percent',
                'repeat_user_rate',
                'ngr',
                'rev_per_paid'
            )],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', DateHelper::monthBegin(), '>='],
            ['date', DateHelper::yesterday(), '<='],
            ['site_id', []],
            ['mode', static::MODE_REG],
            ['ignore', IgnoreColumn::MODE_IGNORE],
            ['currency', CurrencyColumn::EUR],
            ['metrics', ['regs', 'confirms', 'fd_count', 'fd_sum', 'rd_count', 'rd_sum', 'd_sum', 'wd_sum']],
            ['action_date_type', ConversionStatsQueryConfig::DATE_TYPE_UPDATED],
            ['action_join_dates', 1],
        ];
    }
}
