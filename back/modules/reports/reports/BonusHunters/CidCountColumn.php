<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusHunters;

use app\back\components\validators\IntValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class CidCountColumn extends BaseColumn implements Selected, Filtered, Operators
{
    public string $title = 'Cid сount';

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
        ];
    }

    public function rule(): array
    {
        return [IntValidator::class];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return 'COUNT(' . $this->tableAlias . '.cid)';
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andHaving([$operator, $this->selectExpression($db, $query), $value]);
    }

    public function operators(): array
    {
        return [Operators::G, Operators::L];
    }
}
