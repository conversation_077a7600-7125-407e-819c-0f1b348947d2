<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\AdsStats;

use app\back\modules\reports\columns\BaseIdColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use Yiisoft\Db\Connection\ConnectionInterface;

class AdsCampaignExternalIdColumn extends BaseIdColumn
{
    public string $title = 'Campaign id';
    public string $column = 'external_id';
    public bool $stringsAllowed = true;

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
