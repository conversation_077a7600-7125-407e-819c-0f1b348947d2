<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\S2pOrders;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\DateHelper;
use app\back\entities\Bin;
use app\back\entities\CanonicalPaySySource;
use app\back\entities\S2pOrder;
use app\back\entities\UserIgnoreId;
use app\back\modules\reports\columns\{
    BinBankColumn,
    BinColumn,
    BinCountryColumn,
    BinCurrencyColumn,
    BinGlobalPaysysColumn,
    BinStatusColumn,
    BinTypeColumn,
    BooleanColumn,
    BrandColumn,
    CanonicalPaySystemColumn,
    CityColumn,
    CommentColumn,
    CountColumn,
    CountryColumn,
    CountryRealColumn,
    DateColumn,
    DateIntervalBucketColumn,
    DateTypeColumn,
    DayColumn,
    EmailColumn,
    HostColumn,
    HourColumn,
    IgnoreColumn,
    IpColumn,
    IsFirstSuccessColumn,
    IsFirstTryColumn,
    MarketingTidPublisherColumn,
    Minute10Column,
    MinuteColumn,
    MoneyColumn,
    MoneyPreciseColumn,
    MonthColumn,
    Operators,
    RefcodeColumn,
    RequisiteColumn,
    S2pInstanceColumn,
    S2pOrderInvoiceIdColumn,
    S2pOrdersAfScoreColumn,
    S2pOrdersAmountColumn,
    S2pOrdersCardHolderColumn,
    S2pOrdersContextColumn,
    S2pOrdersCurrencyColumn,
    S2pOrdersDeviceColumn,
    S2pOrdersIdColumn,
    S2pOrdersMidColumn,
    S2pOrdersPayClassColumn,
    S2pOrdersPayMethodColumn,
    S2pOrdersPaySourceColumn,
    S2pOrdersPaySysColumn,
    S2pOrdersPaySysTypeColumn,
    S2pOrdersPayTypeColumn,
    S2pOrdersRequisiteTypeColumn,
    S2pOrdersRoutingBranchColumn,
    S2pOrdersStatusColumn,
    S2pOrdersStatusIdColumn,
    S2pOrdersTransactionIdColumn,
    S2pOrdersTrustLevelColumn,
    S2pOrdersTrustScoreColumn,
    S2pOrdersTypeColumn,
    S2pProjectIdColumn,
    S2pProjectsFlagColumn,
    SiteIdColumn,
    SiteUserColumn,
    TimeColumn,
    TrafficSourceColumn,
    UseragentAppColumn,
    UseragentAppGroupColumn,
    UseragentBrowserColumn,
    UseragentColumn,
    UseragentDeviceColumn,
    UseragentPlatformColumn,
    UseragentPlatformGroupColumn,
    UseragentVariantColumn,
    UserCidColumn,
    UserIdColumn,
    UserKycColumn,
    UsersS2pTrustScore,
    UserStatusColumn,
    WebmasterAffOwnerColumn,
    WebmasterAffOwnerGroupColumn,
    WebmasterIdColumn,
    WeekColumn};
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\{
    Bins,
    CanonicalPaySySources,
    Cities,
    MarketingTids,
    Refcodes,
    S2pMids,
    S2pOrders,
    S2pPaySystems,
    S2pProjects,
    UseragentApps,
    UseragentPlatforms,
    Useragents,
    UserIgnoreIds,
    UserKycs,
    Users,
    UserSpecialInfos,
    WpWebmasters};

class S2pOrdersConfig extends BaseReportConfig
{
    private const array DATE_TYPES = [
        DateTypeColumn::CREATED => S2pOrder::COL_CREATED_AT,
        DateTypeColumn::UPDATED => S2pOrder::COL_UPDATED_AT,
        DateTypeColumn::SUCCESS => S2pOrder::COL_SUCCESS_AT,
    ];

    private string $payDateType = S2pOrder::COL_CREATED_AT;
    private BaseAuthAccess $authAccess;

    public function init(): void
    {
        $this->authAccess = $this->container->get(BaseAuthAccess::class);
    }

    public function rules(): array
    {
        return [
            ['pay_date', 'required', 'when' => fn() => $this->request->allEmptyFilter('order_id', 'site_user_id', 'invoice_id')],
            ['date_type', 'required', 'when' => fn() => $this->request->anyNotEmptyFilter('pay_date')],
            ['project_id', 'required', 'when' => fn() => $this->request->allEmptyFilter('site_id', 'site_user_id', 'order_id', 'invoice_id')],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['pay_date', DateHelper::monthBegin(), Operators::GE],
            ['pay_date', DateHelper::yesterday(), Operators::LE],
            ['site_id', []],
            ['date_type', DateTypeColumn::UPDATED],
            ['status', [S2pOrder::STATUS_SUCCESS]],
            ['type', [S2pOrder::TYPE_IN]],
            ['columns', ['order_id', 'project_id', 'date', 'date_created', 'status', 'type', 'is_first_success', 'pay_type', 'user_id', 'amount_eur']],
            ['ignore', UserIgnoreId::MODE_IGNORE],
            ['metrics', ['success_orders', 'count', 'amount_eur']],
        ];
    }

    protected function beforeQuery(): void
    {
        $this->payDateType = self::DATE_TYPES[(string)($this->request->getFilter('date_type') ?: DateTypeColumn::UPDATED)];
    }

    public function filters(): array
    {
        return [
            'Order' => [
                'pay_date' => [DateColumn::class, ['o' => $this->payDateType]],
                'date_type' => [DateTypeColumn::class, 'o', 'types' => array_keys(self::DATE_TYPES)],
                'project_id' => [S2pProjectIdColumn::class, 'o'],
                'site_id' => [SiteIdColumn::class, 'o'],
                'pay_time' => [TimeColumn::class, 'o'],
                'type' => [S2pOrdersTypeColumn::class, 'o'],
                'is_first_success' => [IsFirstSuccessColumn::class, 'o'],
                'status' => [S2pOrdersStatusColumn::class, 'o'],
                'status_id' => [S2pOrdersStatusIdColumn::class, 'o'],
                'requisite_type' => [S2pOrdersRequisiteTypeColumn::class, 'o'],
                'requisite' => [RequisiteColumn::class, 'o'],
                'order_id' => [S2pOrdersIdColumn::class, 'o', 'decorateS2pLink' => true],
                'invoice_id' => [S2pOrderInvoiceIdColumn::class, 'o'],
                'transaction_id' => [S2pOrdersTransactionIdColumn::class, 'o'],
                'user_id' => [UserIdColumn::class, 'o'],
                'user_cid' => [UserCidColumn::class, 'u'],
                'site_user_id' => [SiteUserColumn::class, 'o'],
                'is_first' => [IsFirstTryColumn::class, 'o'],
                'instance_id' => [S2pInstanceColumn::class, 'o'],
                'pay_host' => [HostColumn::class, 'o', 'title' => 'Pay host'],
            ],
            'Secondary' => [
                'ip' => [IpColumn::class, 'o'],
                'device' => [S2pOrdersDeviceColumn::class, 'o'],
                'pay_sys_id' => [S2pOrdersPaySysColumn::class, 'o'],
                'canonical_pay_sys_id' => [CanonicalPaySystemColumn::class, 'cps'],
                'pay_sys_type' => [S2pOrdersPaySysTypeColumn::class, 'o'],
                'pay_class_id' => [S2pOrdersPayClassColumn::class, 'o'],
                'mid_id' => [S2pOrdersMidColumn::class, 'o'],
                'country' => [CountryColumn::class, 'o'],
                'city' => [CityColumn::class, 'c'],
                'score' => [S2pOrdersAfScoreColumn::class, 'o'],
            ],

            'Useragent (pay)' => $this->useragentColumns(),
            'Traffic (pay)' => $this->trafficColumns(),

            'User (reg)' => [
                'reg_date' => [DateColumn::class, 'u'],
                'refcode' => [RefcodeColumn::class, 'r'],
                'traffic_source' => [TrafficSourceColumn::class, 'r'],
                'kyc' => [UserKycColumn::class, 'kyc'],
                'ignore' => [IgnoreColumn::class, 'o'],
                'dep_first_at' => [DateColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Date (FD)'],
                'reg_host' => [HostColumn::class, 'u'],
                'brand_id' => [BrandColumn::class, 'u'],
            ],
            'Card bins' => [
                'bin_country' => [CountryColumn::class, 'b', 'title' => 'Issuer country'],
                'bin_card' => [BinColumn::class, 'o', 'title' => 'Card Bins'],
                'bin_card_global_pay_system' => [BinGlobalPaysysColumn::class, 'b'],
            ],
        ];
    }

    public function columns(): array
    {
        return $this->getColumnsFiltered();
    }

    public function metrics(): array
    {
        return [
            'Order' => [
                'count' => [CountColumn::class, 'o', 'title' => 'Orders count'],
                'success_orders' => [CountColumn::class, ['expr' => 'COUNT(*) FILTER (WHERE o.status = ' . S2pOrder::STATUS_SUCCESS . ')'], 'title' => 'Success orders'],
                'count users' => [CountColumn::class, ['expr' => 'COUNT(DISTINCT (o.site_id, o.user_id))', 'o'], 'title' => 'Uniq users count'],
                'amount_orig' => [MoneyColumn::class, ['expr' => 'SUM(o.client_original_amount)', 'o'], 'title' => 'Amount orig (client)'],
                'amount_eur' => [MoneyColumn::class, ['expr' => 'SUM(summ_eur)', 'o'], 'title' => 'Amount EUR (client)'],
                'amount_usd' => [MoneyColumn::class, ['expr' => 'SUM(summ)', 'o'], 'title' => 'Amount USD (client)'],
                'ps_amount_orig' => [MoneyColumn::class, ['expr' => 'SUM(o.ps_original_amount)', 'o'], 'title' => 'Amount orig (pay system)'],
                'mid_amount_orig' => [MoneyPreciseColumn::class, ['expr' => 'SUM(o.mid_amount)', 'o'], 'title' => 'Amount orig (mid principal)'],
            ],
        ];
    }

    public function groups(): array
    {
        return $this->getColumnsFiltered(true);
    }

    private function getColumnsFiltered(bool $grouping = false): array
    {
        $columns = [
            'Order' => [
                'order_id' => [S2pOrdersIdColumn::class, 'o', 'decorateS2pLink' => true],
                'user_id' => [UserIdColumn::class, 'o', 'decorateWithS2pUserLink' => true, 'isHtmlValue' => true],
                'site_user_id' => [SiteUserColumn::class, 'o'],
                'cid' => [UserCidColumn::class, 'u'],
                'invoice_id' => [S2pOrderInvoiceIdColumn::class, 'o'],
                'project_id' => [S2pProjectIdColumn::class, 'o'],
                'site_id' => [SiteIdColumn::class, 'o'],
                'flag' => [S2pProjectsFlagColumn::class, 'p'],
                'pay_sys_id' => [S2pOrdersPaySysColumn::class, 'o'],
                'canonical_pay_sys_id' => [CanonicalPaySystemColumn::class, 'cps'],
                'pay_sys_type' => [S2pOrdersPaySysTypeColumn::class, 'o'],
                'pay_source' => [S2pOrdersPaySourceColumn::class, 'o'],
                'status' => [S2pOrdersStatusColumn::class, 'o'],
                'currency' => [S2pOrdersCurrencyColumn::class, 'o', 'title' => 'Currency (client)'],
                'ps_currency' => [S2pOrdersCurrencyColumn::class, ['o' => 'ps_original_currency'], 'title' => 'Currency (pay system)'],
                'mid_currency' => [S2pOrdersCurrencyColumn::class, ['o' => 'mid_currency'], 'title' => 'Currency (mid principal)'],
                'is_success' => [BooleanColumn::class, ['expr' => '(o.status = ' . S2pOrder::STATUS_SUCCESS . ')', 'o'], 'title' => 'Is success'],
                'status_id' => [S2pOrdersStatusIdColumn::class, 'o'],
                'type' => [S2pOrdersTypeColumn::class, 'o'],
                'is_first_success' => [IsFirstSuccessColumn::class, 'o'],
                'pay_type' => [S2pOrdersPayTypeColumn::class, 'o'],
                'requisite' => [RequisiteColumn::class, 'o'],
                'ip' => [IpColumn::class, 'o', 'decorateS2pLink' => true, 'isHtmlValue' => true],
                'country' => [CountryColumn::class, 'o'],
                'country_real' => [CountryRealColumn::class, ['ps', 'b'], 'binTable' => 'b', 'paySysTable' => 'ps', 'ordersTable' => 'o', 'title' => 'Country real'],
                'city' => [CityColumn::class, 'c'],
                'card_holder' => [S2pOrdersCardHolderColumn::class, 'o'],
                'device' => [S2pOrdersDeviceColumn::class, 'o', 'title' => 'Platform Group (S2P)'],
                'context' => [S2pOrdersContextColumn::class, 'o'],
                'processing_time_bucket' => [DateIntervalBucketColumn::class, ['o' => 'date'], 'compareTo' => 'o.process_at', 'title' => 'Processing time'],
                'comment' => [CommentColumn::class, 'o'],
                'comment_ps' => [CommentColumn::class, ['o' => 'comment_ps'], 'title' => 'Comment (pay sys)'],
                'initiated_by' => [EmailColumn::class, ['o' => 'initiated_by'], 'title' => 'Initiated by'],
                'approved_by' => [EmailColumn::class, ['o' => 'approved_by'], 'title' => 'Approved by'],
                'is_first' => [IsFirstTryColumn::class, 'o'],
                'instance_id' => [S2pInstanceColumn::class, 'o'],
                'pay_host' => [HostColumn::class, 'o', 'title' => 'Pay host'],
            ],

            'Dates' => $this->datesColumns($grouping),

            'Useragent (pay)' => $this->useragentColumns(),
            'Traffic (pay)' => $this->trafficColumns(),

            'Bin' => [
                'bin_system' => [BinGlobalPaysysColumn::class, 'b'],
                'bin_bank' => [BinBankColumn::class, 'b'],
                'bin_type' => [BinTypeColumn::class, 'b'],
                'bin_status' => [BinStatusColumn::class, 'b'],
                'bin_country' => [BinCountryColumn::class, 'b'],
                'bin_currency' => [BinCurrencyColumn::class, 'b'],
                'bin_card' => [BinColumn::class, 'o'],
            ],

            'User' => [
                'refcode' => [RefcodeColumn::class, 'r'],
                'traffic_source' => [TrafficSourceColumn::class, 'r'],
                'kyc' => [UserKycColumn::class, 'kyc'],
                'is_ignore' => [BooleanColumn::class, ['expr' => '(o.user_id IS NOT NULL)', 'o'], 'title' => 'Is ignore'],
                'reg_host' => [HostColumn::class, 'u'],
                'brand_id' => [BrandColumn::class, 'u'],
            ],
        ];

        if (!$grouping) {
            $columns['Order'] = array_merge($columns['Order'], [
                'amount_orig' => [S2pOrdersAmountColumn::class, 'o', 'title' => 'Amount orig'],
                'amount_eur' => [S2pOrdersAmountColumn::class, ['o' => 'summ_eur'], 'title' => 'Amount EUR (client)'],
                'amount_usd' => [S2pOrdersAmountColumn::class, ['o' => 'summ'], 'title' => 'Amount USD (client)'],
                'ps_amount_orig' => [MoneyColumn::class, ['o' => 'ps_original_amount'], 'title' => 'Amount orig (pay system)'],
                'mid_amount_orig' => [MoneyPreciseColumn::class, ['o' => 'mid_amount'], 'title' => 'Amount orig (mid principal)'],
            ]);

            $columns['User'] = array_merge($columns['User'], [
                'dep_first_at' => [DateColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Date (FD)'],
            ]);
        }

        if ($grouping) {
            $columns['First dep'] = [
                'fd_month' => [MonthColumn::class, ['usi' => 'dep_first_at']],
                'fd_week' => [WeekColumn::class, ['usi' => 'dep_first_at']],
                'fd_day' => [DayColumn::class, ['usi' => 'dep_first_at']],
                'fd_hour' => [HourColumn::class, ['usi' => 'dep_first_at']],
            ];

            $columns['User'] = array_merge($columns['User'], [
                'user_status' => [UserStatusColumn::class, 'u'],
            ]);
        }

        if ($this->authAccess->canViewS2pPayClasses()) {
            $columns['Order'] = array_merge($columns['Order'], [
                'pay_class_id' => [S2pOrdersPayClassColumn::class, 'o'],
                'pay_method_id' => [S2pOrdersPayMethodColumn::class, 'o'],
            ]);
        }

        if ($this->authAccess->canViewS2pMids()) {
            $columns['Order'] = array_merge($columns['Order'], [
                'mid_id' => [S2pOrdersMidColumn::class, 'o'],
            ]);
        }

        if ($this->authAccess->canViewS2pTrustScore()) {
            $columns['Order'] = array_merge($columns['Order'], [
                'trust_score' => [S2pOrdersTrustScoreColumn::class, 'o'],
                'trust_score_now' => [UsersS2pTrustScore::class, 'u'],
            ]);
        }

        if ($this->authAccess->canViewS2pTrustGroups()) {
            $columns['Order'] = array_merge($columns['Order'], [
                'trust_requisite' => [S2pOrdersTrustLevelColumn::class, ['o' => 'trust_requisite'], 'title' => 'Trust (requisite)'],
                'trust_user' => [S2pOrdersTrustLevelColumn::class, ['o' => 'trust_user'], 'title' => 'Trust (user)'],
                'trust_login' => [S2pOrdersTrustLevelColumn::class, ['o' => 'trust_login'], 'title' => 'Trust (login)'],
            ]);
        }

        if ($this->authAccess->canViewS2pRoutingBranch()) {
            $columns['Order']['routing_branch'] = [S2pOrdersRoutingBranchColumn::class, 'o'];
        }

        return $columns;
    }

    private function datesColumns(bool $grouping = false): array
    {
        if ($grouping) {
            return [
                'month' => [MonthColumn::class, 'o', 'title' => 'Month (mod)'],
                'week_number' => [WeekColumn::class, 'o', 'title' => 'Week (mod)', 'isPart' => true],
                'week' => [WeekColumn::class, 'o', 'title' => 'Week start (mod)'],
                'day' => [DayColumn::class, 'o', 'title' => 'Day (mod)'],
                'hour' => [HourColumn::class, 'o', 'title' => 'Hour (mod)'],
                'minute10' => [Minute10Column::class, 'o', 'title' => 'Minutes 10 (mod)'],
                'minute' => [MinuteColumn::class, 'o', 'title' => 'Minute (mod)'],

                'month_created' => [MonthColumn::class, ['o' => 'date_created'], 'title' => 'Month (cr)'],
                'week_number_created' => [WeekColumn::class, ['o' => 'date_created'], 'title' => 'Week (cr)', 'isPart' => true],
                'week_created' => [WeekColumn::class, ['o' => 'date_created'], 'title' => 'Week start (cr)'],
                'day_created' => [DayColumn::class, ['o' => 'date_created'], 'title' => 'Day (cr)'],
                'hour_created' => [HourColumn::class, ['o' => 'date_created'], 'title' => 'Hour (cr)'],
                'minute10_created' => [Minute10Column::class, ['o' => 'date_created'], 'title' => 'Minutes 10 (cr)'],

                'month_success' => [MonthColumn::class, ['o' => 'success_at'], 'title' => 'Month (suc)'],
                'week_number_success' => [WeekColumn::class, ['o' => 'success_at'], 'title' => 'Week (suc)', 'isPart' => true],
                'week_success' => [WeekColumn::class, ['o' => 'success_at'], 'title' => 'Week start (suc)'],
                'day_success' => [DayColumn::class, ['o' => 'success_at'], 'title' => 'Day (suc)'],
                'hour_success' => [HourColumn::class, ['o' => 'success_at'], 'title' => 'Hour (suc)'],
                'minute10_success' => [Minute10Column::class, ['o' => 'success_at'], 'title' => 'Minutes 10 (suc)'],

                'day_transaction' => [DayColumn::class, ['expr' => 'COALESCE(o.success_at, o.date_created)', 'o'], 'title' => 'Day (tr)'],
                'hour_transaction' => [HourColumn::class, ['expr' => 'COALESCE(o.success_at, o.date_created)', 'o'], 'title' => 'Hour (tr)'],

                'day_process' => [DayColumn::class, ['o' => 'date_created'], 'title' => 'Day (proc)'],
            ];
        }

        return [
            'date' => [DateColumn::class, 'o', 'title' => 'Date (mod)'],
            'date_created' => [DateColumn::class, ['o' => 'date_created'], 'title' => 'Date (cr)'],
            'date_process' => [DateColumn::class, ['o' => 'process_at'], 'title' => 'Date (proc)'],
            'date_success' => [DateColumn::class, ['o' => 'success_at'], 'title' => 'Date (suc)'],
            'date_transaction' => [DateColumn::class, ['expr' => 'COALESCE(o.success_at, o.date_created)', 'o'], 'title' => 'Date (tr)'],
        ];
    }

    private function trafficColumns(): array
    {
        return [
            'refcode_pay' => [RefcodeColumn::class, 'r_o'],
            'traffic_source_pay' => [TrafficSourceColumn::class, 'r_o'],
            'webmaster_id' => [WebmasterIdColumn::class, 'r_o'],
            'aff_owner' => [WebmasterAffOwnerColumn::class, 'w', 'refcodeTableAlias' => 'r_o'],
            'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'w', 'refcodeTableAlias' => 'r_o'],
            'publisher' => [MarketingTidPublisherColumn::class, 'tid'],
        ];
    }

    private function useragentColumns(): array
    {
        return [
            'useragent' => [UseragentColumn::class, 'uag'],
            'platform_id' => [UseragentPlatformColumn::class, 'uag'],
            'platform_group' => [UseragentPlatformGroupColumn::class, 'uagp'],
            'browser_id' => [UseragentBrowserColumn::class, 'uag'],
            'device_id' => [UseragentDeviceColumn::class, 'uag'],
            'variant_id' => [UseragentVariantColumn::class, 'uag'],
            'app_id' => [UseragentAppColumn::class, 'uag'],
            'app_group_id' => [UseragentAppGroupColumn::class, 'uaga'],
        ];
    }

    public function tableMap(): array
    {
        return [
            'o' => [S2pOrders::TABLE_NAME],
            'u' => [Users::TABLE_NAME, 'u.site_id = o.site_id AND u.user_id = o.user_id'],
            'kyc' => [UserKycs::TABLE_NAME, 'kyc.site_id = o.site_id AND kyc.user_id = o.user_id'],
            'ui' => [UserIgnoreIds::TABLE_NAME, 'ui.site_id = o.site_id AND ui.user_id = o.user_id'],
            'r' => [Refcodes::TABLE_NAME, 'r.id = u.refcode_id', ['u']],
            'p' => [S2pProjects::TABLE_NAME, 'p.id = o.project_id'],
            'b' => [Bins::TABLE_NAME, 'b.bin = ' . Bin::binExpressionOfRequisite()],
            'm' => [S2pMids::TABLE_NAME, 'm.id = o.mid_id'],
            'ps' => [S2pPaySystems::TABLE_NAME, 'ps.id = o.pay_sys_id'],
            'cps' => [CanonicalPaySySources::TABLE_NAME, 'cps.name = ps.code AND cps.source = ' . CanonicalPaySySource::SOURCE_S2P, ['ps']],
            'c' => [Cities::TABLE_NAME, 'c.id = o.city_id'],
            'usi' => [UserSpecialInfos::TABLE_NAME, 'usi.site_id = o.site_id AND usi.user_id = o.user_id'],
            'r_o' => [Refcodes::TABLE_NAME, 'r_o.id = o.refcode_id'],
            'tid' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r_o', 'tid'), ['r_o']],
            'w' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('w', 'r_o'), ['r_o']],
            'uag' => [Useragents::TABLE_NAME, 'uag.id = o.useragent_id'],
            'uaga' => [UseragentApps::TABLE_NAME, 'uaga.id = uag.app_id', ['uag']],
            'uagp' => [UseragentPlatforms::TABLE_NAME, 'uagp.id = uag.platform_id', ['uag']],
        ];
    }
}
