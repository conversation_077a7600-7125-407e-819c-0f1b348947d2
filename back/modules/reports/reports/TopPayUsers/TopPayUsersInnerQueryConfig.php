<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\TopPayUsers;

use app\back\entities\Rate;
use app\back\entities\UserTransaction;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateRangeColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UsersStatsDirColumn;
use app\back\modules\reports\columns\UsersStatsSumColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\UserTransactions;

class TopPayUsersInnerQueryConfig extends BaseQueryConfig
{
    private const array GROUP_MAP = [
        'site_id' => 'us.site_id',
    ];

    public function selects(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'us'],
            'dir' => [UsersStatsDirColumn::class, 'us'],
            'amount' => [UsersStatsSumColumn::class, 'us', 'currency' => $this->request->getFilter('currency') ?? Rate::EUR],
            'rank' => [TopPayUsersRankColumn::class, 'us', 'groups' => $this->request->groups(), 'groupsMap' => self::GROUP_MAP],
        ];
    }

    public function filters(): array
    {
        return [
            'date_range' => [DateRangeColumn::class, ['us' => 'updated_at']],
            'site_id' => [SiteIdColumn::class, 'us'],
            'currency' => [CurrencyColumn::class, 'us'],
            'ignore' => [IgnoreColumn::class, 'us'],
        ];
    }

    public function groups(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'us'],
            'dir' => [UsersStatsDirColumn::class, 'us'],
            'user_id' => [UserIdColumn::class, 'us'],
        ];
    }

    public function tableMap(): array
    {
        return [
            'us' => [UserTransactions::TABLE_NAME],
        ];
    }

    protected function beforeQuery(): void
    {
        $this->request->select('dir', 'rank', 'amount');
        $this->request->group('dir', 'user_id');

        $this->query->andWhere([
            'us.status' => UserTransaction::STATUS_SUCCESS,
            'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
        ]);
    }
}
