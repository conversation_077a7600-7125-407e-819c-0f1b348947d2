<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\StpEvents;

use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class StpEventCampaignColumn extends BaseColumn implements Selected, Filtered, Operators
{
    public string $title = 'Campaign';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->tableAlias . '.campaign';
    }

    public function inputProps(): array
    {
        return [

            'type' => 'text-area',
            'placeholder' => 'Use ⏎ for multiple values',
        ];
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $campaigns = Db::prepareDbEnumRegex(Str::explodeText($value), $operator === Operators::PREFIX);

        $query->andWhere(['~*', $this->selectExpression($db, $query), $campaigns]);
    }

    public function operators(): array
    {
        return [Operators::PREFIX, Operators::CONTAINS];
    }
}
