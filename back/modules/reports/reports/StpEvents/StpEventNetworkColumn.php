<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\StpEvents;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;
use app\back\repositories\StpNetworks;

class StpEventNetworkColumn extends BaseColumn implements Selected, Filtered, Decorated
{
    use FilterAndSelectDefault;

    public string $column = 'network_id';
    public string $title = 'Network';

    public function __construct(private readonly StpNetworks $stpNetworksRepo)
    {
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, $this->stpNetworksRepo->getNames()];
    }

    public function inputProps(): array
    {
        return [

            'type' => 'select',
            'list' => Arr::assocToIdName($this->stpNetworksRepo->getNames()),
        ];
    }

    public function decorate($value, array $row)
    {
        return $this->stpNetworksRepo->getNameById($value);
    }
}
