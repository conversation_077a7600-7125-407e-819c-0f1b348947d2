<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\PromoCodes;

use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class BonusUserPromoCodeErrorColumn extends BaseColumn implements Selected
{
    public string $title = 'Error';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return "{$this->tableAlias}.error";
    }
}
