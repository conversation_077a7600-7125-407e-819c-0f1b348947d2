<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Bonuses;

use app\back\components\helpers\DateHelper;
use app\back\entities\Rate;
use app\back\entities\UserIgnoreId;
use app\back\entities\UserTransaction;
use app\back\entities\UserWallet;
use app\back\modules\api\components\Operators;
use app\back\modules\reports\columns\BonusIdColumn;
use app\back\modules\reports\columns\BonusListNameColumn;
use app\back\modules\reports\columns\BonusNameColumn;
use app\back\modules\reports\columns\BonusUserProgressPrizeColumn;
use app\back\modules\reports\columns\BooleanColumn;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DateTimeColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\PercentColumn;
use app\back\modules\reports\columns\RefcodeColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserBonusEventNameColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\modules\reports\components\QueryRequest;
use app\back\repositories\Bonuses;
use app\back\repositories\BonusUserDeposits;
use app\back\repositories\Rates;
use app\back\repositories\Refcodes;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserWallets;
use Yiisoft\Db\Query\Query;

class BonusesConfig extends BaseReportConfig
{
    private const int UNION_KEY_PROGRESS = 1;
    private const int UNION_KEY_ACTIVITY = 2;

    private Query $unionQuery;

    protected function beforeQuery(): void
    {
        unset($this->unionQuery);
    }

    public function rules(): array
    {
        return [
            [['site_id', 'date'], 'required'],
            ['groups', fn() => 'Site ID must be selected', 'when' => fn() => $this->request->anyGroup('bonus_id', 'bonus_list_name', 'bonus_name') && !$this->request->anyGroup('site_id')],
            ['currency', function () {
                if (!$this->request->anyGroup('currency') && $this->request->anyMetric('bonus_sum', 'free_bet_sum', 'deposit_sum', 'deposit_avg')) {
                    if ($this->request->allEmptyFilter('currency')) {
                        return 'is required';
                    }
                    if ($this->request->getFilter('currency') === CurrencyColumn::ORIG) {
                        return 'please specify concrete currency';
                    }
                }
                return null;
            }]
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['site_id', []],
            ['date', DateHelper::weekBegin(), Operators::GE],
            ['currency', CurrencyColumn::ORIG],
            ['ignore', UserIgnoreId::MODE_IGNORE],
            ['groups', ['site_id', 'bonus_id', 'bonus_name', 'bonus_list_name', 'event_name', 'brand_id', 'prize', 'started_at', 'ended_at', 'enabled']],
            ['metrics', ['bonus_count', 'users_count', 'bonus_sum', 'free_bet_sum']],
        ];
    }

    public function filters(): array
    {
        return [
            'Main' => [
                'started_at' => [DateColumn::class, ['b' => 'started_at'], 'title' => 'Started at'],
                'ended_at' => [DateColumn::class, ['b' => 'ended_at'], 'title' => 'Ended at'],
                'date' => [DateTimeColumn::class, ['bu' => 'date', 'bup:nls', 'bua:nls']],
                'site_id' => [SiteIdColumn::class, ['bu', 'bup:nls', 'bua:nls']],
                'user_id' => [UserIdColumn::class, ['bu', 'bup:nls', 'bua:nls']],
                'bonus_id' => [BonusIdColumn::class, ['bu', 'bup:nls', 'bua:nls']],
                'brand_id' => [BrandColumn::class, 'u'],
                'event_name' => [UserBonusEventNameColumn::class, 'b'],
                'ignore' => [IgnoreColumn::class, 'bu'],
                'enabled' => [BooleanColumn::class, ['b' => 'enabled'], 'title' => 'Enabled'],
                'country' => [CountryColumn::class, 'u'],
                'currency' => [CurrencyColumn::class],
                'refcode_bonus' => [RefcodeColumn::class, 'rb'],
                'refcode_login' => [RefcodeColumn::class, 'rl', 'title' => 'Login refcode'],
            ],
        ];
    }

    public function columns(): array
    {
        return [];
    }

    public function metrics(): array
    {
        $filterProgress = $this->filterWhereUnionKey(self::UNION_KEY_PROGRESS);
        $filterActivity = $this->filterWhereUnionKey(self::UNION_KEY_ACTIVITY);
        $depsCountExpression = "COUNT(bud.dep_sum) $filterProgress";
        $activationsCountExpression = "COUNT(*) $filterActivity";
        return [
            'Main' => [
                'bonus_count' => [CountColumn::class, ['expr' => "COUNT(*) $filterProgress", ...$this->unionDeps(BonusForUnionQueryConfig::UNION_KEY_COLUMN, 'bup:n')], 'title' => 'Bonus count'],
                'users_count' => [CountColumn::class, ['expr' => "COUNT(DISTINCT CONCAT_WS('-', bu.site_id, bu.user_id)) $filterProgress", ...$this->unionDeps(BonusForUnionQueryConfig::UNION_KEY_COLUMN, 'bup:n')], 'title' => 'Uniq bonus users count'],
                'bonus_sum' => [MoneyColumn::class, ['expr' => "SUM(bu.bonus_sum * br.rate) $filterProgress", ...$this->unionDeps('bonus_sum'), 'br'], 'title' => 'Bonus balance sum'],
                'free_bet_sum' => [MoneyColumn::class, ['expr' => "SUM(bu.free_bet_sum * br.rate) $filterProgress", ...$this->unionDeps('free_bet_sum'), 'br'], 'title' => 'Free bet sum'],
                'deposit_sum' => [MoneyColumn::class, ['expr' => "SUM(bud.dep_sum) $filterProgress", ...$this->unionDeps('bonus_user_progress_id'), 'bud'], 'title' => 'Deposit sum'],
                'deposit_avg' => [MoneyColumn::class, ['expr' => "SUM(bud.dep_avg)/COUNT(bud.dep_avg) $filterProgress", ...$this->unionDeps('bonus_user_progress_id'), 'bud'], 'title' => 'Deposit avg'],
                'unique_activations' => [CountColumn::class, ['expr' => "COUNT(DISTINCT CONCAT_WS('-', bu.site_id, bu.user_id, bu.bonus_id)) $filterActivity", ...$this->unionDeps(BonusForUnionQueryConfig::UNION_KEY_COLUMN, 'bua:n')], 'title' => 'Unique program (Bonus ID) activations'],
                'complete_rate' => [PercentColumn::class, ['expr' => "($depsCountExpression)::float/NULLIF($activationsCountExpression, 0)*100", ...$this->unionDeps('bonus_user_progress_id'), 'bud'], 'title' => 'Programs (Bonus ID) completed rate'],
            ],
        ];
    }

    private function unionDeps(string $column, ?string $forceUnion = null): array
    {
        $deps = ['bu:n'];

        if ($forceUnion) {
            $deps[$forceUnion] = BonusForUnionQueryConfig::UNION_KEY_COLUMN;
            if ($column === BonusForUnionQueryConfig::UNION_KEY_COLUMN) {
                return $deps;
            }
        }

        return [
            'bup:n' => $column,
            'bua:n' => $column,
            ...$deps,
        ];
    }

    public function groups(): array
    {
        return [
            'Main' => [
                'site_id' => [SiteIdColumn::class, 'bu'],
                'user_id' => [UserIdColumn::class, 'bu'],
                'bonus_id' => [BonusIdColumn::class, 'bu', 'isHtmlValue' => true, 'decorateWithBonusLink' => true],
                'bonus_name' => [BonusNameColumn::class, 'b'],
                'bonus_list_name' => [BonusListNameColumn::class, 'b'],
                'event_name' => [UserBonusEventNameColumn::class, 'b'],
                'brand_id' => [BrandColumn::class, 'u'],
                'prize' => [BonusUserProgressPrizeColumn::class, ['bu' => 'prize', 'bup:nls', 'bua:nls']],
                'started_at' => [DateColumn::class, ['b' => 'started_at'], 'title' => 'Started at'],
                'ended_at' => [DateColumn::class, ['b' => 'ended_at'], 'title' => 'Ended at'],
                'enabled' => [BooleanColumn::class, ['b' => 'enabled'], 'title' => 'Enabled'],
                'currency' => [CurrencyColumn::class, 'uw'],
                'month' => [MonthColumn::class, ['bu' => 'date']],
                'day' => [DayColumn::class, ['bu' => 'date']],
                'country' => [CountryColumn::class, 'u'],
                'refcode_bonus' => [RefcodeColumn::class, 'rb'],
                'refcode_login' => [RefcodeColumn::class, 'rl', 'title' => 'Login refcode'],
            ],
        ];
    }

    public function tableMap(): array
    {
        return [
            'bup' => [fn(Query $q, QueryRequest $nestedRequest) => $this->lazyUnion(self::UNION_KEY_PROGRESS, BonusUserProgressBonusForUnionQueryConfig::class, $nestedRequest)],
            'bua' => [fn(Query $q, QueryRequest $nestedRequest) => $this->lazyUnion(self::UNION_KEY_ACTIVITY, BonusUserActivityBonusForUnionQueryConfig::class, $nestedRequest)],
            'bu' => [fn() => $this->unionQuery],
            'bud' => [$this->depositQuery(...), $this->unionKeyCondition(self::UNION_KEY_PROGRESS), [], 'LEFT JOIN LATERAL'],
            'b' => [Bonuses::TABLE_NAME, 'bu.site_id = b.site_id AND bu.bonus_id = b.bonus_id'],
            'u' => [Users::TABLE_NAME, 'u.site_id = bu.site_id AND u.user_id = bu.user_id'],
            'uw' => [
                fn() => (new Query($this->db))
                    ->select(['currency' => 'uw.currency'])
                    ->from(['uw' => UserWallets::TABLE_NAME])
                    ->where(['AND',
                        ['uw.type' => UserWallet::TYPE_BONUS],
                        UserWallets::getActiveCondition('uw'),
                        'bu.site_id = uw.site_id AND bu.user_id = uw.user_id'
                    ])
                    ->limit(1),
                'true', ['bu'], 'LEFT JOIN LATERAL'
            ],
            'br' => [fn() => Rates::ratesQuery($this->db, 'bu.date', 'uw.currency', $this->request->getFilter('currency')), $this->unionKeyCondition(self::UNION_KEY_PROGRESS), ['uw'], 'LEFT JOIN LATERAL'],
            'rb' => [Refcodes::TABLE_NAME, 'rb.id = b.refcode_id', ['b']],
            'rl' => [Refcodes::TABLE_NAME, 'rl.id = b.login_refcode_id', ['b']],
        ];
    }

    private function lazyUnion(int $unionKey, string $queryConfigClass, QueryRequest $nestedRequest): Query
    {
        $queryConfig = (new ($queryConfigClass)($this->container));

        if (!$queryConfig instanceof BonusForUnionQueryConfig) {
            throw new \LogicException("Invalid query config class $queryConfigClass");
        }

        if (empty(array_diff($nestedRequest->selects(), $queryConfig::PRIMARY_SELECTS))) {
            return (new Query($this->db));
        }

        $queryConfig->unionKey = $unionKey;

        if (isset($this->unionQuery)) {
            $this->unionQuery->union($queryConfig->query($this->db, $nestedRequest), true);
        } else {
            $this->unionQuery = $queryConfig->query($this->db, $nestedRequest);
        }

        return $this->unionQuery;
    }

    private function depositQuery(): Query
    {
        $depCol = UserTransaction::AMOUNT_COLUMN_BY_CURRENCY[$this->request->getFilter('currency') ?? Rate::ORIG];

        return (new Query($this->db))
            ->select([
                'dep_sum' => "SUM(us.$depCol)",
                'dep_avg' => "SUM(us.$depCol)/COUNT(us.*)",
            ])
            ->from(['bud' => BonusUserDeposits::TABLE_NAME])
            ->innerJoin(['us' => UserTransactions::TABLE_NAME], 'us.site_id = bud.site_id AND us.transaction_id = bud.transaction_id')
            ->where('bud.site_id = bu.site_id AND bud.bonus_user_progress_id = bu.bonus_user_progress_id')
            ->groupBy(['bu.site_id', 'bu.bonus_user_progress_id']);
    }

    private function filterWhereUnionKey(int $unionKey): string
    {
        return "FILTER ( WHERE {$this->unionKeyCondition($unionKey)} )";
    }

    private function unionKeyCondition(int $unionKey): string
    {
        return "bu." . BonusForUnionQueryConfig::UNION_KEY_COLUMN . " = $unionKey";
    }
}
