<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\NetRevenue;

use app\back\components\helpers\DateHelper;
use app\back\entities\Rate;
use app\back\entities\UserGameToken;
use app\back\entities\UserIgnoreId;
use app\back\modules\api\clients\crm\filter\NetRevenueMethod;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CountUniqUsersColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\GameTypeGroupColumn;
use app\back\modules\reports\columns\HourColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\UserGameTokenBalanceTypeColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\WebmasterIdColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\modules\reports\components\QueryRequest;
use app\back\modules\reports\reports\Games\UserGameTokenSessionTypeColumn;
use Yiisoft\Db\Query\Query;

/** @see NetRevenueMethod same logic */
class NetRevenueConfig extends BaseReportConfig
{
    public function rules(): array
    {
        return [
            [['date', 'currency', 'metrics'], 'required'],
            ['site_id', 'required', 'when' => fn() => $this->request->anyEmptyFilter('site_user')],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', DateHelper::monthBegin(), '>='],
            ['date', DateHelper::today(), '<='],
            ['site_id', []],
            ['ignore', UserIgnoreId::MODE_IGNORE],
            ['currency', Rate::EUR],
            ['balance_type', [UserGameToken::BALANCE_TYPE_REAL]],
            ['groups', ['day']],
            ['metrics', ['bet_count', 'bet_sum', 'win_sum', 'payout', 'profit', 'net_revenue', 'admin_in_prize', 'de_fee', 'admin_compensation', 'bonus_transfer', 'active_users']],
        ];
    }

    public function filters(): array
    {
        // Common filters. Same must be in NetRevenuePaymentsConfig
        $commonFilters = array_map(static fn($c) => [$c, ['ug:nl', 'us:nl']], [
            'date' => DateColumn::class,
            'site_id' => SiteIdColumn::class,
            'user_id' => UserIdColumn::class,
            'site_user' => SiteUserColumn::class,
            'ignore' => IgnoreColumn::class,
            'currency' => CurrencyColumn::class,
            'country' => CountryColumn::class,
            'webmaster_id' => WebmasterIdColumn::class,
        ]);

        // Games specific filters
        $gamesFilters = array_map(static fn($c) => [$c, 'ug:nl'], [
            'balance_type' => UserGameTokenBalanceTypeColumn::class,
            'session_type' => UserGameTokenSessionTypeColumn::class,
            'game_type_group' => GameTypeGroupColumn::class,
        ]);

        return [
            'Main' => [
                ...$commonFilters,
                ...$gamesFilters,
            ],
        ];
    }

    public function columns(): array
    {
        return [];
    }

    public function metrics(): array
    {
        return [
            'Games' => [
                'bet_count' => [CountColumn::class, ['expr' => 'ug.bet_count', 'ug:n'], 'title' => 'Spin'],
                'win_count' => [CountColumn::class, ['expr' => 'win_count', 'ug:n'], 'title' => 'Win count'],
                'bet_sum' => [MoneyColumn::class, ['expr' => 'ug.bet_sum', 'ug:n'], 'title' => 'Bet sum'],
                'win_sum' => [MoneyColumn::class, ['expr' => 'ug.win_sum', 'ug:n'], 'title' => 'Win sum'],
                'payout' => [MoneyColumn::class, ['expr' => 'ug.win_sum', 'ug:n'], 'title' => 'Payout'],
                'profit' => [MoneyColumn::class, ['expr' => 'ug.profit', 'ug:n'], 'title' => 'Profit'],
                'active_users' => [CountUniqUsersColumn::class, ['expr' => 'ug.active_users', 'ug:n'], 'title' => 'Active users'],
            ],
            'Games Extra' => [
                'bet_sum_real' => [MoneyColumn::class, ['expr' => "bet_sum_real", 'ug:n'], 'title' => 'Bet sum (real)'],
                'bet_sum_bonus' => [MoneyColumn::class, ['expr' => "bet_sum_bonus", 'ug:n'], 'title' => 'Bet sum (bonus)'],
                'avg_bet' => [MoneyColumn::class, ['expr' => "avg_bet", 'ug:n'], 'title' => 'Avg bet'],
                'avg_win' => [MoneyColumn::class, ['expr' => "avg_win", 'ug:n'], 'title' => 'Avg win'],
                'atpu' => [MoneyColumn::class, ['expr' => "atpu", 'ug:n'], 'title' => 'ATPU'],
            ],
            'Payments' => [
                'admin_in_prize' => [MoneyColumn::class, ['expr' => 'us.admin_in_prize', 'us:n'], 'title' => 'Admin in prize'],
                'de_fee' => [MoneyColumn::class, ['expr' => 'us.de_fee', 'us:n'], 'title' => 'DE fee'],
                'admin_compensation' => [MoneyColumn::class, ['expr' => 'us.admin_compensation', 'us:n'], 'title' => 'Admin compensation'],
                'bonus_transfer' => [MoneyColumn::class, ['expr' => 'us.bonus_transfer', 'us:n'], 'title' => 'Bonus transfer'],
            ],
            'Combined' => [
                'net_revenue' => [MoneyColumn::class, ['expr' => "ug.bet_sum - COALESCE(ug.win_sum, 0) - COALESCE(us.bonus_transfer, 0) - COALESCE(us.de_fee, 0) - COALESCE(us.admin_compensation, 0)", 'ug:n' => ['bet_sum', 'win_sum'], 'us:n' => ['bonus_transfer', 'de_fee', 'admin_compensation']], 'title' => 'Net revenue'],
            ],
            'Payments by game group' => [
                'in_total' => [MoneyColumn::class, ['expr' => 'us.in_total', 'us:n'], 'title' => 'In (total)'],
                'in_casino' => [MoneyColumn::class, ['expr' => 'us.in_casino', 'us:n'], 'title' => 'In (casino)'],
                'in_betting' => [MoneyColumn::class, ['expr' => 'us.in_betting', 'us:n'], 'title' => 'In (betting)'],
                'in_mixed' => [MoneyColumn::class, ['expr' => 'us.in_mixed', 'us:n'], 'title' => 'In (mixed)'],

                'out_total' => [MoneyColumn::class, ['expr' => 'us.out_total', 'us:n'], 'title' => 'Out (total)'],
                'out_casino' => [MoneyColumn::class, ['expr' => 'us.out_casino', 'us:n'], 'title' => 'Out (casino)'],
                'out_betting' => [MoneyColumn::class, ['expr' => 'us.out_betting', 'us:n'], 'title' => 'Out (betting)'],
                'out_mixed' => [MoneyColumn::class, ['expr' => 'us.out_mixed', 'us:n'], 'title' => 'Out (mixed)'],

                'in_out_total' => [MoneyColumn::class, ['expr' => 'NULLIF(COALESCE(us.in_total, 0) - COALESCE(us.out_total, 0), 0)', 'us:n' => ['in_total', 'out_total']], 'title' => 'In-Out (total)'],
                'in_out_casino' => [MoneyColumn::class, ['expr' => 'NULLIF(COALESCE(us.in_casino, 0) - COALESCE(us.out_casino, 0), 0)', 'us:n' => ['in_casino', 'out_casino']], 'title' => 'In-Out (casino)'],
                'in_out_betting' => [MoneyColumn::class, ['expr' => 'NULLIF(COALESCE(us.in_betting, 0) - COALESCE(us.out_betting, 0), 0)', 'us:n' => ['in_betting', 'out_betting']], 'title' => 'In-Out (betting)'],
                'in_out_mixed' => [MoneyColumn::class, ['expr' => 'NULLIF(COALESCE(us.in_mixed, 0) - COALESCE(us.out_mixed, 0), 0)', 'us:n' => ['in_mixed', 'out_mixed']], 'title' => 'In-Out (mixed)'],
            ],
            'Games by group' => [
                'bets_casino' => [MoneyColumn::class, ['expr' => "ug.bets_casino", 'ug:n'], 'title' => 'Bet sum (casino)'],
                'bets_betting' => [MoneyColumn::class, ['expr' => "ug.bets_betting", 'ug:n'], 'title' => 'Bet sum (betting)'],

                'wins_casino' => [MoneyColumn::class, ['expr' => "ug.wins_casino", 'ug:n'], 'title' => 'Win sum (casino)'],
                'wins_betting' => [MoneyColumn::class, ['expr' => "ug.wins_betting", 'ug:n'], 'title' => 'Win sum (betting)'],

                'bets_wins_casino' => [MoneyColumn::class, ['expr' => "ug.bets_wins_casino", 'ug:n'], 'title' => 'Bet-Win sum (casino)'],
                'bets_wins_betting' => [MoneyColumn::class, ['expr' => "ug.bets_wins_betting", 'ug:n'], 'title' => 'Bet-Win sum (betting)'],
            ],
        ];
    }

    public function groups(): array
    {
        $nestedColumns = [
            'site_id' => SiteIdColumn::class,
            'user_id' => UserIdColumn::class,
            'site_user' => SiteUserColumn::class,
            'month' => MonthColumn::class,
            'week' => WeekColumn::class,
            'day' => DayColumn::class,
            'hour' => HourColumn::class,
            'country' => CountryColumn::class,
            'webmaster_id' => WebmasterIdColumn::class,
        ];

        foreach ($nestedColumns as $k => &$c) {
            $c = [$c, ['expr:s' => "COALESCE(ug.{$k}, us.{$k})", 'ug:nl', 'us:nl']];
        }

        return [
            'Main' => [
                ...$nestedColumns,
                'game_type_group' => [GameTypeGroupColumn::class, ['ug:nl']],
            ],
        ];
    }

    public function tableMap(): array
    {
        $connections = [
            'site_id',
            'user_id',
            'site_user',
            'month',
            'week',
            'day',
            'hour',
            'country',
            'webmaster_id',
        ];

        $usJoinConditions = [];

        foreach ($this->request->groups as $column) {
            if (in_array($column, $connections, true)) {
                $usJoinConditions[] = "us.$column = ug.$column";
            }
        }

        if (count($usJoinConditions) > 0) {
            array_unshift($usJoinConditions, 'AND');
        } else {
            $usJoinConditions = 'true';
        }

        return [
            'ug' => [function (Query $query, QueryRequest $nestedRequest) {
                $params = $query->getParams(); // Prevent numeric placeholder for overwrite in another nested query
                $query->withQuery((new NetRevenueGamesConfig($this->container))->query($this->db, $nestedRequest, $params), 'ug');
                $query->params($params);
                return 'ug';
            }],
            'us' => [function (Query $query, QueryRequest $nestedRequest) {
                $params = $query->getParams(); // Prevent numeric placeholder for overwrite in another nested query
                $query->withQuery((new NetRevenuePaymentsConfig($this->container))->query($this->db, $nestedRequest, $params), 'us');
                $query->params($params);
                return 'us';
            }, $usJoinConditions, [], 'FULL JOIN'],
        ];
    }
}
