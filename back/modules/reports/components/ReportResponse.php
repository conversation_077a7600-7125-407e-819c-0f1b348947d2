<?php

declare(strict_types=1);

namespace app\back\modules\reports\components;

use app\back\components\helpers\Arr;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\Decorated;

class ReportResponse
{
    public const string TYPE_TABLE = 'table';
    public const string TYPE_CHART = 'chart';
    public const string TYPE_TEXT = 'text';

    public function __construct(
        private readonly BaseReportConfig $reportConfig
    ) {
    }

    public function results(): array
    {
        if ($this->reportConfig->showSql) {
            BaseQueryConfig::enableLog();
            return [
                $this->text($this->reportConfig->queryString() . "\n" . BaseQueryConfig::logs())
            ];
        }

        return $this->blocks();
    }

    private function blocks(): array
    {
        ['data' => $data, 'columns' => $columns] = $this->reportConfig->dataAndColumns(false);

        $blockColumnName = $this->reportConfig->getRequest()->block;
        if ($blockColumnName && $this->reportConfig->getRequest()->isTotals) {
            unset($columns[$blockColumnName]);
            $blockedData = Arr::groupBy($data, [$blockColumnName]);

            if ($this->reportConfig->blocksSortOrder) {
                $blockedData = Arr::sortByKeys($blockedData, $this->reportConfig->blocksSortOrder);
            }

            if (empty($blockedData)) {
                $blockedData = ['' => []]; // To show empty result
            }
        } else {
            // Fake 1 block
            $blockedData = ['' => $data];
        }

        $blocks = [];
        foreach ($blockedData as $title => $data) {
            if ($this->reportConfig->getRequest()->isChart) {
                $blocks[] = $this->chart($title, $data, $columns);
            } else {
                $blocks[] = $this->table($title, $data, $columns);
            }
        }

        return $blocks;
    }

    private function table(string|int $title, array $data, array $columns): array
    {
        $columns = array_values(array_map(static function (string $columnName, BaseColumn $col) {
            /** @see front/components/entity-table/table.vue rowCellProps */
            $c = [
                'code' => $columnName,
                'name' => strip_tags($col->title),
                'sortable' => true,
            ];

            if ($col->isHtmlValue) {
                $c['raw'] = true;
            }

            if ($col->align) {
                $c['align'] = $col->align;
            }

            return $c;
        }, array_keys($columns), $columns));

        return [
            'title' => strip_tags((string)$title),
            'type' => self::TYPE_TABLE,
            'table' => ['data' => $data, 'columns' => $columns],
        ];
    }

    public function text(string $text): array
    {
        return [
            'type' => self::TYPE_TEXT,
            'text' => $text,
        ];
    }

    public function chart(string|int $title, array $data, array $columns): array
    {
        $xAxisColumnName = $this->reportConfig->getRequest()->groups[0];
        /** @var BaseColumn|Decorated $xColumn */
        $xColumn = $columns[$xAxisColumnName];
        $xAxisType = method_exists($xColumn, 'chartXAxisType') ? $xColumn->chartXAxisType() : 'category';

        $notMetricColumns = array_filter(array_merge($this->reportConfig->getRequest()->groups, [$this->reportConfig->getRequest()->block, $this->reportConfig->getRequest()->split]));
        $metrics = array_diff_key($columns, array_flip($notMetricColumns));

        $series = [];
        foreach ($metrics as $metricKey => $metricColumn) {
            /** @var BaseColumn $metricColumn */

            $series[] = [
                'name' => $metricColumn->title,
                'data' => self::serieData($xAxisColumnName, $data, $metricKey, $xColumn, $xAxisType),
            ];
        }

        return [
            'type' => self::TYPE_CHART,
            'title' => $title,
            'chart' => [
                'credits' => [
                    'enabled' => false,
                ],
                'title' => [
                    'text' => '',
                ],
                'tooltip' => [
                    'xDateFormat' => '%Y-%m-%d %H:%M:%S',
                    'shared' => true,
                ],
                'legend' => [
                    'enabled' => true,
                ],
                'xAxis' => [
                    'type' => $xAxisType,
                    'title' => false,
                ],
                'yAxis' => [
                    'title' => false,
                ],
                'series' => $series,
            ],
        ];
    }

    private static function serieData(string|int $xName, array $rows, string|int $yName, BaseColumn|Decorated $xColumn, string $xAxisType): array
    {
        $xColumnDecorable = $xColumn instanceof Decorated;
        $isDateTime = $xAxisType === 'datetime';

        $serieData = [];
        foreach ($rows as $row) {
            if (!isset($row[$yName])) {
                continue;
            }

            $x = $row[$xName];

            $xFinal = match (true) {
                $isDateTime => strtotime($x) * 1000,
                $xColumnDecorable => $xColumn->decorate($x, []),
                default => $x,
            };

            $y = round((float)$row[$yName], 2);
            if (floor($y) === ceil($y)) {
                $y = (int)$y;
            }

            $serieData[] = [$xFinal, $y];
        }

        return $serieData;
    }
}
