<?php

declare(strict_types=1);

namespace app\back\modules\reports\components;

use app\back\components\Container;
use app\back\components\exceptions\InvalidException;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Str;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\FilteredLarge;
use app\back\modules\reports\columns\Grouped;
use app\back\modules\reports\columns\Input;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\Ordered;
use app\back\modules\reports\columns\RootQueryFiltered;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Expression\ExpressionInterface;
use Yiisoft\Db\Query\Query;

abstract class BaseQueryConfig
{
    use UseTableTrait;

    protected ConnectionInterface $db;
    protected Query $query;
    protected ColumnQuery $columnQuery;
    protected QueryRequest $request;

    private array $nestedRequests;
    private array $selectedColumnConfigs;
    private array $columnsQueryClosures;
    private array $columnsRootQueryClosures;
    private array $filtersLargeQueryClosures;
    private array $filtersTableMapDependencies;
    private array $orQueries;
    private static array $logs = [];
    private static bool $logEnabled = false;

    abstract public function selects(): array;
    abstract public function groups(): array;
    abstract public function filters(): array;
    abstract public function tableMap(): array;

    public function __construct(protected Container $container)
    {
    }

    public function query(ConnectionInterface $db, QueryRequest $request, array &$params = []): Query
    {
        $this->useTables = [];
        $this->nestedRequests = [];
        $this->selectedColumnConfigs = [];
        $this->columnsQueryClosures = [];
        $this->columnsRootQueryClosures = [];
        $this->filtersLargeQueryClosures = [];
        $this->filtersTableMapDependencies = [];
        $this->orQueries = [];
        $this->db = $db;
        $this->request = $request;
        $this->query = new Query($db);
        $this->query->params($params);
        $this->columnQuery = new ColumnQuery();

        $this->beforeQuery();
        $this->compose();
        $params = $this->query->getParams();

        return $this->query;
    }

    protected function beforeQuery(): void
    {
    }

    protected function initColumn(BaseColumn $column): void
    {
    }

    private function compose(): void
    {
        self::log("\nCompose {$this->name()}:");

        foreach ($this->sqlConfigs() as $config) {
            match ($config->type) {
                SqlConfig::TYPE_COLUMN => $this->collectColumnSql($config),
                SqlConfig::TYPE_JOIN => $this->addTableSql($config),
                SqlConfig::TYPE_NESTED => $this->addNestedSql($config),
            };
        }

        foreach ($this->getColumnsOrderConfigs() as [$config, $direction]) {
            $this->addOrderBySql($config, $direction);
        }

        $this->applyTablesAndNestedConfigSql();
        $this->applyColumnsSql();

        if (empty($this->query->getSelect())) {
            throw new \LogicException("{$this->name()} empty select columns");
        }
    }

    private function applyColumnsSql(): void
    {
        array_walk($this->columnsRootQueryClosures, static fn($closure) => $closure());
        $this->columnQuery->addParams($this->query->getParams());
        array_walk($this->columnsQueryClosures, static fn($closure) => $closure());

        $this->query->params($this->columnQuery->getParams());

        $where = $this->columnQuery->getWhere();
        if (!empty($where)) {
            $this->query->andWhere($where);
        }

        $having = $this->columnQuery->getHaving();
        if (!empty($having)) {
            $this->query->andHaving($having);
        }

        $where = [];
        $having = [];
        /** @var Query $orQuery */
        foreach ($this->orQueries as $orQuery) {
            $whereCur = $orQuery->getWhere();
            if ($whereCur !== null) {
                $where[] = $whereCur;
            }
            $havingCur = $orQuery->getHaving();
            if ($havingCur !== null) {
                $having[] = $havingCur;
            }
        }

        if (!empty($where)) {
            array_unshift($where, 'OR');
            $this->query->andWhere($where);
        }

        if (!empty($having)) {
            array_unshift($having, 'OR');
            $this->query->andHaving($having);
        }
    }

    private function collectColumnSql(SqlConfig $sqlConfig): void
    {
        $sqlConfig->applyModifiers([
            SqlConfig::MOD_SELECT => fn() => $this->selectColumn($sqlConfig),
            SqlConfig::MOD_GROUP => fn() => $this->groupByColumn($sqlConfig),
            SqlConfig::MOD_FILTER => fn() => $this->filterColumn($sqlConfig),
        ]);
        !isset($sqlConfig->expression) && $this->addTableSql($sqlConfig);
    }

    private function addNestedSql(SqlConfig $sqlConfig): void
    {
        $usageInfo = SqlConfig::MODS[$sqlConfig->defaultModifier];
        self::log("Use {$sqlConfig->info} on $usageInfo '$sqlConfig->columnName'");

        $nestedRequest = $this->nestedRequest($sqlConfig->tableAlias);
        $sqlConfig->applyModifiers([
            SqlConfig::MOD_SELECT => fn() => $nestedRequest->select(...$sqlConfig->columns),
            SqlConfig::MOD_GROUP => fn() => $nestedRequest->group(...$sqlConfig->columns),
            SqlConfig::MOD_FILTER => function () use ($sqlConfig, $nestedRequest) {
                foreach ($sqlConfig->columns as $filterName) {
                    foreach ($this->request->getColumnFilters($filterName) as [$value, $operator, $orBlock]) {
                        $nestedRequest->addFilter($filterName, $value, $operator, $orBlock);
                    }
                }
            },
        ]);
        !$sqlConfig->isLazy && $this->addTableSql($sqlConfig);
    }

    private function selectColumn(SqlConfig $config): void
    {
        self::log("Select '$config->columnName'");
        $this->selectedColumnConfigs[$config->columnName] = $config;
        $this->query->addSelect([$config->columnName => $this->customOrSelectExpression($config)]);
    }

    private function groupByColumn(SqlConfig $config): void
    {
        self::log("Group by '$config->columnName'");

        if ($config->column instanceof Grouped) {
            /** @var Grouped $col */
            $col = $config->column;
            $this->columnsQueryClosures[] = fn() => $this->query->addSelect($col->groupingSelectExpression($this->db, $this->columnQuery));
            $expression = $config->expression ?? $config->column->groupByExpression($this->db, $this->columnQuery);
        } elseif ($config->column instanceof Selected) {
            $expression = $config->expression ?? $config->column->selectExpression($this->db, $this->columnQuery);
        } else {
            throw new InvalidException("{$this->name()} column {$config->columnName} not implemented as Selected or Grouped!");
        }

        $this->query->addGroupBy([$expression]);

        if ($config->column instanceof Ordered) {
            // apply after custom orders
            $this->columnsQueryClosures[] = fn() => $this->query->addOrderBy([$expression => SORT_ASC]);
        }
    }

    private function filterColumn(SqlConfig $config): void
    {
        $column = $config->column;

        if (!$column instanceof Input) {
            throw new InvalidException("{$this->name()} column {$config->columnName} not implemented as Input!");
        }

        $filters = $this->request->getColumnFilters($config->columnName);

        if (empty($filters)) {
            throw new \LogicException("{$this->name()} column $config->columnName empty filter used");
        }

        if ($column instanceof RootQueryFiltered) {
            [$value, $operator] = reset($filters);
            $this->columnsRootQueryClosures[] = fn() => $column->filter($this->db, $this->query, $value, $operator);
            return;
        }

        if (!$column instanceof Filtered) {
            return;
        }

        self::log("Filter '$config->columnName'");

        foreach ($filters as [$value, $operator, $orBlock]) {
            if ($orBlock === 0) {
                $query = $this->columnQuery;
            } else {
                $this->orQueries[$orBlock] ??= new ColumnQuery($this->columnQuery->getParams());
                $query = $this->orQueries[$orBlock];
            }
            $this->addFilterClosure($config->columnName, $column, $value, $query, $operator, $orBlock);
        }
    }

    private function addFilterClosure(string $filterName, Filtered $column, $value, ColumnQuery $query, $operator, $orBlock): void
    {
        if ($column instanceof FilteredLarge && is_string($value) && strlen($value) > $column::LARGER_VALUE_LENGTH_LIMIT) {
            if ($orBlock > 0) {
                throw new InvalidException("Filter $filterName 'OR' blocks not supported with large params");
            }
            if (!is_null($operator) && $operator !== Operators::IN) {
                throw new InvalidException("Filter $filterName operator '$operator' not supported with large params");
            }
            if (Str::containsLine(BaseColumn::BLANK_FAKE_VALUE, $value)) {
                throw new InvalidException("Filter $filterName [blank] not supported with large params");
            }
            $this->filtersLargeQueryClosures[$filterName][] = fn() => $column->filterLarge($this->db, $this->query, $value, $query);
        } else {
            $this->columnsQueryClosures[] = fn() => $column->filter($this->db, $query, $value, $operator);
        }
    }

    private function addOrderBySql(SqlConfig $config, int $direction): void
    {
        self::log("Order by '$config->columnName'");
        $expression = $this->customOrSelectExpression($config);
        if ($expression instanceof ExpressionInterface) {
            throw new \LogicException("{$this->name()} unable to order by column $config->columnName by Query");
        }
        $order = new Expression($expression . ' ' . ($direction === SORT_DESC ? 'DESC' : 'ASC') . '  NULLS LAST');
        $this->query->addOrderBy([$order]);
    }

    private function getColumnsOrderConfigs(): array
    {
        $ordersDirections = $this->request->orders();
        $invalidOrderColumns = array_diff_key($ordersDirections, $this->selectedColumnConfigs);
        if (!empty($invalidOrderColumns)) {
            $invalidOrderColumns = implode(', ', array_keys($invalidOrderColumns));
            throw new \LogicException("{$this->name()} order by column(s) $invalidOrderColumns must by selected");
        }

        $configs = array_intersect_key($this->selectedColumnConfigs, $ordersDirections);
        return array_map(static fn(SqlConfig $config) => [$config, $ordersDirections[$config->columnName]], $configs);
    }

    /**
     * @return SqlConfig[]
     */
    private function sqlConfigs(): array
    {
        $configsGrouped = [
            SqlConfig::MOD_SELECT => [$this->selects(), $this->request->selects()],
            SqlConfig::MOD_GROUP => [$this->groups(), $this->request->groups()],
            SqlConfig::MOD_FILTER => [$this->filters(), $this->request->filters()],
        ];

        $configs = [];

        /** @var string $defaultMod */
        foreach ($configsGrouped as $defaultMod => [$rawConfigs, $columnsNames]) {
            $rawConfigs = Arr::leaveOnlyKeys($rawConfigs, $columnsNames);
            $notExistsColumns = implode(', ', array_diff($columnsNames, array_keys($rawConfigs)));
            if (!empty($notExistsColumns)) {
                $modInfo = SqlConfig::MODS[$defaultMod];
                throw new \LogicException("Column \"$notExistsColumns\" not found in {$this->name()}::$modInfo block");
            }

            foreach ($rawConfigs as $colName => $rawConfig) {
                foreach (SqlConfig::parse($this->container, $this->name(), $colName, $defaultMod, $rawConfig) as $sqlConfig) {
                    if (isset($sqlConfig->column)) {
                        $this->initColumn($sqlConfig->column);
                        if (in_array(SqlConfig::MOD_FILTER, $sqlConfig->mods, true)) {
                            $this->filtersTableMapDependencies[$sqlConfig->columnName][] = $sqlConfig->tableAlias;
                        }
                    }
                    $configs[] = $sqlConfig;
                }
            }
        }

        return $configs;
    }

    private function addTableSql(SqlConfig $config): void
    {
        $usageMask = SqlConfig::MODS[$config->defaultModifier];
        !isset($this->useTables[$config->tableAlias]) && self::log("Use table '$config->tableAlias' on {$usageMask} '$config->columnName'");
        $this->useTable($config->tableAlias);
    }

    private function customOrSelectExpression(SqlConfig $config): string | ExpressionInterface
    {
        if (!$config->column instanceof Selected) {
            throw new InvalidException("{$this->name()} column {$config->columnName} not implemented as Selected!");
        }
        return $config->expression ?? $config->column->selectExpression($this->db, $this->columnQuery);
    }

    private function applyTablesAndNestedConfigSql(): void
    {
        $tableMap = $this->tableMap();

        $customTables = array_merge(...array_map(static fn($j) => ((array)$j[1]), $this->query->getJoins() ?: []));
        $customTables = array_map(static fn($v, $k) => is_int($k) ? $v : $k, $customTables, array_keys($customTables));
        $invalidTables = implode(', ', array_map(static fn($v) => "'$v'", array_diff(array_keys($this->useTables), array_keys($tableMap), $customTables)));
        if (!empty($invalidTables)) {
            self::log("WARNING: not found table alias $invalidTables in table map and query");
        }

        if (empty($tableMap)) {
            return;
        }

        if (array_key_exists(SqlConfig::EXPR_ALIAS, $tableMap)) {
            throw new \ErrorException("{$this->name()} invalid tableMap alias '" . SqlConfig::EXPR_ALIAS . "'");
        }

        $depsMap = array_map(static fn($joinConfig) => $joinConfig[2] ?? [], $tableMap);
        $needTables = array_filter(array_map(fn($a) => $this->needTable($a) ? $a : false, array_keys($tableMap)));

        array_walk($needTables, function ($name, $index) use (&$needTables, $depsMap) {
            if ($index > 100) {
                throw new \ErrorException("Out of memory! Check tableMap config in {$this->name()}");
            }
            array_push($needTables, ...$depsMap[$name]);
        });

        $this->filtersTableMapDependencies = Arr::leaveOnlyKeys($this->filtersTableMapDependencies, array_keys($this->filtersLargeQueryClosures));
        $needTableMap = Arr::leaveOnlyKeys($tableMap, [array_key_first($tableMap), ...array_unique($needTables)]);

        foreach ($needTableMap as $alias => $tableCondition) {
            $tableQuery = $tableCondition[0];
            if ($tableQuery instanceof \Closure) {
                $tableQuery = $tableQuery($this->query, $this->nestedRequest($alias));
                if (empty($tableQuery)) {
                    if (!empty($tableCondition[1])) {
                        throw new \ErrorException("{$this->name()} table Closure '$alias' must return table alias or Query to join with");
                    }
                    continue;
                }
            } elseif (isset($this->nestedRequests[$alias])) {
                $behaviorErrMsg = "{$this->name()} invalid nested request usage for alias '$alias'. Table config[0] mast be callable or query config class";
                if (!class_exists($tableQuery)) {
                    throw new \ErrorException($behaviorErrMsg);
                }
                $tableQuery = new $tableQuery($this->container);
                if (!$tableQuery instanceof self) {
                    throw new \ErrorException($behaviorErrMsg);
                }
                $params = $this->query->getParams();
                $tableQuery = $tableQuery->query($this->db, $this->nestedRequest($alias), $params);
                $this->query->params($params);
            }

            $table = [$alias => $tableQuery];
            if (!isset($tableCondition[1])) {
                $this->query->from($table);
            } else {
                $this->query->join($tableCondition[3] ?? 'LEFT JOIN', $table, $tableCondition[1]);
            }

            $this->applyFilterLargeSql($alias);
        }

        if (!empty($this->filtersLargeQueryClosures)) {
            throw new \LogicException("{$this->name()} column(s) " . implode(', ', array_keys($this->filtersLargeQueryClosures)) . " large filters not applied");
        }
    }

    // main usage is filter by inner join from values,
    // so required to be in top of joins (for index scan) but with already joined tables to inner join with
    private function applyFilterLargeSql(string $curTableAlias): void
    {
        $this->filtersTableMapDependencies = array_map(static fn($deps) => array_diff($deps, [$curTableAlias]), $this->filtersTableMapDependencies);
        $this->filtersTableMapDependencies = array_filter($this->filtersTableMapDependencies, function ($deps, $filterName) {
            if (empty($deps)) {
                array_walk($this->filtersLargeQueryClosures[$filterName], static fn($closure) => $closure());
                unset($this->filtersLargeQueryClosures[$filterName]);
            }
            return $deps;
        }, ARRAY_FILTER_USE_BOTH);
    }

    private function nestedRequest(string $configName): QueryRequest
    {
        return $this->nestedRequests[$configName] ??= new QueryRequest();
    }

    protected function name(): string
    {
        return array_slice(explode('\\', static::class), -1)[0];
    }

    public static function enableLog(): void
    {
        self::$logEnabled = true;
    }

    private static function log(string $msg): void
    {
        self::$logEnabled && array_push(static::$logs, $msg);
    }

    public static function logs(): string
    {
        return implode("\n", self::$logs);
    }
}
