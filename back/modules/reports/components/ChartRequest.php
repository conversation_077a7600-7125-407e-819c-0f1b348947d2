<?php

declare(strict_types=1);

namespace app\back\modules\reports\components;

use app\back\modules\monitoring\charts\sources\BaseChartSource;
use app\back\modules\reports\columns\Operators;

class ChartRequest extends BaseFormRequest
{
    public ?string $from;
    public ?string $to;
    public ?int $divider;
    public ?string $metric = null;

    public function __construct(array $requestData, array $allowedFilters)
    {
        $this->from = $requestData['from'] ?? null;
        $this->to = $requestData['to'] ?? null;
        $this->divider = $requestData['divider'] ?? null;
        $this->metric = $requestData['metric'] ?? null;

        if (isset($this->from)) {
            $requestData['filters'][] = [BaseChartSource::DATE_COLUMN, $this->from, Operators::GE];
        }

        if (isset($this->to)) {
            $requestData['filters'][] = [BaseChartSource::DATE_COLUMN, $this->to, Operators::L];
        }

        parent::__construct($requestData, $allowedFilters);
    }

    public function setCasted(string $attr, mixed $value): void
    {
        match ($attr) {
            'from', 'to', 'divider', 'metric' => $this->$attr = $value,
            default => parent::setCasted($attr, $value),
        };
    }

    public function valueByAttr(string $attr): mixed
    {
        return match ($attr) {
            'from', 'to', 'divider', 'metric' => $this->$attr,
            default => parent::valueByAttr($attr),
        };
    }

    public function queryRequest(): QueryRequest
    {
        $queryRequest = (new QueryRequest())
            ->select(BaseChartSource::DATE_COLUMN, $this->metric)
            ->group(BaseChartSource::DATE_COLUMN)
            ->order(BaseChartSource::DATE_COLUMN);

        foreach ($this->filtersData() as $filter) {
            $queryRequest->addFilter($filter['name'], $filter['value'], $filter['operator'] ?? null, $filter['orFilter'] ?? 0);
        }

        return $queryRequest;
    }
}
