<?php

declare(strict_types=1);

namespace app\back\modules\back\cidIgnores;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class CidIgnoresController extends WebController
{
    public function actionData(CidIgnoresForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionAddForm(CidIgnoresAddForm $form): array
    {
        return $form->response();
    }

    public function actionAdd(CidIgnoresAddForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->add();
        $this->bl()->create($request->json());
    }

    public function actionDelete(CidIgnoresDeleteForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->delete();
        $this->bl()->delete($request->json());
    }
}
