<?php

declare(strict_types=1);

namespace app\back\modules\back\games;

use app\back\components\Form;
use app\back\components\validators\IdValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\GameVendorGroup;
use app\back\repositories\GameVendorGroups;

class GamesVendorsGroupsModifyForm
{
    use Form;

    #[IdValidator]
    public int $id;
    #[StringValidator(1, 100)]
    public string $name;

    public function __construct(private GameVendorGroups $gameVendorGroups)
    {
    }

    public function update(): void
    {
        /** @var GameVendorGroup $entity */
        $entity = $this->gameVendorGroups->findOneOr404(['id' => $this->id]);
        $entity->name = $this->name;
        $this->gameVendorGroups->update($entity, ['name']);
    }
}
