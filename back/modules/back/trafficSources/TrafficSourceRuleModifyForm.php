<?php

declare(strict_types=1);

namespace app\back\modules\back\trafficSources;

use app\back\components\Form;
use app\back\components\SessionMessages;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\TrafficSource;
use app\back\entities\TrafficSourceRule;
use app\back\repositories\Refcodes;
use app\back\repositories\TrafficSourceRules;

class TrafficSourceRuleModifyForm
{
    use Form;

    #[StringValidator]
    public string $code;
    #[IntInArrayValidator(TrafficSource::NAMES)]
    public ?int $ts_id = null;
    #[IntValidator]
    public ?int $order = null;
    #[StringInArrayValidator(['ts_id', 'order'], true)]
    public string $column;

    public function __construct(
        public SessionMessages $messages,
        private readonly TrafficSourceRules $trafficSourceRules,
        private readonly Refcodes $refcodes,
    ) {
    }

    public function save(): void
    {
        /** @var TrafficSourceRule $entity */
        $entity = $this->trafficSourceRules->findOneOr404(['code' => $this->code]);

        if ($this->column === 'ts_id') {
            $entity->ts_id = $this->ts_id;
        }

        if ($this->column === 'order') {
            $entity->order = $this->order;
        }

        $this->trafficSourceRules->update($entity, [$this->column]);

        $resetCount = $this->refcodes->resetTs($entity->code);

        $this->messages->success("Traffic source has been saved. {$resetCount} ref codes have been reset");
    }
}
