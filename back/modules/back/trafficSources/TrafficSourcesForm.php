<?php

declare(strict_types=1);

namespace app\back\modules\back\trafficSources;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Db;
use app\back\components\RichTable;
use app\back\components\validators\IdArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\TrafficSource;
use app\back\repositories\Refcodes;
use app\back\repositories\TrafficSourceRules;
use app\back\repositories\TrafficSources;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class TrafficSourcesForm
{
    use RichTable;

    #[StringValidator]
    public ?string $code = null;
    #[IdArrayValidator(TrafficSource::NAMES)]
    public array $ts_id = [];

    public function __construct(
        private readonly ConnectionInterface $db
    ) {
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Prefix', 'code' => 'code', 'sortable' => true],
            ['name' => 'Traffic Source', 'code' => 'name', 'slotName' => 'name', 'sortable' => true],
            ['name' => 'Order', 'code' => 'order', 'slotName' => 'order', 'sortable' => true],
            ['name' => 'Actions', 'slotName' => 'actions'],
        ];
    }

    protected function blocks(): array
    {
        $trafficSources = (new Query($this->db))
            ->from(['t' => TrafficSources::TABLE_NAME])
            ->select([
                'id' => 't.id',
                'name' => 't.name',
                'count' => 'COUNT(*)',
            ])
            ->innerJoin(['tr' => TrafficSourceRules::TABLE_NAME], 'tr.ts_id = t.id')
            ->groupBy(['t.id'])
            ->orderBy(['t.name' => SORT_ASC])
            ->all();

        return [
            [
                $this->textInputCell(2, 'code', 'Prefix'),
                $this->listCell(10, 'ts_id', 'Traffic sources', [
                    'list' => $trafficSources,
                ]),
            ],
        ];
    }

    protected function getQuery(): Query
    {
        $query = (new Query($this->db))
            ->from(['tr' => TrafficSourceRules::TABLE_NAME])
            ->innerJoin(['t' => TrafficSources::TABLE_NAME], 't.id = tr.ts_id')
            ->andFilterWhere(['tr.ts_id' => $this->ts_id]);

        if ($this->code) {
            $query->andWhere(['ILIKE', 'code', Db::escapeLikeVal($this->code) . '%', null]);
        }

        return $query;
    }

    protected function total(): int
    {
        return $this->getQuery()->count();
    }

    protected function data(): array
    {
        $rows = $this->getQuery()
            ->select([
                'tr.code',
                'tr.ts_id',
                'tr.order',
            ])
            ->offset($this->getOffset())
            ->limit($this->getLimit())
            ->orderBy($this->getOrderMap())
            ->all();

        foreach ($rows as &$row) {
            $row['all_traffic_sources'] = Arr::assocToIdName(TrafficSource::NAMES);
        }

        return $rows;
    }

    public function refcodesCount(): int
    {
        return (new Query($this->db))
            ->from(['r' => Refcodes::TABLE_NAME])
            ->andWhere(['ILIKE', 'code', Db::escapeLikeVal($this->code) . '%', null])
            ->count();
    }

    public function getCoveredByCurrentRuleRules(): array
    {
        return (new Query($this->db))
            ->select('tr.code')
            ->from(['tr' => TrafficSourceRules::TABLE_NAME])
            ->orWhere(['ILIKE', 'tr.code', Db::escapeLikeVal($this->code) . '%', null])
            ->andWhere(['!=', 'tr.code', $this->code])
            ->column();
    }

    public function getRulesCoversCurrentRule(): array
    {
        return (new Query($this->db))
            ->select('tr.code')
            ->from(['tr' => TrafficSourceRules::TABLE_NAME])
            ->where(['IN', 'tr.code', array_reduce(str_split($this->code), function ($c, $i) {
                $c = $c ?? [];
                $c[] = end($c) . $i;
                return $c;
            })])
            ->andWhere(['!=', 'tr.code', $this->code])
            ->column();
    }
}
