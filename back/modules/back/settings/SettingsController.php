<?php

declare(strict_types=1);

namespace app\back\modules\back\settings;

use app\back\components\accessCheck\AccessCheckCallable;
use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\BaseAuthAccess;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;
use app\back\entities\Setting;

#[AccessCheckPage]
class SettingsController extends WebController
{
    public function actionSettings(SettingSaveForm $form): array
    {
        return $form->settings();
    }

    #[AccessCheckCallable([self::class, 'checkSettings'])]
    public function actionSaveValue(SettingSaveForm $form, Request $request, SessionMessages $messages): array
    {
        $form->validateOrException($request->json());
        $form->save();
        $this->bl()->modify($request->json());
        $messages->success('Updated successfully');

        return [];
    }

    public static function checkSettings(BaseAuthAccess $auth, Request $request): bool
    {
        $settingName = $request->json()['name'];

        return $auth->can(Setting::getPermissionNameBySetting($settingName));
    }
}
