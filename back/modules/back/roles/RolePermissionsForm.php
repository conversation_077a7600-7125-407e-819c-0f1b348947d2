<?php

declare(strict_types=1);

namespace app\back\modules\back\roles;

use app\back\components\Form;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\AuthRole;
use app\back\entities\AuthRolePermission;
use app\back\modules\back\employees\EmployeePermissionsTree;
use app\back\repositories\AuthRolePermissions;
use app\back\repositories\AuthRoles;

class RolePermissionsForm
{
    use Form;

    #[StringValidator]
    #[CallableValidator([self::class, 'loadRole'])]
    public string $role;

    private readonly AuthRole $roleItem;

    public function __construct(
        private readonly AuthRolePermissions $authRolePermissionsRepo,
        private readonly AuthRoles $authRolesRepo,
        private readonly EmployeePermissionsTree $employeePermissionsTree,
    ) {
    }

    public static function loadRole(string $role, self $form): ?string
    {
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        $form->roleItem = $form->authRolesRepo->findOneOr404(['name' => $role]);

        return null;
    }

    public function response(): array
    {
        /** @var AuthRolePermission[] $children */
        $children = $this->authRolePermissionsRepo->findEach(['role' => $this->roleItem->name]);

        $assigned = [];
        foreach ($children as $row) {
            $assigned[$row->permission] = true;
        }

        return [
            'roleDescription' => $this->roleItem->description,
            'permissions' => $this->employeePermissionsTree->build($assigned, $assigned),
        ];
    }
}
