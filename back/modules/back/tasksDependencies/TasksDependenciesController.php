<?php

declare(strict_types=1);

namespace app\back\modules\back\tasksDependencies;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class TasksDependenciesController extends WebController
{
    public function actionForm(TasksDependenciesForm $form): array
    {
        return $form->response();
    }

    public function actionData(TasksDependenciesForm $form, Request $request): array
    {
        $form->validateOrException($request->json());

        return $form->data();
    }
}
