<?php

declare(strict_types=1);

namespace app\back\modules\user\player\forms;

use app\back\components\AllowedLists;
use app\back\components\Form;
use app\back\components\helpers\Arr;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\entities\Site;
use app\back\modules\task\components\FetchTaskFactory;
use app\back\repositories\Sites;

class CashbackShowForm extends BasePlayerForm
{
    use Form;

    #[AllowedSiteValidator]
    #[IntInArrayValidator(Site::PLATFORM_SITES_SMEN, true)] // Parent with additional check
    public int $siteId;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly Sites $sitesRepo,
        private readonly FetchTaskFactory $requestFactory,
    ) {
    }

    public function fetchCashback(): ?string
    {
        $task = $this->requestFactory->createFetchTask('get-user-cashback', $this->siteId, ['params' => [
            'userId' => $this->userId,
        ]]);

        $data = Arr::fromIterable($task->finalData());
        return $data[0]['cashback'];
    }
}
