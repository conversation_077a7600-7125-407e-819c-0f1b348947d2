<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\bonusOffers;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\InvalidException;
use app\back\components\Form;
use app\back\components\helpers\Json;
use app\back\components\validators\IntValidator;
use app\back\entities\BonusOffer;
use app\back\entities\Site;
use app\back\modules\task\components\FetchTaskFactory;
use app\back\modules\task\requests\SmenBonusOfferUpdateRequest;
use app\back\modules\user\player\forms\BasePlayerForm;
use app\back\repositories\BonusOffers;
use app\back\repositories\Sites;

class BonusOfferCancelForm extends BasePlayerForm
{
    use Form;

    #[IntValidator]
    public int $bonusOfferId;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly BonusOffers $bonusOffersRepo,
        private readonly Sites $sitesRepo,
        private readonly BaseAuthAccess $auth,
        private readonly FetchTaskFactory $requestFactory,
    ) {
    }

    public function cancel(): bool
    {
        /** @var BonusOffer $bo */
        $bo = $this->bonusOffersRepo->findOneOr404(['id' => $this->bonusOfferId]);

        if (!in_array($bo->status, [BonusOffer::STATUS_ACTIVE, BonusOffer::STATUS_AVAILABLE], true)) {
            throw new InvalidException('Offer is not active or available');
        }

        if (Site::getPlatformBySiteId($this->siteId) === Site::PLATFORM_SMEN) {
            $this->cancelSmen($bo);
        }

        $bo->status = BonusOffer::STATUS_CANCELLED;
        $this->bonusOffersRepo->update($bo, ['status']);

        return true;
    }

    private function cancelSmen(BonusOffer $bo): void
    {
        if ($bo->remote_id === null) {
            throw new InvalidException('Offer without remote_id');
        }

        $fetchTask = $this->requestFactory->createFetchTask('bonus-offer-update', $this->siteId, [
            'bonusOfferId' => $bo->remote_id,
            'action' => SmenBonusOfferUpdateRequest::ACTION_CANCEL,
            'params' => [],
        ]);
        $result = $fetchTask->finalData();

        if (!isset($result[0]['status_name']) || $result[0]['status_name'] !== 'Cancel') {
            throw new InvalidException('Invalid state received: ' . Json::encode($result[0]));
        }
    }
}
