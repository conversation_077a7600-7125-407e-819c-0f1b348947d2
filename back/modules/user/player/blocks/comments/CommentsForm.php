<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\comments;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\UserComment;
use app\back\modules\task\TaskConfigs;
use app\back\modules\user\player\blocks\BasePlayerForm;
use app\back\repositories\YhOperators;
use app\back\repositories\UserComments;
use Yiisoft\Db\Query\Query;

class CommentsForm extends BasePlayerForm
{
    use RichTable;

    #[IntArrayValidator(UserComment::SOURCES)]
    public array $source = [];
    #[BooleanValidator]
    public bool $deleted = false;

    public function __construct(
        AllowedLists $allowedLists,
        SecondaryConnection $db,
        BaseAuthAccess $auth,
        private readonly YhOperators $operatorsRepo,
        private readonly TaskConfigs $taskConfigs,
    ) {
        parent::__construct($allowedLists, $db, $auth);
        $this->from = $this->getSettingFrom();
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Message', 'code' => 'comment', 'slotName' => 'comment', 'align' => 'start'],
            ['name' => 'Operator', 'code' => 'updated_by'],
            ['name' => 'Source', 'code' => 'source'],
            ['name' => 'Updated at', 'code' => 'updated_at'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->listCell(3, 'source', 'Source', [
                    'list' => Arr::assocToIdName(UserComment::SOURCES),
                ]),
                $this->selectBooleanCell(3, 'deleted', 'Show deleted', ['multiple' => false]),
            ],
        ];
    }

    protected function data(): array
    {
        $result = $this->buildQuery()
            ->select([
                'id' => 'uc.id',
                'updated_at' => 'uc.updated_at',
                'updated_by' => 'uc.updated_by',
                'comment' => 'uc.comment',
                'source' => 'uc.source',
                'is_editable' => "CASE WHEN now() - uc.created_at < interval '1 hour' THEN true ELSE false END",
            ])
            ->orderBy(['uc.updated_at' => SORT_DESC])
            ->all();

        foreach ($result as &$row) {
            $row['source'] = UserComment::SOURCES[$row['source']];
        }

        return $result;
    }

    public function total(): int
    {
        return $this->buildQuery()
            ->select('COUNT(*)')
            ->scalar();
    }

    private function buildQuery(): Query
    {
        $query = (new Query($this->db))
            ->from(['uc' => UserComments::TABLE_NAME]);

        return $this->applyFilters($query);
    }

    private function applyFilters(Query $query): Query
    {
        $query->andWhere($this->siteUser('uc'))
            ->andFilterWhere([
                'AND',
                ['uc.source' => $this->source],
                ['uc.type' => UserComment::TYPE_USER],
            ]);

        if (!$this->deleted) {
            $query->andFilterWhere(['uc.is_visible' => !$this->deleted]);
        }

        return $query;
    }
}
