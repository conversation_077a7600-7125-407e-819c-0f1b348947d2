<?php

declare(strict_types=1);

namespace app\back\modules\user\chats;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class ChatsController extends WebController
{
    public function actionForm(ChatsForm $form): array
    {
        return $form->response();
    }

    public function actionLoad(ChatsForm $form, Request $request): array
    {
        $result = $form->validateAndResponse($request->json());
        $result['chats'] = $form->getData();

        return $result;
    }
}
