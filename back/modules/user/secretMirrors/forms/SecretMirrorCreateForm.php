<?php

declare(strict_types=1);

namespace app\back\modules\user\secretMirrors\forms;

use app\back\components\AllowedLists;
use app\back\components\FormGrid;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\MatchValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\SecretMirror;
use app\back\repositories\SecretMirrors;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class SecretMirrorCreateForm
{
    use FormGrid;

    public const string DOMAIN_VALIDATE_PATTERN = '#^([A-Z0-9][A-Z0-9_-]*)(\.[A-Z0-9][A-Z0-9_-]*)+$#i';

    #[StringValidator]
    #[MatchValidator(SecretMirrorCreateForm::DOMAIN_VALIDATE_PATTERN)]
    #[CallableValidator([self::class, 'domainExist'])]
    public string $mirror;

    #[AllowedSiteValidator]
    public int $siteId;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly SecretMirrors $secretMirrors,
        public readonly AllowedLists $allowedLists
    ) {
    }

    public function save(): void
    {
        $entity = new SecretMirror([
            'url' => $this->mirror,
            'status' => SecretMirror::STATUS_NOT_SYNCED,
            'site_id' => $this->siteId
        ]);

        $this->secretMirrors->insert($entity);
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectSiteCell(12, 'siteId', 'Site', ['multiple' => false]),
            ],
            [
                $this->textInputCell(12, 'mirror', 'Domain names', ['placeholder' => 'Put domain name (without http://)'])
            ],
            [
                $this->submitCell(12, 'Add', ['buttonStyle' => 'btn-success', 'buttonIcon' => 'icn-plus'])
            ],
        ];
    }

    public static function domainExist(string $value, self $form): ?string
    {
        $isExists = (new Query($form->db))
            ->select(['id'])
            ->from(SecretMirrors::TABLE_NAME)
            ->where(['url' => $value])
            ->exists();

        return ($isExists) ? 'Domain already exists' : null;
    }
}
