<?php

declare(strict_types=1);

namespace app\back\modules\user\timeline\events;

use app\back\components\Initializable;
use app\back\entities\UserBlock;
use app\back\modules\user\timeline\EventDto;
use app\back\modules\user\timeline\TimelineForm;
use app\back\repositories\UserBlockComments;
use app\back\repositories\UserBlocks;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class BlockUserEvent extends BaseEvent
{
    public static function getEventTypeName(): string
    {
        return 'Block user';
    }

    public static function getInfo(): array
    {

        return [
            ['icon' => 'icn-ban', 'classes' => 'btn-red', 'text' => 'Blocked', 'description' => 'Block types: ' . implode(', ', UserBlock::BLOCK_TYPES)],
        ];
    }

    public function getEvents(): array
    {
        return (new Query($this->db))
            ->select([
                'event_type' => new Expression((string)TimelineForm::EVENT_TYPE_BLOCK_USER),
                'ub.site_id',
                'ub.user_id',
                'ub.type',
                'ub.active',
                'ub.reason',
                'ubc.comment',
                'ub.source',
                'date' => '(ub.updated_at::date)',
                'time' => '(ub.updated_at::time)',
            ])
            ->from(['ub' => UserBlocks::TABLE_NAME])
            ->leftJoin(['ubc' => UserBlockComments::TABLE_NAME], 'ub.comment_id = ubc.id')
            ->where([
                'ub.site_id' => $this->siteIdUserId['site_id'],
                'ub.user_id' => $this->siteIdUserId['user_id'],
                'ub.active' => true,
            ])
            ->andFilterWhere([
                'AND',
                ['>=', 'ub.updated_at', $this->dateFrom],
                ['<', 'ub.updated_at', $this->dateTo],
            ])
            ->all();
    }

    public function rowToEvent(array $row, EventDto $event): void
    {
        $event->classes[] = 'btn-red';
        $event->icon[] = 'icn-ban';
        $event->text = UserBlock::BLOCK_TYPES[$row['type']] . ' blocked';
        $event->title = 'Type: ' . UserBlock::BLOCK_TYPES[$row['type']];
        $event->tooltip['Event'] = $event->text;
        $event->tooltip['Source'] = UserBlock::SOURCES[$row['source']];
        $event->tooltip['Reason'] = UserBlock::REASONS[$row['reason']];
        $event->tooltip['Comment'] = $row['comment'];
    }
}
