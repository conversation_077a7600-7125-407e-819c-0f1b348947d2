<?php

declare(strict_types=1);

namespace app\back\modules\user\timeline\events;

use app\back\components\helpers\Str;
use app\back\components\Initializable;
use app\back\entities\UserEventCheckout;
use app\back\modules\user\timeline\EventDto;
use app\back\repositories\DbLargeDictionary;
use app\back\repositories\PaySystems;
use app\back\repositories\UseragentBrowsers;
use app\back\repositories\UseragentDevices;
use app\back\repositories\UseragentPlatforms;
use app\back\repositories\Useragents;
use app\back\repositories\UserEventCheckouts;
use Yiisoft\Db\Query\Query;

class CheckoutEvent extends BaseEvent
{
    private readonly PaySystems $paySystemsRepo;
    private readonly UseragentPlatforms $useragentPlatformsRepo;
    private readonly UseragentBrowsers $useragentBrowsersRepo;
    private readonly DbLargeDictionary $useragentDevicesDict;

    #[Initializable]
    final public function init(
        PaySystems $paySystemsRepo,
        UseragentPlatforms $useragentPlatformsRepo,
        UseragentBrowsers $useragentBrowsersRepo,
    ): void {
        $this->useragentPlatformsRepo = $useragentPlatformsRepo;
        $this->useragentBrowsersRepo = $useragentBrowsersRepo;
        $this->paySystemsRepo = $paySystemsRepo;
        $this->useragentDevicesDict = new DbLargeDictionary(UseragentDevices::class, $this->db);
    }

    public static function getEventTypeName(): string
    {
        return 'Checkout';
    }

    public static function getInfo(): array
    {
        return [
            ['icon' => 'icn-cash-register', 'classes' => 'btn-cyan-muted', 'text' => 'Checkout', 'description' => 'Events types: ' . implode(', ', UserEventCheckout::EVENT_TYPES_ALL)],
        ];
    }

    public function getEvents(): array
    {
        return (new Query($this->db))
            ->select([
                'uec.site_id',
                'uec.user_id',
                'uec.event_type',
                'date_received' => '(uec.server_received_at::timestamp(0))',
                'date' => '(uec.client_created_at::date)',
                'time' => '(uec.client_created_at::time)',
                'uec.ip',
                'uec.pay_sys_id',
                'uag.platform_id',
                'uag.browser_id',
                'uag.device_id',
            ])
            ->from(['uec' => UserEventCheckouts::TABLE_NAME])
            ->leftJoin(['uag' => Useragents::TABLE_NAME], 'uag.id = uec.useragent_id')
            ->where([
                'uec.site_id' => $this->siteIdUserId['site_id'],
                'uec.user_id' => $this->siteIdUserId['user_id'],
            ])
            ->andFilterWhere([
                'AND',
                ['>=', 'uec.server_received_at', $this->dateFrom],
                ['<', 'uec.server_received_at', $this->dateTo],
                ['uec.ip' => Str::explodeText($this->ip)],
            ])
            ->all();
    }

    public function rowToEvent(array $row, EventDto $event): void
    {
        $event->classes[] = 'btn-cyan-muted';
        $event->icon[] = 'icn-cash-register';
        $event->text = UserEventCheckout::EVENT_TYPES_ALL[$row['event_type']];
        $event->tooltip['Source'] = isset(UserEventCheckout::EVENT_TYPES_S2P[$row['event_type']]) ?
            UserEventCheckout::SOURCES[UserEventCheckout::SOURCE_S2P] : UserEventCheckout::SOURCES[UserEventCheckout::SOURCE_PRODUCT];

        $event->title = "Checkout {$event->tooltip['Source']} event";
        $event->tooltip['Event'] = $event->text;
        $event->tooltip['Received'] = $row['date_received'];
        if ($row['ip']) {
            $event->tooltip['IP'] = $row['ip'];
        }
        if ($row['pay_sys_id']) {
            $event->tooltip['Pay sys'] = $this->paySystemsRepo->getNameById($row['pay_sys_id']);
        }
        if ($row['device_id']) {
            $event->tooltip['Device'] = $this->useragentDevicesDict->getNameById($row['device_id']);
        }
        if ($row['platform_id']) {
            $platformName = $this->useragentPlatformsRepo->getNameById($row['platform_id']);
            $event->tooltip['Platform'] = $platformName;
        }
        if ($row['browser_id']) {
            $event->tooltip['Browser'] = $this->useragentBrowsersRepo->getNameById($row['browser_id']);
        }
    }
}
