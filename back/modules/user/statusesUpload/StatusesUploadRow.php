<?php

declare(strict_types=1);

namespace app\back\modules\user\statusesUpload;

use app\back\components\AllowedLists;
use app\back\components\BaseCsvUploadRow;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\FilterValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\entities\User;
use app\back\entities\UserSpecialInfo;
use app\back\repositories\YhOperators;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use DateTimeImmutable;

class StatusesUploadRow extends BaseCsvUploadRow
{
    protected const array VALID_CRM_GROUPS = [UserSpecialInfo::CRM_GROUP_FAKE_RESET, ...UserSpecialInfo::CRM_GROUPS];

    #[IdValidator]
    #[AllowedSiteValidator]
    public int $site_id;
    #[BigIdValidator]
    #[CallableValidator([self::class, 'userExistsValidator'])]
    public int $user_id;
    #[IntInArrayValidator([User::class, 'actualStatuses'])]
    public ?int $status = null;
    #[IntInArrayValidator(User::ACTIVE_STATUSES)]
    public ?int $active_status = null;
    #[BooleanValidator]
    #[FilterValidator([self::class, 'processFalse'])]
    public ?bool $is_manual_status = null;
    #[IntInArrayValidator([self::class, 'validatePersonalManager'])]
    #[FilterValidator([self::class, 'processZero'])]
    public ?int $personal_manager = null;
    #[FilterValidator([self::class, 'processEmpty'], 'pm_first_contact_at')]
    #[DateTimeImmutableValidator]
    public ?DateTimeImmutable $pm_first_contact_at = null;
    #[IntInArrayValidator(self::VALID_CRM_GROUPS, useValues: true)]
    #[CallableValidator([self::class, 'userSpecialInfoExistsValidator'])]
    #[FilterValidator([self::class, 'resetCrmGroup'])]
    public ?int $crm_group = null;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        protected readonly YhOperators $operatorsRepo,
        protected readonly Users $usersRepo,
        protected readonly UserSpecialInfos $userSpecialInfosRepo,
    ) {
    }

    public static function userExistsValidator(?int $value, self $rowForm, array $context): ?string
    {
        if (!isset($rowForm->site_id)) {
            return null; // siteId is invalid at this point
        }

        /** @var User $user */
        $user = $rowForm->usersRepo->findOne(['site_id' => $context['site_id'], 'user_id' => $value]);
        if ($user === null) {
            return "not found: {$context['site_id']}-{$value}";
        }
        $rowForm->columnsToProcess['user'] = $user; // required for UserHistoryHandler
        return null;
    }

    public static function validatePersonalManager(self $rowForm): array
    {
        return $rowForm->operatorsRepo->getNames();
    }

    public static function userSpecialInfoExistsValidator(?int $value, self $rowForm, array $context): ?string
    {
        if (!isset($rowForm->site_id, $rowForm->user_id) || $value === null) {
            return null;
        }

        $entity = $rowForm->userSpecialInfosRepo->findOne(['site_id' => $rowForm->site_id, 'user_id' => $rowForm->user_id]);
        return ($entity === null) ? "user special info not found: {$rowForm->site_id}-{$value}" : null;
    }

    public static function resetCrmGroup(?int $value, self $rowForm): ?int
    {
        if ($value === UserSpecialInfo::CRM_GROUP_FAKE_RESET) {
            $rowForm->columnsToProcess['crm_group'] = null;
            return null;
        }
        return $value;
    }

    public static function processFalse(?bool $value, self $rowForm): ?bool
    {
        if ($value === false) {
            $rowForm->columnsToProcess['is_manual_status'] = false;
        }
        return $value;
    }

    public static function processZero(?int $value, self $rowForm): ?int
    {
        if ($value === 0) {
            $rowForm->columnsToProcess['personal_manager'] = 0;
        }
        return $value;
    }
}
