<?php

declare(strict_types=1);

namespace app\back\modules\user\manager\forms;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Str;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\BigIdMultilineValidator;
use app\back\components\validators\StringValidator;
use app\back\modules\task\components\FetchTaskFactory;

class ManagerUnbanForm
{
    use FormGrid;

    #[BigIdMultilineValidator]
    public string $userId;
    #[AllowedSiteValidator]
    public int $siteId;
    #[StringValidator]
    public string $reason;

    public function __construct(
        private readonly BaseAuthAccess $auth,
        public readonly AllowedLists $allowedLists,
        private readonly FetchTaskFactory $requestFactory,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectSiteCell(3, 'siteId', 'Site', ['multiple' => false]),
                $this->textAreaCell(3, 'userId', 'User ID'),
                $this->textAreaCell(5, 'reason', 'Reason'),
                $this->submitCell(1, 'Unblock'),
            ],
        ];
    }

    public function process(): bool
    {
        $userIds = Str::explodeText($this->userId);
        $employee = $this->auth->employee();

        $fetchTask = $this->requestFactory->createFetchTask('users-unblock', $this->siteId, [
            'params' => [
                'userIds' => $userIds,
                'reason' => $this->reason,
                'operator' => $employee->email,
            ],
        ]);

        return $fetchTask->sendAndCheckSuccess();
    }
}
