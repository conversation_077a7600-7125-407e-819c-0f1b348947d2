<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\RequiredWhenAnyNotEmptyValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\S2pOrder;

abstract class BaseRequisiteTransactionsRule extends BaseRule
{
    public const string COL_REQUISITE = 'requisite';

    public const string AGGREGATOR_SUM = 'SUM';
    public const string AGGREGATOR_COUNT = 'COUNT';

    public const array AGGREGATES = [
        self::AGGREGATOR_SUM => 'Sum',
        self::AGGREGATOR_COUNT => 'Count',
    ];

    #[IntArrayValidator(S2pOrder::STATUSES)]
    public array $status = [];
    #[StringArrayValidator(S2pOrder::TYPES)]
    public array $direction = [];
    #[StringInArrayValidator(self::UNITS)]
    #[RequiredWhenAnyNotEmptyValidator('period')]
    public ?string $periodUnit = self::UNIT_HOUR;
    #[IntValidator]
    public ?int $period = null;
    #[StringInArrayValidator(self::AGGREGATES)]
    public string $aggregator;
    #[StringInArrayValidator(self::OPERATORS)]
    public string $valueOperator = self::OPERATOR_GREATER;
    #[IntValidator]
    public int $value;

    protected function blocks(): array
    {
        return [
            [
                $this->selectCell(2, 'status', 'Status', [
                    'list' => Arr::assocToIdName(S2pOrder::STATUSES),
                ]),
                $this->selectCell(2, 'direction', 'Direction', [
                    'list' => Arr::assocToIdName(S2pOrder::TYPES),
                ]),
                $this->radioListCell(2, 'aggregator', 'Aggregator', [
                    'list' => Arr::assocToIdName(self::AGGREGATES),
                ]),
                $this->textInputCell(2, 'period', 'Period', [
                    'operators' => Arr::assocToIdName(self::UNITS),
                    'operatorPostfix' => 'Unit',
                ]),
                $this->textInputCell(2, 'value', 'Value', [
                    'hint' => 'For Sum in USD',
                    'operators' => Arr::assocToIdName(self::OPERATORS),
                    'operatorPostfix' => 'Operator',
                ]),
            ],
        ];
    }

    public function getPk(array $sourceRow): ?string
    {
        return $sourceRow[static::COL_REQUISITE];
    }

    public function getParamsSignatureParts(): array
    {
        return ['periodUnit', 'period', 'status', 'direction', 'aggregator', 'value', 'valueOperator'];
    }

    public function getLabel(): string
    {
        $statuses = [];
        foreach ($this->status as $status) {
            $statuses[] = S2pOrder::getStatusById((int) $status);
        }
        $statuses = implode(', ', $statuses);

        $directions = [];
        foreach ($this->direction as $direction) {
            $directions[] = S2pOrder::getTypeById($direction);
        }
        $directions = implode(', ', $directions);

        $period = 'LT';
        if (!empty($this->period)) {
            $period = $this->period . " " . $this->periodUnit;
        }

        $aggregator = (self::AGGREGATES)[$this->aggregator];

        return "<span class=\"badge bg-secondary\">$statuses $directions {$aggregator} (for $period) {$this->valueOperator} {$this->value}</span>";
    }
}
