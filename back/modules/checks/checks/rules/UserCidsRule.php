<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\helpers\Arr;
use app\back\components\InBuilder;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\repositories\Users;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UserCidsRule extends BaseSiteUserRule
{
    #[StringInArrayValidator(self::UNITS)]
    public string $periodUnit = self::UNIT_HOUR;
    #[IntValidator]
    public int $period;
    #[StringInArrayValidator(self::OPERATORS)]
    public string $valueOperator = self::OPERATOR_GREATER;
    #[IntValidator]
    public int $value;

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(2, 'period', 'Period', [
                    'operators' => Arr::assocToIdName(self::UNITS),
                    'operatorPostfix' => 'Unit',
                ]),
                $this->textInputCell(2, 'value', 'Value', [
                    'hint' => 'CIDs count',
                    'operators' => Arr::assocToIdName(self::OPERATORS),
                    'operatorPostfix' => 'Operator',
                ]),
            ],
        ];
    }

    public function getParamsSignatureParts(): array
    {
        return ['periodUnit', 'period', 'value', 'valueOperator'];
    }

    public function getLabel(): string
    {
        $period = empty($this->period) ? 'LT' : $this->period . " " . $this->periodUnit;
        return "<span class=\"badge bg-secondary\">CIDs (for $period) {$this->valueOperator} {$this->value}</span>";
    }

    protected function getQuery(ConnectionInterface $db, InBuilder $inBuilder): Query
    {
        $query = (new Query($db))
            ->select([
                static::RESULT_COL_RESULT => "(COALESCE(COUNT(f.site_id), 0) {$this->valueOperator} {$this->value})::bool",
                static::RESULT_COL_PK => SiteUserBuilder::siteUserQueryExpression('u'),
            ]);

        $inBuilder->setMap([
            "u.site_id" => 'intval',
            "u.user_id" => 'intval',
        ]);

        $query->from($inBuilder->table('u', ['site_id', 'user_id']));

        $joinConds = [
            'AND',
            "t.cid = f.cid",
        ];

        if (!empty($this->period)) {
            $joinConds[] = "t.date > NOW() - INTERVAL '{$this->period} {$this->periodUnit}'";
        }

        $query
            ->leftJoin(['f' => Users::TABLE_NAME], "f.site_id = u.site_id AND f.user_id = u.user_id")
            ->leftJoin(['t' => Users::TABLE_NAME], $joinConds)
            ->groupBy([
                "u.site_id",
                "u.user_id",
            ]);

        return $query;
    }
}
