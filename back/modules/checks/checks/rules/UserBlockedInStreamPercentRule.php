<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\exceptions\InvalidException;
use app\back\components\helpers\Arr;
use app\back\components\InBuilder;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\repositories\Refcodes;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UserBlockedInStreamPercentRule extends BaseSiteUserRule
{
    public const string FIELD_NAME = 'blocked_user_percent';

    #[StringMultilineValidator(1)]
    #[CallableValidator([self::class, 'validateMessagesMap'])]
    public ?string $messagesMap = null;
    #[IntValidator(1)]
    public ?int $period = null;
    #[StringInArrayValidator(self::UNITS)]
    public string $periodUnit = self::UNIT_DAY;
    private ?array $messageMapArray = null;


    protected function getQuery(ConnectionInterface $db, InBuilder $inBuilder): Query
    {
        $inBuilder->setMap(['intval', 'intval']);

        $streamBlockedUsersStatSubquery = (new Query($db))
            ->select([
                'webmaster_id' => 'r.webmaster_id',
                'site_id' => 'su.site_id',
                self::FIELD_NAME => new Expression('round(count(*) filter (where su.is_blocked = true)::decimal  / count(*) * 100, 2)')
            ])
            ->from(['su' => Users::TABLE_NAME])
            ->innerJoin(['r' => Refcodes::TABLE_NAME], 'r.id = su.refcode_id')
            ->innerJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.user_id = su.user_id and usi.site_id = su.site_id and usi.dep_lt_count > 0')
            ->groupBy(['r.webmaster_id', 'su.site_id']);


        if (isset($this->period)) {
            $streamBlockedUsersStatSubquery->where(['>', 'su.date', new Expression("NOW() - INTERVAL '{$this->period}' {$this->periodUnit}")]);
        }

        $cond = [];
        $expParams = [];
        $i = 0;
        foreach ($this->messageMapToArray() as $value) {
            $key = "exp_{$i}";
            $cond[] = self::FIELD_NAME . " >= :$key THEN TRUE";
            $expParams[$key] = $value;
            $i++;
        }

        return (new Query($db))
            ->select([
                static::RESULT_COL_RESULT => new Expression("CASE WHEN " . implode(' WHEN ', $cond) . ' ELSE FALSE END', $expParams),
                static::RESULT_COL_PK => SiteUserBuilder::siteUserQueryExpression('f'),
                static::RESULT_COL_INFO => self::FIELD_NAME,
            ])
            ->from($inBuilder->table('f', ['site_id', 'user_id']))
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.site_id = f.site_id and u.user_id = f.user_id')
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = u.refcode_id and r.webmaster_id is not null')
            ->leftJoin(['stat' => $streamBlockedUsersStatSubquery], 'stat.webmaster_id = r.webmaster_id and stat.site_id = f.site_id');
    }

    public function getParamsSignatureParts(): array
    {
        return [
            'messagesMap',
            'period',
            'periodUnit'
        ];
    }

    public function getMessage(): string
    {
        $parts = [];
        foreach ($this->messageMapToArray() as $message => $value) {
            $parts[] = ">={$value} $message";
        }
        return implode(', ', $parts);
    }

    public function getMessageByResult(string $result): string
    {
        $messageMap = $this->messageMapToArray();
        foreach ($messageMap as $message => $value) {
            if ($result >= $value) {
                return $message;
            }
        }
        return '';
    }

    private function messageMapToArray(): array
    {
        if (!is_null($this->messageMapArray)) {
            return $this->messageMapArray;
        }

        $this->messageMapArray = [];
        foreach (explode("\n", $this->messagesMap) as $line) {
            $matches = [];
            if (!preg_match('/^([0-9.]+)%\s([a-z]+)$/i', $line, $matches)) {
                throw new \InvalidArgumentException('Not valid value!');
            }
            [, $value, $message] = $matches;
            $this->messageMapArray[$message] = (float)$value;
        }

        return $this->messageMapArray;
    }


    public function getLabel(): string
    {
        return "<span class=\"badge bg-secondary\"'>{$this->getMessage()}</span>";
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textAreaCell(8, 'messagesMap', 'Values messages map', [
                    'hint' => '"Percent message" for each line, Example: "20% mid". Largest values must be first, order is important!'
                ]),
                $this->textInputCell(4, 'period', 'Period', [
                    'operators' => Arr::assocToIdName(self::UNITS),
                    'operatorPostfix' => 'Unit',
                ]),
            ]
        ];
    }

    public static function validateMessagesMap(string $value, self $form, array $context): ?string
    {
        try {
            $form->messagesMap = $value;
            $form->messageMapToArray();
            return null;
        } catch (\InvalidArgumentException $e) {
            return $e->getMessage();
        }
    }
}
