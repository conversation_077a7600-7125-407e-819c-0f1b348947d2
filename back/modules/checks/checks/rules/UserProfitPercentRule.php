<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\helpers\Arr;
use app\back\components\InBuilder;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\FloatValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\S2pOrder;
use app\back\repositories\S2pOrders;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UserProfitPercentRule extends BaseSiteUserRule
{
    #[StringInArrayValidator(self::OPERATORS)]
    public string $valueOperator = self::OPERATOR_GREATER;
    #[FloatValidator(PHP_FLOAT_MIN, 1)]
    public float $value;

    protected function blocks(): array
    {
        return [
            [
                $this->radioListCell(4, 'operator', 'Operator', [
                    'list' => Arr::assocToIdName(self::OPERATORS),
                ]),
                $this->textInputCell(4, 'value', 'Value', [
                    'hint' => 'from -inf to 1.0',
                    'operators' => Arr::assocToIdName(self::OPERATORS),
                    'operatorPostfix' => 'Operator',
                ]),
            ],
        ];
    }

    public function getParamsSignatureParts(): array
    {
        return ['valueOperator', 'value'];
    }

    public function getLabel(): string
    {
        return "<span class=\"badge bg-secondary\">(In-Out)/In {$this->valueOperator} {$this->value}</span>";
    }

    protected function getQuery(ConnectionInterface $db, InBuilder $inBuilder): Query
    {
        $query = (new Query($db))
            ->select([
                static::RESULT_COL_RESULT => "(SUM(CASE WHEN o.type = :in THEN 1 ELSE -1 END * o.summ) / SUM(CASE WHEN o.type = :in THEN 1 END * o.summ) {$this->valueOperator} {$this->value})::bool",
                static::RESULT_COL_PK => SiteUserBuilder::siteUserQueryExpression('o'),
            ])
            ->from(['o' => S2pOrders::TABLE_NAME])
            ->where([
                'o.status' => S2pOrder::STATUS_SUCCESS,
            ])
            ->andWhere(['>', 'o.summ', 0])
            ->addParams([
                'in' => S2pOrder::TYPE_IN,
            ])
            ->groupBy([
                'o.site_id',
                'o.user_id',
            ]);

        $inBuilder->setMap([
            'o.site_id' => 'intval',
            'o.user_id' => 'intval',
        ]);

        $inBuilder->filterInnerJoin($query);

        return $query;
    }
}
