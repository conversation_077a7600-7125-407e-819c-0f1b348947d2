<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\helpers\Arr;
use app\back\components\InBuilder;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\S2pOrder;
use app\back\repositories\S2pOrders;
use app\back\repositories\Users;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class CidInOutRule extends BaseCidRule
{
    #[StringInArrayValidator(self::OPERATORS)]
    public string $valueOperator = self::OPERATOR_GREATER;
    #[IntValidator]
    public int $value;

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(4, 'value', 'Value', [
                    'hint' => 'in USD',
                    'operators' => Arr::assocToIdName(self::OPERATORS),
                    'operatorPostfix' => 'Operator',
                ]),
            ],
        ];
    }

    public function getParamsSignatureParts(): array
    {
        return ['valueOperator', 'value'];
    }

    protected function getQuery(ConnectionInterface $db, InBuilder $inBuilder): Query
    {
        $inBuilder->setMap([
            'u.cid' => 'intval',
        ]);

        return (new Query($db))
            ->select([
                static::RESULT_COL_RESULT => "COALESCE((SUM(CASE WHEN o.type = :in THEN 1 ELSE -1 END * o.summ) {$this->valueOperator} {$this->value})::bool, false)",
                static::RESULT_COL_PK => 'u.cid',
            ])
            ->from($inBuilder->table('u', ['cid']))
            ->leftJoin(['u2' => Users::TABLE_NAME], 'u2.cid = u.cid')
            ->leftJoin(['o' => S2pOrders::TABLE_NAME], 'o.site_id = u2.site_id AND o.user_id = u2.user_id AND o.status = :status')
            ->addParams([
                'status' => S2pOrder::STATUS_SUCCESS,
                'in' => S2pOrder::TYPE_IN,
            ])
            ->groupBy([
                'u.cid',
            ]);
    }

    public function getLabel(): string
    {
        return "<span class=\"badge bg-secondary\">CID In-Out {$this->valueOperator} {$this->value}</span>";
    }
}
