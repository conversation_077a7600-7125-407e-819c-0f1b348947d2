<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks;

use app\back\entities\Check;

class ChecksCreateForm extends ChecksBaseForm
{
    public function create(): int
    {
        $entity = new Check();
        $this->prepareEntityToSave($entity);
        $this->checksRepo->insert($entity);
        return $entity->id;
    }

    public function complexResponse(): array
    {
        return [
            'checkForm' => $this->response(),
            'filterForm' => [],
            'rulesForms' => [],
            'rulesAddForm' => $this->ruleForm->responseAddForm(),
        ];
    }
}
