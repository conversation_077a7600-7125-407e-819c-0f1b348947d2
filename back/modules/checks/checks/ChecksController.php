<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\accessCheck\AccessCheckRole;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Json;
use app\back\components\Permission;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;

#[AccessCheckPage]
class ChecksController extends WebController
{
    public function actionData(ChecksFilterForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionCheckCreateForm(ChecksCreateForm $form): array
    {
        return $form->complexResponse();
    }

    public function actionFiltersCreateForm(FilterForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        return $form->response();
    }

    public function actionCreate(ChecksCreateForm $form, Request $request, SessionMessages $messages): array
    {
        $form->validateOrException($request->json());
        $id = $form->create();
        $this->bl()->create($request->json());

        $messages->success('Created successfully');
        return ['id' => $id];
    }

    public function actionModifyProp(ChecksModifyPropForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->update();

        $messages->success('Updated successfully');
        $this->bl()->modify($request->json());
    }

    public function actionCheckModifyForm(ChecksModifyForm $form, Request $request, BaseAuthAccess $auth): array
    {
        return $form->complexResponse($request->json(), $auth);
    }

    public function actionModify(ChecksModifyForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->update();
        $messages->success('Updated successfully');
    }

    public function actionRuleForm(RuleForm $form, Request $request, BaseAuthAccess $auth): array
    {
        $form->validateOrException($request->json());
        return $form->response($auth);
    }

    public function actionClone(ChecksCloneForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->clone();
        $this->bl()->create($request->json());
        $messages->success('Cloned successfully');
    }

    public function actionDelete(ChecksDeleteForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $form->delete();

        $messages->success('Deleted successfully');
        $this->bl()->delete($request->json());
    }

    public function actionTest(RuleTestForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->json());
        $messages->info(Json::encode($form->test()));
    }

    #[AccessCheckRole(Permission::PERM_VIEW_SQL)]
    public function actionShowSql(RuleTestForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        return ['sql' => $form->sql()];
    }
}
