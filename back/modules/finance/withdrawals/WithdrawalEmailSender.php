<?php

declare(strict_types=1);

namespace app\back\modules\finance\withdrawals;

use app\back\components\Emailer;
use app\back\components\helpers\Html;
use app\back\entities\User;
use app\back\repositories\YhOperators;
use app\back\repositories\Sites;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mime\Email;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class WithdrawalEmailSender
{
    private const int EMAIL_SEND_LIMIT_RUB = 1000;

    public function __construct(
        private readonly Emailer $emailer,
        private readonly ConnectionInterface $db,
        private readonly LoggerInterface $log,
        private readonly YhOperators $operatorsRepo,
        private readonly Sites $sitesRepo,
    ) {
    }

    public function send(int $siteId, string $transactionId): void
    {
        $data = $this->getMailData($siteId, $transactionId);

        if ($data === null) {
            return;
        }

        $content = Html::renderPhpFile(APP_ROOT . 'views/email/withdrawal.php', $data);

        $content = $this->emailer->wrapHtml($content);

        $email = (new Email())
            ->from($this->emailer->getFromAddress())
            ->to($data['operatorEmail'])
            ->subject('Ultra user withdrawal')
            ->html($content);

        $this->emailer->send($email);
    }

    private function getMailData(int $siteId, string $transactionId): ?array
    {
        $userTransaction = (new Query($this->db))
            ->select(['site_id', 'user_id', 'amount_rub'])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->where([
                'site_id' => $siteId,
                'transaction_id' => $transactionId,
            ])
            ->one();

        if ($userTransaction === null) {
            $this->log->error("Unable to find user stat for withdrawal email. Site: $siteId, transactionId: $transactionId");
            return null;
        }

        $user = (new Query($this->db))
            ->select(['personal_manager', 'status', 'active_status', 'is_rm'])
            ->from(['us' => Users::TABLE_NAME])
            ->where([
                'site_id' => $userTransaction['site_id'],
                'user_id' => $userTransaction['user_id'],
            ])
            ->one();

        if ($user === null) {
            $this->log->error("Unable to find user for withdrawal email. Site: {$userTransaction['site_id']}, userId: {$userTransaction['user_id']}");
            return null;
        }

        if ($user['status'] !== User::STATUS_ULTRA) {
            return null;
        }

        if ($userTransaction['amount_rub'] < self::EMAIL_SEND_LIMIT_RUB) {
            return null;
        }

        if (empty($user['personal_manager'])) {
            return null;
        }

        $operatorEmail = $this->operatorsRepo->getEmailById($user['personal_manager']);
        if (empty($operatorEmail)) {
            return null;
        }

        return [
            'operatorEmail' => $operatorEmail,
            'siteName' => $this->sitesRepo->getShortNameById($userTransaction['site_id']),
            'userId' => $userTransaction['user_id'],
            'userHref' => User::playerHref($userTransaction['site_id'], $userTransaction['user_id']),
            'totalSumRub' => $userTransaction['amount_rub'],
        ];
    }
}
