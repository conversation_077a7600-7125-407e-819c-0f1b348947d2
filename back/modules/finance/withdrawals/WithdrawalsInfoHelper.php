<?php

declare(strict_types=1);

namespace app\back\modules\finance\withdrawals;

use app\back\components\helpers\Json;
use app\back\entities\UserSpecialInfo;
use app\back\entities\UserTransaction;
use app\back\entities\Withdrawal;
use app\back\repositories\UserTransactions;
use app\back\repositories\Withdrawals;
use Yiisoft\Db\Query\Query;

trait WithdrawalsInfoHelper
{
    private function collectExtraInfo(array $transactionIds): WithdrawalsExtraInfo
    {
        $info = new WithdrawalsExtraInfo();

        /** @var UserSpecialInfo $usi */
        $usi = $this->userSpecialInfos->findOneOr404([
            'site_id' => $this->siteId,
            'user_id' => $this->userId
        ]);

        $info->dep_lt_usd = $usi->dep_lt_usd;
        $info->wd_lt_usd = $usi->wd_lt_usd;
        $info->pay_bonus_ratio_lt = $usi->pay_bonus_ratio_lt;

        $totalInfo = (new Query($this->db))
            ->select([
                'total_sum_usd' => 'SUM(ut.amount_usd)',
                'first_created_at' => 'MIN(ut.created_at)',
            ])
            ->from(['ut' => UserTransactions::TABLE_NAME])
            ->where([
                'ut.site_id' => $this->siteId,
                'ut.transaction_id' => $transactionIds,
            ])
            ->one();

        $info->transaction_created_at = new \DateTimeImmutable($totalInfo['first_created_at']);
        $info->decision_made_at = new \DateTimeImmutable();

        $info->total_sum_usd = $totalInfo['total_sum_usd'];

        $lastBonus = (new Query($this->db))
            ->select(['ut.amount_usd'])
            ->from(['ut' => UserTransactions::TABLE_NAME])
            ->where([
                'site_id' => $this->siteId,
                'user_id' => $this->userId,
                'dir' => UserTransaction::DIR_IN,
                'ext_type' => [UserTransaction::EXT_TYPE_PRIZE, UserTransaction::EXT_TYPE_COMPENSATION, UserTransaction::EXT_TYPE_EXCHANGE, UserTransaction::EXT_TYPE_CASH_BACK],
                'status' => UserTransaction::STATUS_SUCCESS,
            ])
            ->orderBy(['ut.created_at' => SORT_DESC])
            ->limit(1)
            ->scalar();

        if ($lastBonus !== false) {
            $info->last_bonus_usd = $lastBonus;
        }

        $prevInfo = (new Query($this->db))
            ->select(['id', 'extra'])
            ->from(['w' => Withdrawals::TABLE_NAME])
            ->where([
                'AND',
                ['site_id' => $this->siteId],
                ['user_id' => $this->userId],
                ['status' => Withdrawal::STATUS_SYNCED],
                ['decision' => Withdrawal::DECISION_ALLOW],
            ])
            ->orderBy(['created_at' => SORT_DESC])
            ->limit(1)
            ->one();

        if ($prevInfo && !empty($prevInfo['extra'])) {
            $prevExtra = Json::decode($prevInfo['extra'], false);

            $info->prev_dep_lt_usd = $prevExtra->dep_lt_usd ?? '';
        }

        return $info;
    }
}
