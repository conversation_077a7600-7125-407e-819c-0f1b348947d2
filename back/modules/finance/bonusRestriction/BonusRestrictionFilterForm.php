<?php

declare(strict_types=1);

namespace app\back\modules\finance\bonusRestriction;

use app\back\components\helpers\Json;
use app\back\components\Permission;
use app\back\components\RichTable;
use app\back\entities\jsonTypes\BonusRestrictionSetting;
use app\back\modules\finance\components\bonus\BonusTypes;
use app\back\modules\finance\components\bonus\RestrictionManager;
use app\back\repositories\AuthAssignments;
use app\back\repositories\AuthRolePermissions;
use app\back\repositories\BonusRestrictions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class BonusRestrictionFilterForm
{
    use RichTable;

    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
        $this->sort = 'name';
    }

    public function additionalResponse(): array
    {
        return [
            'periods' => BonusRestrictionSetting::PERIODS,
            'bonuses' => $this->bonuses(),
        ];
    }

    protected function blocks(): array
    {
        return [];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Name', 'code' => 'name', 'slotName' => 'name', 'sortable' => true],
            ['name' => 'Edit', 'slotName' => 'edit'],
            ... $this->periodColumns(),
            ['name' => 'Roles', 'code' => 'roles', 'slotName' => 'roles'],
            ['name' => 'Direct assignments', 'code' => 'employees', 'slotName' => 'employees'],
            ['name' => 'Delete', 'slotName' => 'delete'],
        ];
    }

    protected function data(): array
    {
        $permission = new Expression('(:prefix || br.id)', ['prefix' => Permission::PERM_BONUS_RESTRICTION_PREFIX]);

        $rows = (new Query($this->db))
            ->select([
                'id' => 'br.id',
                'name' => 'br.name',
                'settings' => 'br.settings',
                'roles' => "STRING_AGG(arp.role, ',')",
                'employees' => 'COUNT(DISTINCT aa.employee_id)',
            ])
            ->from(['br' => BonusRestrictions::TABLE_NAME])
            ->leftJoin(['arp' => AuthRolePermissions::TABLE_NAME], ['arp.permission' => $permission])
            ->leftJoin(['aa' => AuthAssignments::TABLE_NAME], ['aa.item_name' => $permission])
            ->groupBy(['br.id'])
            ->offset($this->getOffset())
            ->limit($this->getLimit())
            ->orderBy($this->getOrderMap())
            ->all();

        foreach ($rows as &$row) {
            $row['settings'] = Json::decode($row['settings']);
            $row['isUsed'] = ((int)$row['employees'] > 0 || $row['roles'] !== null);
            $row['hrefEmployees'] = '/back/employees?permission=,' . Permission::PERM_BONUS_RESTRICTION_PREFIX . $row['id'];
        }
        return $rows;
    }

    private function periodColumns(): array
    {
        $result = [];
        foreach (BonusRestrictionSetting::PERIODS as $periodCode) {
            $columnName = ucfirst($periodCode) . ', ' . RestrictionManager::CURRENCY;
            $result[] = ['name' => $columnName, 'code' => $periodCode, 'slotName' => $periodCode, 'align' => 'start'];
        }
        return $result;
    }

    private function bonuses(): array
    {
        $result = [];
        foreach (BonusRestrictionSetting::BONUS_TYPES as $bonus) {
            $result [] = [
                'icon' => BonusRestrictionSetting::BONUS_ICONS[$bonus],
                'name' => BonusTypes::ACTIVE_BONUSES[$bonus],
                'type' => $bonus,
            ];
        }
        return $result;
    }
}
