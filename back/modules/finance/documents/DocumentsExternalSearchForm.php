<?php

declare(strict_types=1);

namespace app\back\modules\finance\documents;

use app\back\components\AllowedLists;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\AllowedSitesValidator;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\IntValidator;
use app\back\entities\UserDocument;
use app\back\entities\UserKyc;
use app\back\entities\UserWallet;
use app\back\repositories\Countries;
use app\back\repositories\Sites;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserKycs;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserWallets;
use Yiisoft\Db\Query\Query;

class DocumentsExternalSearchForm
{
    use FormGrid;

    public const string TAG_TRASH = 'trash';

    #[AllowedSiteValidator]
    public ?int $siteId = null;
    #[IntValidator]
    public ?int $userId = null;
    #[AllowedSitesValidator]
    public null|string|array $filterSiteId = null;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly FileStorage $storage,
        private readonly UserDocumentsActive $userDocuments,
        private readonly Countries $countriesRepo,
        private readonly SiteUserBuilder $siteUserBuilder,
        private readonly Sites $sitesRepo,
        private readonly UserWallets $userWalletsRepo,
    ) {
    }

    protected function blocks(): array
    {
        $countries = $this->countriesRepo->getQuantityOrderedNames($this->userDocuments::TABLE_NAME, 'country');
        $countriesTop10 = array_keys(array_slice($countries, 0, 10));
        $countriesTop10 = array_combine($countriesTop10, $countriesTop10);

        return [
            [
                $this->radioListCell(5, 'country_top', 'Documents country', [
                    'list' => Arr::assocToIdName($countriesTop10),
                    'ignoreNotExists' => true,
                    'buttonStyle' => 'btn btn-outline-secondary z-index-0'
                ]),
                $this->selectCell(5, 'country', '', [
                    'list' => Arr::assocToIdName($countries),
                    'multiple' => false,
                ]),
                $this->submitCell(2, 'Approve', [
                    'buttonIcon' => 'icn-thumb-up',
                ]),
            ],
        ];
    }

    public function search(): array
    {
        $response = $this->response();
        $response['awaitApproveCount'] = (new Query($this->userDocuments->db))
            ->from(UserDocumentsActive::TABLE_NAME)
            ->where(['IS NOT', 'tags_recognition_request_at', null])
            ->count();

        $walletSubQuery = $this->userWalletsRepo->walletBalancesSubQuery('u', UserWallet::TYPE_REAL);

        /** @var $user array */
        $query = (new Query($this->userDocuments->db))->select([
                'site_id' => 'ud.site_id',
                'user_id' => 'ud.user_id',
                'exists' => '(u.user_id IS NOT NULL)',
                'locale' => 'u.locale',
                'country' => 'u.country',
                'currency' => 'uw.currency',
            ])
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.site_id = ud.site_id AND u.user_id = ud.user_id')
            ->join("LEFT JOIN LATERAL", ['uw' => $walletSubQuery], 'true');

        if ($this->siteId && $this->userId) {
            $query->andWhere([
                'ud.site_id' => $this->siteId,
                'ud.user_id' => $this->userId,
            ]);
        } else {
            $query
                ->leftJoin(['kyc' => UserKycs::TABLE_NAME], 'kyc.site_id = u.site_id AND kyc.user_id = u.user_id')
                ->innerJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = ud.site_id AND usi.user_id = ud.user_id AND usi.dep_lt_count > 0')
                ->andWhere(['IS NOT', 'tags_recognition_request_at', null])
                ->andWhere([
                    'ud.site_id' => !empty($this->filterSiteId) ? $this->filterSiteId : array_keys($this->allowedLists->sites()),
                    "kyc.kyc_status" => [
                        null,
                        UserKyc::KYC_VERIFIED,
                        UserKyc::KYC_NOT_VERIFIED,
                        UserKyc::KYC_FAILED,
                    ]
                ]);
        }

        // get random user from first 1000
        // quick fix to allow parallel approving for several managers in same time
        $user = (new Query($this->userDocuments->db))
            ->from($query->limit(1000))
            ->orderBy('RANDOM()')
            ->one();

        if ($user === null) {
            if ($this->siteId && $this->userId) {
                $response['siteId'] = $this->siteId;
                $response['userId'] = $this->userId;
                $response['foundUser'] = false;
                $response['siteShortName'] = $this->sitesRepo->getShortNameById($this->siteId);
            }
            $response['files'] = [];
            return $response;
        }

        $response['siteId'] = $user['site_id'];
        $response['userId'] = $user['user_id'];
        $response['siteUser'] = $this->siteUserBuilder->siteUserToValue($user['site_id'], $user['user_id']);
        $response['foundUser'] = $user['exists'];
        $response['currency'] = $user['currency'];
        $response['country'] = $user['country'];
        $response['locale'] = $user['locale'];
        $response['tags'] = UserDocument::TAGS;
        $response['tagTrash'] = self::TAG_TRASH;
        $response['files'] = [];

        $docs = $this->userDocuments->findEach([
            'site_id' => $user['site_id'],
            'user_id' => $user['user_id'],
        ]);

        $validTags = array_keys(UserDocument::TAGS);
        foreach ($docs as $doc) {
            /** @var UserDocument $doc */
            $tags = $doc->tags->toArray();

            $response['files'][] = [
                'id' => $doc->id,
                'url' => $this->storage->getPublicUrlByKey($doc->storagePath()),
                'filename' => $doc->filename,
                'date' => $doc->created_at?->format(DateHelper::DATETIME_FORMAT_PHP),
                'tags' => array_values(array_intersect($tags, $validTags)),
                'tagsExternal' => array_values(array_diff($tags, $validTags)),
                'country' => $doc->country,
                'needApprove' => $doc->tags_recognition_request_at !== null,
            ];
        }

        return $response;
    }
}
