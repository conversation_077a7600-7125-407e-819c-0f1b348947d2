<?php

declare(strict_types=1);

namespace app\back\modules\finance\withdrawalsAutoProcessing\components\steps;

use app\back\entities\UserTransaction;
use app\back\modules\finance\withdrawalsAutoProcessing\components\WithdrawalToProcess;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class SafeRequisiteStep extends BaseRuleStep
{
    public function check(ConnectionInterface $db, WithdrawalToProcess $wd): ?string
    {
        $depsCount = (int) (new Query($db))
            ->select('count(*)')
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->where([
                'site_id' => $wd->site_id,
                'user_id' => $wd->user_id,
                'op_id' => UserTransaction::OP_IN,
                'status' => UserTransaction::STATUS_SUCCESS,
                'wallet' => $wd->wallet,
            ])
            ->scalar();


        if ($depsCount === 0) {
            return "User $wd->user_id wallet $wd->wallet is not safe";
        }

        return null;
    }

    public static function examples(): array
    {
        return [
            'SafeRequisite - Вывод на тот же реквизит, с которого был успешный депозит',
        ];
    }
}
