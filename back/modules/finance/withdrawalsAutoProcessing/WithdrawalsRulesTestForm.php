<?php

declare(strict_types=1);

namespace app\back\modules\finance\withdrawalsAutoProcessing;

use app\back\components\Form;
use app\back\components\SessionMessages;
use app\back\components\validators\IdValidator;
use app\back\entities\WithdrawalRule;
use app\back\modules\finance\withdrawalsAutoProcessing\components\WithdrawalsAutoProcessor;
use app\back\repositories\WithdrawalRules;

class WithdrawalsRulesTestForm
{
    use Form;

    #[IdValidator]
    public int $id;

    public function __construct(
        private readonly WithdrawalRules $withdrawalRules,
        private readonly WithdrawalsAutoProcessor $withdrawalsAutoProcessor,
        private readonly SessionMessages $sessionMessages,
    ) {
    }

    public function test(): array
    {
        /** @var WithdrawalRule $rule */
        $rule = $this->withdrawalRules->findOneOr404(['id' => $this->id]);

        $result = $this->withdrawalsAutoProcessor->processRule($rule, new \DateTimeImmutable('-1 day'), new \DateTimeImmutable('now'), true);

        $this->sessionMessages->info("Finished $result->rows_finished from $result->rows_processed");

        return $result->asTable();
    }
}
