<?php

declare(strict_types=1);

namespace app\back\modules\finance\components\bonus\forms;

use app\back\components\validators\BooleanValidator;
use app\back\components\validators\CallableValidator;

trait IgnoreProductLimitsTrait
{
    public bool $ignoreProductMaxBet = false; // not active due to @samopal request
    #[BooleanValidator]
    #[CallableValidator([self::class, 'isAllowedIgnoreProductSettings'])]
    public bool $ignoreProductMaxTransfer = false;

    protected function ignoreProductLimitsFields(): array
    {
        if ($this->isAdvancedMode === false) {
            return []; // access restricted due to @samopal request
        }

        return  [
            ['width' => 6, 'name' => 'ignoreProductMaxTransfer', 'title' => 'Ignore product max transfer'],
        ];
    }

    public static function isAllowedIgnoreProductSettings(bool $value, self $form): ?string
    {
        if ($value === true && $form->isAdvancedMode === false) {
            return 'required "Advanced bonus mode"';
        }

        return null;
    }
}
