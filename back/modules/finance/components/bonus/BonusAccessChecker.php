<?php

declare(strict_types=1);

namespace app\back\modules\finance\components\bonus;

use app\back\components\BaseAuthAccess;
use app\back\components\Permission;

readonly class BonusAccessChecker
{
    public function __construct(
        private BaseAuthAccess $authAccess,
    ) {
    }

    public function allowAssignBonusesInFullMode(): bool
    {
        return $this->authAccess->can(Permission::PERM_ADVANCED_BONUS_MODE);
    }

    public function isBonusAvailable(string $bonusType): bool
    {
        return $this->authAccess->can(Permission::PERM_ASSIGN_PRIZE_PREFIX . $bonusType);
    }
}
