<?php

declare(strict_types=1);

namespace app\back\modules\events\rowDatas;

use app\back\entities\UserContact;

/** @see UserContact */
class UserContactAddRow extends BaseEventDataRow
{
    public int $id;
    public int $user_id;
    public int $type;
    public string $value;
    public ?bool $private = false;
    public ?bool $no_contacts = false;
    public ?int $source_id = null;

    /** Added while procession */
    public ?int $variant_id = null;
    public ?int $platform_id = null;
}
