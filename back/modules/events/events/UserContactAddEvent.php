<?php

declare(strict_types=1);

namespace app\back\modules\events\events;

use app\back\components\validators\IntInArrayValidator;
use app\back\entities\UserContact;
use app\back\modules\events\rowDatas\UserContactAddRow;

class UserContactAddEvent extends BaseEvent
{
    #[IntInArrayValidator([UserContact::TYPE_PHONE], true)]
    public int $type;

    /** @param UserContactAddRow $row */
    public function filterOne($row): bool
    {
        if ($row->source_id !== UserContact::SOURCE_PROFILE) {
            return false;
        }

        if ($row->type !== $this->type) {
            return false;
        }

        return true;
    }

    public function uniqueKeyColumns(): array
    {
        return ['id'];
    }

    public static function getRowClassName(): string
    {
        return UserContactAddRow::class;
    }

    public static function isOneTimeEvent(): bool
    {
        return true;
    }

    public function getResultColumns(): array
    {
        return ['site_id', 'user_id'];
    }
}
