<?php

declare(strict_types=1);

namespace app\back\modules\events\events;

use app\back\entities\UserContact;
use app\back\modules\events\rowDatas\BaseEventDataRow;
use app\back\modules\events\rowDatas\UserContactAddRow;
use app\back\repositories\Useragents;
use app\back\repositories\UserContacts;
use Yiisoft\Db\Query\Query;

class UserContactSubscriptionAddEvent extends BaseEvent
{
    use DbAwareTrait;

    protected function afterFilterRows(array $rows): array
    {
        $this->appendVariantIdPlatformId($rows);

        return $rows;
    }

    /** @param UserContactAddRow $row */
    public function filterOne(BaseEventDataRow $row): bool
    {
        if ($row->type !== UserContact::TYPE_SUBSCRIPTION) {
            return false;
        }

        return true;
    }

    public function uniqueKeyColumns(): array
    {
        return ['id'];
    }

    public static function getRowClassName(): string
    {
        return UserContactAddRow::class;
    }

    public static function isOneTimeEvent(): bool
    {
        return true;
    }

    public function getResultColumns(): array
    {
        return ['site_id', 'user_id', 'variant_id', 'platform_id'];
    }

    private function appendVariantIdPlatformId(array &$rows): void
    {
        $siteUsersTable = $this->createUniqueValuesTable($rows, ['id' => 'int']);

        $lastDepsRows = (new Query($this->db))
            ->select([
                'i.id',
                'ua.variant_id',
                'ua.platform_id',
            ])
            ->from($siteUsersTable)
            ->innerJoin(['uc' => UserContacts::TABLE_NAME], 'uc.id = i.id')
            ->leftJoin(['ua' => Useragents::TABLE_NAME], 'ua.id = uc.useragent_id')
            ->all();

        $this->appendRowsByKey($rows, $lastDepsRows, ['id'], ['variant_id', 'platform_id']);
    }
}
