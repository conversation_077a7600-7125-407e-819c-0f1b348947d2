<?php

declare(strict_types=1);

namespace app\back\modules\events\events;

use app\back\components\Form;
use app\back\components\helpers\Arr;
use app\back\components\validators\IdValidator;
use app\back\modules\events\rowDatas\BaseEventDataRow;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

/**
 * @method beforeFilterRows(BaseEventDataRow[] $rows): array (Can be used to add extra data to rows for further filtering)
 * @method afterFilterRows(BaseEventDataRow[] $rows): void (Can be used to add extra data to rows after filtering)
 */
abstract class BaseEvent implements LoggerAwareInterface
{
    use Form;

    #[IdValidator]
    public int $site_id;

    protected LoggerInterface $log;

    protected string $defaultPeriod = '2 hours';

    /**
     * Return true if row matches subscription params.
     * False otherwise
     * Exact type must be hinted for all children
     */
    abstract public function filterOne(BaseEventDataRow $row): bool;

    /** Columns for unique key of data row, used for checking if that data already generated particular event for one-time events */
    abstract public function uniqueKeyColumns(): array;

    /** Row data type this event working with */
    abstract public static function getRowClassName(): string;

    /** "True" means this event can trigger only once for particular data (unique key) */
    abstract public static function isOneTimeEvent(): bool;

    /** Returns array of keys that appear in result data */
    abstract public function getResultColumns(): array;

    public function setLogger(LoggerInterface $logger): void
    {
        $this->log = $logger;
    }

    /**
     * Checks input data and return only rows that match subscribed params
     * @see DataQueueTrait::getChanges()
     */
    final public function filterRows(array $rawData): array
    {
        $rowClassName = static::getRowClassName();

        /** @var BaseEventDataRow[] $dataRows */
        $dataRows = array_map(static fn (array $r) => new $rowClassName($r), $rawData);

        // Pre-filtering by site_id
        $dataRows = array_filter($dataRows, fn ($row) => $row->site_id === $this->site_id);

        if (method_exists($this, 'beforeFilterRows')) {
            $dataRows = $this->beforeFilterRows($dataRows);
        }

        $filteredRows = [];
        foreach ($dataRows as $row) {
            $prevState = isset($row->oldRow) && $this->filterOne($row->oldRow);
            $currState = $this->filterOne($row);

            if (!$prevState && $currState) {
                $filteredRows[] = $row;
            }
        }

        if (method_exists($this, 'afterFilterRows')) {
            $this->afterFilterRows($filteredRows);
        }

        return $filteredRows;
    }

    final public function getUniqueKey(BaseEventDataRow $row): string
    {
        return implode('-', Arr::leaveOnlyKeys(get_object_vars($row), $this->uniqueKeyColumns(), true));
    }

    protected function getMinimalDateTime(): \DateTimeImmutable
    {
        return (new \DateTimeImmutable())->sub(\DateInterval::createFromDateString($this->defaultPeriod));
    }
}
