<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\entities\AdCredential;
use app\back\entities\Proxy;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

trait AdsCredentialsInvalidateTrait
{
    protected array $invalidatedCredentials = [];

    protected function invalidateProxyOnError(TransportExceptionInterface $e, Proxy $proxy, AdCredential $credential): void
    {
        $this->log->warning("Info before exception. Credential: {$credential->id}, Proxy: {$proxy->config}");

        if ($this->isProxyProblem($e)) {
            $isProxyUpdated = $this->adCredentialsRepo->invalidateProxy($credential, $proxy);
            $this->log->warning("Proxy update result: $isProxyUpdated");
        }
        $this->log->error('NetworkException: ' . $e->getMessage());
    }

    protected function invalidateCredential(AdCredential $credential, string $message): void
    {
        $this->invalidatedCredentials[] = "$credential->id: $credential->name";
        $this->log->warning($message);
        $this->adCredentialsRepo->invalidate($credential, $message);
    }
}
