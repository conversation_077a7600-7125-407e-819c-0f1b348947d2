<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\maintenance;

use app\back\modules\task\BaseTask;
use app\back\repositories\Users;
use Yiisoft\Db\Command\CommandInterface;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class CloudSourceResetTask extends BaseTask
{
    private CommandInterface $countCommand;

    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
        $this->countCommand = (new Query($this->db))
        ->select('COUNT(*)')
        ->from(Users::TABLE_NAME)
        ->where(['IS NOT', 'source_reset_at', null])
        ->createCommand();
    }

    public function process(): void
    {
        $from = new \DateTimeImmutable($this->from);
        $to = new \DateTimeImmutable($this->to);
        $toGlobal = new \DateTimeImmutable();
        $period = $from->diff($to);
        $resetReadyRows = $limit = (int)$this->limit ?: 100000;

        do {
            while ($resetReadyRows >= $limit && ($resetReadyRows = $this->countCommand->queryScalar()) > $limit) {
                $this->log->debug("AWAIT LIMIT $limit");
                sleep(600);
            }
            $affectedRows = 0;
            foreach ($this->usersQuery($from, $to)->batch(1000) as $rows) {
                $affectedRows += $this->db->createCommand()->update(
                    Users::TABLE_NAME,
                    ['source_reset_at' => new Expression('NOW()')],
                    ['IN', ['site_id', 'user_id'], $rows]
                )->execute();
            }
            $resetReadyRows += $affectedRows;
            $this->totalRows = $this->affectedRows += $affectedRows;

            $this->log->debug("DONE +{$affectedRows}/{$this->totalRows} {$from->format('Y-m-d')}");
            $from = $from->add($period);
            $to = $from->add($period);
        } while ($from <= $toGlobal);
    }

    private function usersQuery(\DateTimeImmutable $from, \DateTimeImmutable $to): Query
    {
        return (new Query($this->db))
            ->select(['site_id', 'user_id'])
            ->from(Users::TABLE_NAME)
            ->where([
                'AND',
                ['>=', 'date', $from->format('Y-m-d H:i:s')],
                ['<', 'date', $to->format('Y-m-d H:i:s')],
            ]);
    }
}
