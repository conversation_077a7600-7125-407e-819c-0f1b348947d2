<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Container;
use app\back\components\Initializable;
use app\back\entities\Rate;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\reports\Conversion\ConversionConfig;
use app\back\modules\reports\reports\Conversion\ConversionStatsQueryConfig;
use app\back\repositories\Sites;

class ConversionByCountriesTask extends BaseSendTask
{
    public array $siteIds;
    public string $contactName;
    public array $webmasterIds = [];

    protected bool $crypt = true;

    private string $filterDate;

    private readonly Sites $sitesRepo;
    private readonly Container $container;

    #[Initializable]
    final public function init(Sites $sitesRepo, Container $container): void
    {
        $this->sitesRepo = $sitesRepo;
        $this->container = $container;
    }

    private function getReportConfig(array $groups, array $sites = []): ConversionConfig
    {
        $this->filterDate = date('Y-m-d', $this->fromTime);

        $filters = [
            ['mode', ConversionConfig::MODE_REG],
            ['ignore', IgnoreColumn::MODE_IGNORE],
            ['date', $this->filterDate, '>='],
            ['date', $this->filterDate, '<='],
            ['action_date', $this->filterDate, '<='],
            ['action_date_type', ConversionStatsQueryConfig::DATE_TYPE_CREATED],
            ['currency', Rate::EUR],
        ];

        if (!empty($this->webmasterIds)) {
            $filters[] = ['webmaster_id', implode("\n", $this->webmasterIds), Operators::IN];
        }

        if ($sites) {
            $filters[] = ['site_id', $sites];
        }

        $report = new ConversionConfig($this->container);
        $report->sitesIsRequired = false;

        $report->loadAndValidateOrException([
            'metrics' => [
                'regs',
                'fd_count',
                'fd_sum',
                'rd_count',
                'rd_sum',
                'wd_sum',
                'd_sum',
                'reg_to_dep',
                'repeat_rate',
                'rev_per_paid',
                'ngr',
                'fd_share',
                'reg_to_tries',
                'tries_to_fd'
            ],
            'isTotals' => true,
            'isHtmlVersion' => true,
            'groups' => $groups,
            'orders' => ['fd_count' => SORT_DESC],
            'filters' => $filters,
        ]);

        return $report;
    }

    protected function getContactName(): string
    {
        return $this->contactName;
    }

    public function getSubject(): string
    {
        return "Conversion by countries ({$this->filterDate}, EUR) for {$this->sitesRepo->getSitesNames($this->siteIds)}";
    }

    public function getContent(): string
    {
        $table = '';

        if ($this->webmasterIds) {
            ['data' => $sitesData, 'columns' => $sitesCols] = $this->getReportConfig(['site_id'])->dataAndColumns();
            $table .= $this->table($sitesData, $sitesCols, 'Conversion by sites');
        }

        ['data' => $countriesData, 'columns' => $countriesCols] = $this->getReportConfig(['country'], $this->siteIds)->dataAndColumns();
        $table .= $this->table($countriesData, $countriesCols, $this->getSubject());

        return $table;
    }
}
