<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Container;
use app\back\components\helpers\DateHelper;
use app\back\components\Initializable;
use app\back\entities\Site;
use app\back\modules\reports\reports\ConfirmRate\ConfirmRateConfig;

class ConfirmRateTask extends BaseSendTask
{
    private array $parts = [
        'Confirm Rate' => ConfirmRateConfig::METRIC_CONFIRM_RATE,
        'Regs' => ConfirmRateConfig::METRIC_REGS,
        'Confirms' => ConfirmRateConfig::METRIC_CONFIRMS,
    ];

    private readonly Container $container;

    #[Initializable]
    final public function init(Container $container): void
    {
        $this->container = $container;
    }

    protected function getContactName(): string
    {
        return 'confirm_rate_emails';
    }

    public function getSubject(): string
    {
        return "Weekly confirm rate ({$this->to})";
    }

    public function getContent(): string
    {
        $tables = [];

        $reportFilters = [
            ['date_range', DateHelper::range($this->from, $this->to)],
            ['site_id', [Site::CV, Site::V24, Site::VV, Site::GMSD, Site::ARM, Site::PHB]],
            ['diff_threshold', 5],
            ['min_reg', 20, '>='],
        ];

        foreach ($this->parts as $title => $metric) {
            $report = new ConfirmRateConfig($this->container);
            $report->loadAndValidateOrException([
                'isTotals' => true,
                'isHtmlVersion' => true,
                'metrics' => [$metric],
                'filters' => $reportFilters,
            ]);

            ['data' => $totalData] = $report->dataAndColumns();

            if (count($totalData)) {
                $totalData[0]['sp_id'] = 'Total';
            }

            $report = new ConfirmRateConfig($this->container);
            $report->loadAndValidateOrException([
                'isTotals' => true,
                'isHtmlVersion' => true,
                'metrics' => [$metric],
                'groups' => ['sp_id'],
                'filters' => $reportFilters,
            ]);

            ['data' => $data, 'columns' => $cols] = $report->dataAndColumns();

            array_push($data, ...$totalData);

            $this->replaceToStyleObjects($data, $cols);

            $tables[] = $this->table($data, $cols, $title);
        }

        return implode('', $tables);
    }
}
