<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Initializable;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserIgnoreId;
use app\back\entities\UserTransaction;
use app\back\modules\api\components\Operators;
use app\back\modules\reports\columns\DateTypeColumn;
use app\back\modules\reports\reports\Payments\PaymentsConfig;

class DepositsFailedTask extends BaseSendTask
{
    private const array CONFIG = [[
        'site_id' => [Site::K7, Site::CV, Site::V24, Site::VP]
    ], [
        'site_id' => [Site::VV, Site::NVC, Site::HIT,  Site::VERDE, Site::ICG],
        'locale' => ['de','de-at','de-ch','de_DE','en','en_AU','en-ca','en_CA','en_GB','en-nz','en_US','pl','pl_PL'],
    ], [
        'site_id' => [Site::S7, Site::VOX],
        'locale' => ['pl', 'pl_PL'],
    ], [
        'site_id' => [Site::WIN],
        'locale' => ['it', 'it-ch', 'it_IT'],
    ]];

    public string $contact;

    private string $datetimeFrom;
    private string $datetimeTo;
    private readonly PaymentsConfig $report;

    #[Initializable]
    final public function init(PaymentsConfig $report): void
    {
        $this->report = $report;

        $this->datetimeFrom = date('Y-m-d H:i', $this->fromTime);
        $this->datetimeTo =  date('Y-m-d H:i', strtotime($this->to));
    }

    protected function getContactName(): string
    {
        return $this->contact;
    }

    public function getSubject(): string
    {
        return "Deposits failed ($this->datetimeFrom - $this->datetimeTo)";
    }

    public function getContent(): string
    {
        ['data' => $data, 'columns' => $cols] =
            $this->report->loadAndValidateOrException([
                'isTotals' => true,
                'isHtmlVersion' => true,
                'metrics' => [
                    'count',
                    'sum_eur',
                ],
                'groups' => [
                    'site_id',
                    'player_link',
                    'reg_brand_id',
                    'locale',
                    'phone_exists',
                ],
                'filters' => array_merge([
                    ['status', [UserTransaction::STATUS_FAIL], Operators::IN],
                    ['date_type', DateTypeColumn::UPDATED],
                    ['date_time', $this->datetimeFrom, Operators::GE],
                    ['date_time', $this->datetimeTo, Operators::LE],
                    ['op_id', [UserTransaction::OP_IN], Operators::IN],
                    ['ignore', UserIgnoreId::MODE_IGNORE],
                    ['user_status', [User::STATUS_FREE], Operators::IN],
                ], $this->sitesLocalesConfig())
            ])
            ->dataAndColumns();

        if (!$data) {
            return '';
        }

        return $this->table($data, $cols, $this->getSubject());
    }

    private function sitesLocalesConfig(): array
    {
        $res = [];
        foreach (self::CONFIG as $n => $items) {
            $blockNum = $n + 1;
            $res[] = [
                'site_id', $items['site_id'], Operators::IN, $blockNum,
            ];
            if (isset($items['locale'])) {
                $res[] = [
                    'locale', implode("\n", $items['locale']), Operators::IN, $blockNum,
                ];
            }
        }

        return $res;
    }
}
