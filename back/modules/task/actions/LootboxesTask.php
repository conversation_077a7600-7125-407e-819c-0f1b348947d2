<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Lootboxes;

class LootboxesTask extends ImportTask
{
    use TaskWithDefaultGetData;

    public function __construct(
        private readonly Lootboxes $lootboxesRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    protected function repository(): Lootboxes
    {
        return $this->lootboxesRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();

        return true;
    }
}
