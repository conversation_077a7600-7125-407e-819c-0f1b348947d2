<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import;

use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\entities\UserTicket;
use app\back\entities\UserTicketFile;
use app\back\modules\task\requests\JiraRequest;
use app\back\modules\task\components\JiraParseHelper;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\Sites;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketFiles;
use app\back\repositories\UserTicketLogs;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

/** @property JiraRequest $request
 * @see https://support.atlassian.com/jira-software-cloud/docs/jql-fields/
 * */
class UsersTicketsJiraTask extends ImportTask
{
    private array $ticketsFiles = [];
    private array $ticketsLog = [];

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Sites $sitesRepo,
        private readonly UserTickets $userTicketsRepo,
        private readonly UserTicketFiles $userTicketsFilesRepo,
        private readonly UserTicketLogs $userTicketsLogsRepo,
    ) {
    }

    protected function repository(): BaseRepository
    {
        return $this->userTicketsRepo;
    }

    protected function getData(): iterable
    {
        $issueType = JiraParseHelper::JIRA_TYPE_LOST_DEPOSIT_ID;
        $from = JiraParseHelper::parseDateToJira($this->from);
        $to = JiraParseHelper::parseDateToJira($this->to);

        return $this->createRequest([
            'jql' => "issuetype=$issueType AND (updated >= '{$from}' AND updated < '{$to}') ORDER BY key ASC",
            'fields' => 'status,issuetype,assignee,summary,priority,attachment,updated,created,creator,customfield_17200,customfield_17201,customfield_12902,customfield_11704,customfield_26104',
            'expand' => 'changelog',
        ])->finalData();
    }

    protected function beforeFind(array &$row): bool
    {
        if (!isset($row['site'])) {
            return false;
        }

        $row['site_id'] = $this->sitesRepo->getIdByShortName($row['site']['value']);

        if ($row['site_id'] === null) {
            $this->log->debug("Unknown site name '{$row['site']['value']}' in issue {$row['jira_key']}");
            return false;
        }

        if (empty($row['user_id']) || !is_numeric($row['user_id'])) {
            $this->log->debug("User id empty or invalid: {$row['user_id']}");
            return false;
        }

        if (!array_key_exists($row['status']['name'], JiraParseHelper::JIRA_ANALYTICS_STATUSES)) {
            $this->log->debug("Unknown jira status '{$row['status']['name']}' in issue {$row['jira_key']}");
            return false;
        }

        if (!array_key_exists($row['type']['id'], JiraParseHelper::JIRA_ANALYTICS_TYPES)) {
            $this->log->debug("Unknown jira issue type '{$row['type']['id']}'  in issue {$row['jira_key']}");
            return false;
        }

        if (empty($row['invoice_id']) || !preg_match('#[a-zA-Z0-9-]+#', $row['invoice_id']) || strlen($row['invoice_id']) < 3 || strlen($row['invoice_id']) > 36) {
            $invoiceId = $row['invoice_id'] ?? '';
            $this->log->debug("Invalid invoice id '$invoiceId' in issue {$row['jira_key']}");
            return false;
        }

        $row['status'] = JiraParseHelper::JIRA_ANALYTICS_STATUSES[$row['status']['name']];

        $row['type'] = JiraParseHelper::JIRA_ANALYTICS_TYPES[$row['type']['id']];

        $row['created_at'] = JiraParseHelper::parseDateFromJira($row['created_at']);

        $row['updated_at'] = JiraParseHelper::parseDateFromJira($row['updated_at']);

        $row['creator'] = JiraParseHelper::parseEmail($row['creator']);

        $row['source'] = UserTicket::SOURCE_JIRA;

        $row['priority'] = JiraParseHelper::JIRA_ANALYTICS_PRIORITIES[$row['priority']['id']];

        if (array_key_exists('assignee', $row)) {
            $row['assignee'] = JiraParseHelper::parseEmail($row['assignee']);
        }

        if (array_key_exists('attachments', $row)) {
            $this->ticketsFiles[$row['jira_key']] = $this->parseTicketFiles($row);
        }

        $this->ticketsLog[$row['jira_key']] = JiraParseHelper::parseHistory($row);

        unset($row['history'], $row['creator'], $row['site']);

        return true;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        $jiraKeys = array_column($rows, 'jira_key');

        $rows = $this->keepDatesForExistingTickets($rows, $jiraKeys);

        $affected = $this->userTicketsRepo->batchUpsert($rows, ['upserted_at' => new Expression('NOW()')], '(jira_key)');

        $existingTicketsIds = $this->getColumnsFromExistingTickets(['id'], $jiraKeys); // get id of existing + newly inserted tickets

        $logs = $this->setTicketIdFromExistingTickets($this->ticketsLog, $existingTicketsIds);

        $affected += $this->userTicketsLogsRepo->batchUpsert($logs, [], '(ticket_id, status, source, created_at)');

        $files = $this->skipAlreadyDownloadedFiles($this->ticketsFiles);

        $files = $this->setTicketIdFromExistingTickets($files, $existingTicketsIds);

        $affected += $this->userTicketsFilesRepo->batchUpsert($files, [], '(jira_file_id)');

        return $affected;
    }

    private function keepDatesForExistingTickets(array $rows, array $jiraKeys): array
    {
        $existingUpdatedAts = $this->getColumnsFromExistingTickets(['created_at', 'updated_at', 'source'], $jiraKeys);

        foreach ($rows as &$row) {
            if (isset($existingUpdatedAts[$row['jira_key']])) {
                $existingData = $existingUpdatedAts[$row['jira_key']]; //don't overwrite created_at for already existing tickets
                $row['created_at'] = $existingData['created_at'];
                if (strtotime($row['updated_at']->format(DateHelper::DATETIME_FORMAT_PHP)) < strtotime($existingData['updated_at'])) {
                    $row['updated_at'] = $existingData['updated_at']; // don't overwrite updated_at if it newer then imported
                    $row['source'] = $existingData['source'];
                }
            }
        }

        return $rows;
    }

    private function setTicketIdFromExistingTickets(array $toSetId, array $existingTicketsIds): array
    {
        $result = [];
        foreach ($toSetId as $jiraKey => $records) {
            if (!array_key_exists($jiraKey, $existingTicketsIds)) {
                continue;
            }
            foreach ($records as $record) {
                $record['ticket_id'] = $existingTicketsIds[$jiraKey]['id'];
                $result[] = $record;
            }
        }

        return $result;
    }

    private function getColumnsFromExistingTickets(array $columns, array $jiraKeys): array
    {
        $result = (new Query($this->db))
            ->select(['jira_key', ...$columns])
            ->from(UserTickets::TABLE_NAME)
            ->where(['jira_key' => $jiraKeys])
            ->all();

        return Arr::index($result, 'jira_key');
    }

    private function parseTicketFiles(array $row): array
    {
        $result = [];
        foreach ($row['attachments'] as $attachment) {
            if (!array_key_exists($attachment['mimeType'], UsersTicketsFilesBaseTask::MIME_TYPES_ALLOWED)) {
                $this->log->debug("Forbidden mime type {$attachment['mimeType']} in issue {$row['jira_key']}");
                continue;
            }

            $result[] = [
                'jira_file_id' => $attachment['id'],
                'extension' => UsersTicketsFilesBaseTask::MIME_TYPES_ALLOWED[$attachment['mimeType']],
                'original_name' => urldecode($attachment['filename']),
                'created_at' => JiraParseHelper::parseDateFromJira($attachment['created']),
                'sync_status' => UserTicketFile::SYNC_TO_DOWNLOAD,
                'source' => UserTicket::SOURCE_JIRA,
            ];
        }

        return $result;
    }

    private function skipAlreadyDownloadedFiles(array $files): array
    {
        $downloadedFiles = (new Query($this->db))
            ->select(['jira_file_id'])
            ->from(UserTicketFiles::TABLE_NAME)
            ->where(['sync_status' => UserTicketFile::SYNC_SUCCESS])
            ->andWhere(['IS NOT', 'jira_file_id', null])
            ->all();

        return array_filter($files, static fn($file) =>
            !isset($file['jira_file_id']) || !in_array($file['jira_file_id'], $downloadedFiles, true));
    }
}
