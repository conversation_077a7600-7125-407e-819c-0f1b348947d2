<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\crm;

use app\back\components\helpers\Arr;
use app\back\entities\CrmLetter;
use app\back\modules\task\actions\TaskWithSplitFromToRequest;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\CrmLetters;
use app\back\repositories\UserContacts;

class CrmLettersOpensTask extends ImportTask
{
    use TaskWithSplitFromToRequest;
    use CrmLettersContactIdTrait;

    protected array $validateOnlyPropNames = ['bulk_id', 'site_id', 'user_id', 'contact_id', 'opened_at', 'clicked_at'];

    protected string $propToUpdate = 'opened_at';

    public function __construct(
        private readonly CrmLetters $crmLettersRepo,
        private readonly UserContacts $userContactsRepo,
    ) {
    }

    protected function getData(): iterable
    {
        foreach (Arr::batchIterable($this->getSplitData(), $this->batchSize()) as $batch) {
            yield from $this->appendContactId($batch);
        }
    }

    protected function repository(): BaseRepository
    {
        return $this->crmLettersRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        if (empty($row['contact_id'])) {
            return false;
        }

        /** @var CrmLetter $entity */
        $entity = $this->crmLettersRepo->findOne([
            'bulk_id' => $row['bulk_id'],
            'user_id' => $row['user_id'],
            'contact_id' => $row['contact_id'],
        ]);

        if ($entity === null) {
            return false;
        }

        if (!empty($entity->{$this->propToUpdate}) && $row[$this->propToUpdate] >= $entity->{$this->propToUpdate}->format('Y-m-d H:i:s')) {
            return false;
        }

        return true;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['bulk_id', 'site_id', 'user_id', 'contact_id'], $this->propToUpdate);

        return $repository->batchUpdateDistinct($rows, [], ['bulk_id', 'site_id', 'user_id', 'contact_id']);
    }
}
