<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\yhlp;

use app\back\modules\task\actions\TaskWithFromToRequest;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\UserSpecialInfos;

class YhlpUsersTimeZoneTask extends ImportTask
{
    use TaskWithFromToRequest;

    public function __construct(private readonly UserSpecialInfos $userSpecialInfoRepo)
    {
    }

    protected function repository(): BaseRepository
    {
        return $this->userSpecialInfoRepo;
    }
}
