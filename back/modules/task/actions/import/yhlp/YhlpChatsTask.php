<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\yhlp;

use app\back\modules\task\actions\TaskWithFromToRequest;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\Chats;
use app\back\repositories\DbLargeDictionary;
use app\back\repositories\Useragents;
use Yiisoft\Db\Connection\ConnectionInterface;

class YhlpChatsTask extends ImportTask
{
    use TaskWithFromToRequest;

    private DbLargeDictionary $useragentsDict;

    public function __construct(
        private readonly Chats $chatsRepo,
        private readonly ConnectionInterface $db,
        Useragents $useragentsRepo,
    ) {
        $this->useragentsDict = $useragentsRepo->createDictionary();
    }

    protected function beforeFind(array &$row): bool
    {
        if (!empty($row['useragent'])) {
            $row['useragent_id'] = $this->useragentsDict->getIdByName($row['useragent']);
        }

        if (empty($row['ip'])) {
            $row['ip'] = null;
        }

        if (!empty($row['user_id']) && (!ctype_digit($row['user_id']) || $row['user_id'] > PHP_INT_MAX)) {
            $row['user_id'] = null;
        }

        unset($row['locale'], $row['expired_status_id'], $row['useragent']);

        return parent::beforeFind($row);
    }

    protected function repository(): BaseRepository
    {
        return $this->chatsRepo;
    }
}
