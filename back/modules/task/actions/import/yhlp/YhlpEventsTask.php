<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\yhlp;

use app\back\modules\task\actions\TaskWithFromToRequest;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\YhEvents;

class YhlpEventsTask extends ImportTask
{
    use TaskWithFromToRequest;
    use YhlpTaskTrait;

    public function __construct(private readonly YhEvents $yhEventsRepo)
    {
    }

    protected function beforeFind(array &$row): bool
    {
        $parent = parent::beforeFind($row);

        $row['user_id'] = $this->normalizeUserId($row['user_id']);

        if (empty($row['user_id'])) {
            $row['user_id'] = null;
        }

        if (empty($row['guest_id'])) {
            $row['guest_id'] = null;
        }

        return $parent;
    }

    protected function repository(): BaseRepository
    {
        return $this->yhEventsRepo;
    }
}
