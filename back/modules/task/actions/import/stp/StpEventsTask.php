<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\stp;

use app\back\entities\StpEvent;
use app\back\modules\task\actions\TaskWithFromToRequest;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\StpEvents;
use app\back\repositories\StpNetworks;

class StpEventsTask extends ImportTask
{
    use TaskWithFromToRequest;

    private const array TYPES = [
        'install' => StpEvent::TYPE_INSTALL,
        'registration' => StpEvent::TYPE_REGISTRATION,
        'first_deposit' => StpEvent::TYPE_FIRST_DEPOSIT,
        'deposit' => StpEvent::TYPE_DEPOSIT,
    ];

    public function __construct(
        private readonly StpEvents $stpEventsRepo,
        private readonly StpNetworks $stpNetworksRepo,
    ) {
    }

    protected function repository(): BaseRepository
    {
        return $this->stpEventsRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        if (!empty($row['network_name'])) {
            $row['network_id'] = $this->stpNetworksRepo->getIdByName($row['network_name']);
        }
        unset($row['network_name']);

        if (!empty($row['type'])) {
            $row['type'] = self::TYPES[$row['type']] ?? null;
        }

        if (!empty($row['event_timestamp']) && is_numeric($row['event_timestamp'])) {
            $row['created_at'] = date('Y-m-d H:i:s', (int) $row['event_timestamp']);
        }

        foreach (['adid', 'idfa', 'idfv'] as $value) {
            if (empty($row[$value])) {
                $row[$value] = null;
            }
        }

        unset($row['event_timestamp']);

        return true;
    }
}
