<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BettingLogs;
use app\back\repositories\UserLogins;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class BettingLogFromLoginsTask extends BaseTask
{
    private const int USERS_BATCH_SIZE = 10000;

    public function __construct(
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly ConnectionInterface $db,
    ) {
    }

    public function process(): void
    {
        $data = (new Query($this->db))
            ->select([
                'bl.id',
                'ua.useragent_id',
                'ua.ip',
                'ua.refcode_id',
            ])
            ->from(['bl' => BettingLogs::TABLE_NAME])
            ->leftJoin(['ua' => UserLogins::TABLE_NAME], [
                'AND',
                'ua.site_id = bl.site_id',
                'ua.user_id = bl.user_id',
                'ua.session_token = bl.auth_token',
            ])
            ->where([
                'AND',
                ['bl.site_id' => $this->siteIdResolver->siteId()],
                ['>=', 'bl.created_at', $this->from],
                ['<', 'bl.created_at', $this->to],
            ]);

        $this->totalRows = 0;
        $this->affectedRows = 0;

        foreach ($data->each(self::USERS_BATCH_SIZE) as $record) {
            $this->affectedRows += $this->db->createCommand()
                ->update(
                    BettingLogs::TABLE_NAME,
                    [
                        'useragent_id' => $record['useragent_id'],
                        'ip' => $record['ip'],
                        'refcode_id' => $record['refcode_id'],
                    ],
                    ['id' => $record['id']]
                )
                ->execute();
            ++$this->totalRows;
        }
    }
}
