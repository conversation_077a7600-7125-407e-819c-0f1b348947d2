<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\entities\Country;
use app\back\entities\Rate;
use app\back\entities\User;
use app\back\entities\UserWallet;
use app\back\repositories\Rates;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\UserStatusNormalThresholds;
use app\back\repositories\UserWallets;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;
use Yiisoft\Db\Query\QueryInterface;

class UsersStatusesNormalTask extends BaseTask
{
    use UsersStatusesUpdateTrait;

    public string $shiftFrom = 'today';
    public string $shiftTo = 'today';

    private const int PERIOD_DAYS = 15;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Users $usersRepo,
        protected readonly UserStatusNormalThresholds $normalThresholds,
        private readonly UserWallets $userWalletsRepo,
    ) {
    }

    public function process(): void
    {
        $siteId = $this->siteIdResolver->siteId();
        $paramsByCountry = $this->normalThresholds->paramsByCountryConfig();

        foreach ($paramsByCountry as $country => $props) {
            $currency = Country::getCurrencyByCountryCode2($country);
            [$userIn, $userOut] = match ($currency) {
                Rate::USD => ['SUM(usi.dep_lt_usd)', 'SUM(usi.wd_lt_usd)'],
                Rate::EUR => ['SUM(usi.dep_lt_eur)', 'SUM(usi.wd_lt_eur)'],
                Rate::RUB => ['SUM(usi.dep_lt_rub)', 'SUM(usi.wd_lt_rub)'],
                default => [
                    'CASE WHEN(ANY_VALUE(uw.currency) = :currency) THEN SUM(usi.dep_lt_orig) ELSE SUM(usi.dep_lt_usd * r.rate) END',
                    'CASE WHEN(ANY_VALUE(uw.currency) = :currency) THEN SUM(usi.wd_lt_orig) ELSE SUM(usi.wd_lt_usd * r.rate) END',
                ],
            };

            $cloudIn = 'SUM(usi.dep_lt_eur)';
            $cloudOut = 'SUM(usi.wd_lt_eur)';
            $ngrLtAverageFor15Days = "(COALESCE($userIn, 0) - COALESCE($userOut, 0)) / NULLIF((:to - u.date::date), 0) * :period";
            $cloudInOutRate = "($cloudOut / NULLIF($cloudIn, 0)) * 100";
            $countryCondition = $country === Country::DEFAULT ? ['NOT IN', 'u.country', array_keys($paramsByCountry)] : ['u.country' => $country];
            $userParams = [
                'period' => self::PERIOD_DAYS,
                'to' => date('Y-m-d', strtotime($this->shiftTo, strtotime($this->to))),
            ];

            $steps = [
                [
                    'update' => User::ACTIVE_STATUS_LOW,
                    'user' => [
                        'having' => ['<', $ngrLtAverageFor15Days, $props['ngr_amount_active']],
                        'params' => $userParams
                    ],
                    'cloud' => [
                        'having' => [
                            'AND',
                            ['>', $cloudInOutRate, $props['ngr_cloud_percent']],
                            ['=', "COUNT((u_cloud.site_id || '-' || u_cloud.user_id)) FILTER (WHERE u_cloud.status = :vip)", 0]
                        ],
                        'params' => ['vip' => User::STATUS_VIP]
                    ],
                ],
                [
                    'update' => User::ACTIVE_STATUS_ACTIVE,
                    'user' => [
                        'having' => [
                            'AND',
                            ['>=', $ngrLtAverageFor15Days, $props['ngr_amount_active']],
                            ['<', $ngrLtAverageFor15Days, $props['ngr_amount_high']]
                        ],
                        'params' => $userParams
                    ],
                    'cloud' => [
                        'having' => ['<=', $cloudInOutRate, $props['ngr_cloud_percent']],
                    ],
                ],
                [
                    'update' => User::ACTIVE_STATUS_ACTIVE,
                    'user' => [
                        'having' => [],
                        'params' => []
                    ],
                    'cloud' => [
                        'having' => ['>=', "COUNT((u_cloud.site_id || '-' || u_cloud.user_id)) FILTER (WHERE u_cloud.status = :vip)", 1], //exception: VIP in cloud
                        'params' => ['vip' => User::STATUS_VIP]
                    ],
                ],
                [
                    'update' => User::ACTIVE_STATUS_HIGH,
                    'user' => [
                        'having' => ['>=', $ngrLtAverageFor15Days, $props['ngr_amount_high']],
                        'params' => $userParams
                    ],
                    'cloud' => [
                        'having' => ['<=', $cloudInOutRate, $props['ngr_cloud_percent']],
                    ],
                ],
            ];

            foreach ($steps as $i => $params) {
                $params['country'] = $countryCondition;
                $affected = $this->findUsers($params, $currency, $siteId);
                $this->log->debug("Country: $country, step: $i, affected: " . count($affected));
                $this->updateUsersStatus($affected, null, $params['update']);
            }
        }
    }

    protected function findUsers(array $params, string $currency, int $siteId): array
    {
        $shiftedFrom = date('Y-m-d', strtotime($this->shiftFrom, strtotime($this->from)));
        $shiftedTo = date('Y-m-d', strtotime($this->shiftTo, strtotime($this->to)));

        $where = array_merge([
            'AND',
            ['u.site_id' => $siteId],
            [
                'OR',
                [
                    'AND',
                    ['>=', 'usi.dep_last_at', $shiftedFrom],
                    ['<', 'usi.dep_last_at', $shiftedTo],
                ],
                [
                    'AND',
                    ['>=', 'usi.wd_last_at', $shiftedFrom],
                    ['<', 'usi.wd_last_at', $shiftedTo],
                ],
            ],
            ['IS DISTINCT FROM', 'u.is_manual_status', true],
            ['u.status' => User::STATUS_NORMAL],
            ['!=', 'u.active_status', $params['update']],
        ], [$params['country']]);

        $walletSubQuery = $this->userWalletsRepo->walletBalancesSubQuery('u', UserWallet::TYPE_REAL);

        $usersByUserTransactionsQuery = $this->getQuery($where, $params['user']['params'])
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u.site_id AND usi.user_id = u.user_id')
            ->join("LEFT JOIN LATERAL", ['uw' => $walletSubQuery], 'true')
            ->filterHaving($params['user']['having']);

        if (!in_array($currency, [Rate::USD, Rate::EUR, Rate::RUB], true)) {
            $rateUserQuery = (new Query($this->db))
                ->select(['rate'])
                ->from(['r' => Rates::TABLE_NAME])
                ->where('r.code = :currency', ['currency' => $currency])
                ->orderBy(['date' => SORT_DESC])
                ->limit(1);

            $usersByUserTransactionsQuery
                ->join('LEFT JOIN LATERAL', ['r' => $rateUserQuery], 'TRUE');
        }

        $usersByUserTransactions = $usersByUserTransactionsQuery->all();
        $usersByCloudStats = $this->getCloudQuery($where, $params['cloud'])->all();

        if ($params['update'] === User::ACTIVE_STATUS_LOW) {
            return $usersByUserTransactions + $usersByCloudStats;
        } else {
            return array_intersect_key($usersByUserTransactions, $usersByCloudStats);
        }
    }

    private function getCloudQuery(array $where, array $params): QueryInterface
    {
        return $this->getQuery($where, $params['params'] ?? [])
            ->leftJoin(['u_cloud' => Users::TABLE_NAME], 'u_cloud.cid = u.cid')
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u_cloud.site_id AND usi.user_id = u_cloud.user_id')
            ->having($params['having']);
    }

    private function getQuery(array $where, array $queryParams = []): QueryInterface
    {
        return (new Query($this->db))
            ->select([
                'u.site_id',
                'u.user_id',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->where($where)
            ->groupBy([
                'u.site_id',
                'u.user_id',
            ])
            ->addParams($queryParams)
            ->indexBy('user_id');
    }
}
