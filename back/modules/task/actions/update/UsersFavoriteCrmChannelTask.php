<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\entities\Refcode;
use app\back\entities\UserMetric;
use app\back\entities\UserTransaction;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Refcodes;
use app\back\repositories\UserMetrics;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersFavoriteCrmChannelTask extends BaseTask
{
    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly UserMetrics $userMetricsRepo,
    ) {
    }

    public function process(): void
    {
        $newUsers = (new Query($this->db))
            ->select(['site_id', 'user_id'])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->where([
                'AND',
                ['>=', 'us.updated_at', $this->from],
                ['<', 'us.updated_at', $this->to],
                [
                    'us.site_id' => $this->siteIdResolver->siteId(),
                    'us.op_id' => UserTransaction::OP_IN,
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                ],
            ])
            ->groupBy(['us.site_id', 'us.user_id']);

        $monthAgo = date('Y-m-d H:i:s', strtotime('-30 days', strtotime($this->to)));
        [$metricId, $metricCol] = UserMetric::M_CRM_FAVORITE_CHANNEL;

        foreach ($newUsers->each(1000) as $u) {
            $cc = (new Query($this->db))
                ->select([
                    'crm_channel' => Refcodes::crmChannelsCase(),
                ])
                ->from(['us' => UserTransactions::TABLE_NAME])
                ->leftJoin(['r' => Refcodes::TABLE_NAME], 'us.refcode_id = r.id')
                ->where([
                    'AND',
                    ['>=', 'us.updated_at', $monthAgo],
                    ['<', 'us.updated_at', $this->to],
                    [
                        'us.op_id' => UserTransaction::OP_IN,
                        'us.status' => UserTransaction::STATUS_SUCCESS,
                        Refcodes::crmChannelsCase() => array_keys(Refcode::CRM_CHANNELS),
                    ],
                ])
                ->andWhere($u)
                ->groupBy('crm_channel')
                ->orderBy(['SUM(us.amount_usd)' => SORT_DESC])
                ->limit(1)
                ->one();

            if ($cc === null) {
                continue;
            }

            $this->affectedRows += $this->userMetricsRepo->upsert(['site_id', 'user_id', 'metric'], [
                'site_id' => $u['site_id'],
                'user_id' => $u['user_id'],
                'metric' => $metricId,
                $metricCol => $cc['crm_channel']
            ], ['updated_at' => new Expression('NOW()')]);
        }
        $this->totalRows = $this->affectedRows;
    }
}
