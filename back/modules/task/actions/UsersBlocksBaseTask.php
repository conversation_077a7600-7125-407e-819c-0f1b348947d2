<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\Arr;
use app\back\entities\UserBlock;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\UserBlockComments;
use app\back\repositories\UserBlocks;
use app\back\repositories\Users;

abstract class UsersBlocksBaseTask extends ImportTask
{
    use TaskWithFromToRequest;

    private array $rowsForUser = [];

    public function __construct(
        protected readonly UserBlocks $userBlocksRepo,
        protected readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Users $usersRepo,
        private readonly UserBlockComments $userBlockCommentsRepo,
    ) {
    }

    public function process(): void
    {
        parent::process();

        $usersUpdatedCount = $this->usersRepo->batchUpdateDistinct($this->rowsForUser);
        $this->log->debug("Updated $usersUpdatedCount users");
    }

    protected function beforeFind(array &$row): bool
    {
        if ($row['type'] === UserBlock::BLOCK_TYPE_LOGIN) {
            $this->rowsForUser[] = [
                'site_id' => $row['site_id'],
                'user_id' => (int)$row['user_id'],
                'is_blocked' => (bool)$row['active'],
            ];
        }

        if (isset($row['comment'])) {
            $row['comment_id'] = $this->userBlockCommentsRepo->getIdByName($row['comment']);
            unset($row['comment']);
        }

        return true;
    }

    protected function repository(): BaseRepository
    {
        return $this->userBlocksRepo;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['site_id', 'user_id', 'type'], 'updated_at');
        return parent::batchUpsert($repository, $rows);
    }
}
