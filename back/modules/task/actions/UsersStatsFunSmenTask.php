<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\DateHelper;
use app\back\entities\UserTransaction;
use app\back\modules\task\CurrencyConversionTrait;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\Rates;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Expression\Expression;

class UsersStatsFunSmenTask extends ImportTask
{
    use TaskWithFromToRequest;
    use CurrencyConversionTrait;

    private const string FUN_ID_PREFIX = 'f';

    public string $dateFormat = DateHelper::DATETIME_FORMAT_PHP;

    public function __construct(
        private readonly UserTransactions $userTransactionsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Rates $ratesRepo,
    ) {
    }

    private const array SOURCE_NAMES_TO_OP_IDS = [
        'fun_tournament' => UserTransaction::OP_TOURNAMENT_FUN_BONUS,
        'fun_reward' => UserTransaction::OP_FUN_REWARD,
        'front_button' => UserTransaction::OP_FUN_REWARD,
    ];

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();
        $row['transaction_id'] = static::FUN_ID_PREFIX . $row['transaction_id'];
        $row['balance_type'] = UserTransaction::BALANCE_TYPE_BONUS;

        if (!empty($row['source_name'])) {
            if (empty(self::SOURCE_NAMES_TO_OP_IDS[$row['source_name']])) {
                $this->log->error('Unknown source: ' . $row['source_name']);
                return false;
            }
            $row['op_id'] = self::SOURCE_NAMES_TO_OP_IDS[$row['source_name']];
        }

        $this->convertCurrencyAmounts($row);

        return true;
    }

    protected function repository(): BaseRepository
    {
        return $this->userTransactionsRepo;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        return $repository->batchUpsert($rows, [
            'upserted_at' => new Expression('now()'),
        ]);
    }
}
