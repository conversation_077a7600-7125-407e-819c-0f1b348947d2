<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\Url;
use app\back\components\Initializable;
use app\back\components\kyc\KycStatusModel;
use app\back\components\Notifier;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use app\back\modules\reports\components\Locations;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Sites;
use app\back\repositories\UserDocumentProgresses;

trait UsersDocumentsWithKycTrait
{
    private array $newWaitVerificationUsers = [];
    private Notifier $notifier;
    private UserDocumentProgresses $docProgressRepo;
    protected TaskSiteIdResolver $siteIdResolver;
    private Sites $sitesRepo;
    protected readonly KycStatusModel $kycStatusModel;

    #[Initializable]
    final public function initForKyc(
        Notifier $notifier,
        KycStatusModel $kycStatusModel,
        TaskSiteIdResolver $siteIdResolver,
        Sites $sitesRepo,
    ): void {
        $this->notifier = $notifier;
        $this->kycStatusModel = $kycStatusModel;
        $this->kycStatusModel->source = UserDocumentProgress::SOURCE_PRODUCT;
        $this->siteIdResolver = $siteIdResolver;
        $this->sitesRepo = $sitesRepo;
    }

    protected function updateKycWaitStatus(UserDocument $doc): bool
    {
        $updatedToWaitStatus = $this->kycStatusModel->update(
            $doc->site_id,
            $doc->user_id,
            UserKyc::KYC_WAIT,
            docIds: [$doc->id],
        );

        if ($updatedToWaitStatus) {
            $this->newWaitVerificationUsers[$doc->user_id] = $doc->user_id;
        }

        return true;
    }

    public function process(): void
    {
        try {
            parent::process();
        } finally {
            $usersCount = count($this->newWaitVerificationUsers);

            if (!$this->skipEvents && $usersCount !== 0) {
                $links = implode("<br>\n", array_map(function (int $userId) {
                    $siteUser = $this->siteUserBuilder->siteUserToValue($this->siteIdResolver->siteId(), $userId);
                    $url = Url::to('/finance/documents', ['siteId' => $this->siteIdResolver->siteId(), 'userId' => $userId], true);
                    return "<a href=\"$url\">$siteUser</a>";
                }, $this->newWaitVerificationUsers));

                try {
                    if (in_array($this->siteIdResolver->siteId(), Locations::getSitesByLocation($this->sitesRepo, Locations::CIS), true)) {
                        $contactName = 'documents_verification_wait_cis';
                        $postfix = 'cis';
                    } else {
                        $contactName = 'documents_verification_wait_int';
                        $postfix = 'int';
                    }

                    $this->notifier->notify($contactName, "Documents verification for $usersCount users $postfix", $links);
                } catch (\Throwable $e) {
                    $this->log->error("Unable to sent verification links:\n$links");
                    throw $e;
                }
            }
        }
    }
}
