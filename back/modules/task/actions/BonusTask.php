<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\entities\Bonus;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Bonuses;
use app\back\repositories\Refcodes;

class BonusTask extends ImportTask
{
    use TaskWithDefaultGetData;

    public const array TYPES = [
        'single' => Bonus::TYPE_SINGLE,
        'progressive' => Bonus::TYPE_PROGRESSIVE,
        'manual_activation' => Bonus::TYPE_MANUAL_ACTIVATION,
        'loyalty' => Bonus::TYPE_LOYALTY,
        'white_list' => Bonus::TYPE_WHITE_LIST,
        'for_points' => Bonus::TYPE_FOR_POINTS,
        'active_sales' => Bonus::TYPE_ACTIVE_SALES,
        'motivation' => Bonus::TYPE_MOTIVATION,
        'personal' => Bonus::TYPE_PERSONAL,
        'shake' => Bonus::TYPE_SHAKE,
        'achievement' => Bonus::TYPE_ACHIEVEMENT,
        'limited_activation' => Bonus::TYPE_LIMITED_ACTIVATION,
        'promo_code' => Bonus::TYPE_PROMO_CODE,
        'crm_bonus' => Bonus::TYPE_CRM_BONUS,
        'registration_ref_code' => Bonus::TYPE_REGISTRATION_REF_CODE,
        'login_ref_code' => Bonus::TYPE_LOGIN_REF_CODE,
    ];

    private const array EVENTS = [
        'first_deposit' => Bonus::EVENT_FIRST_DEPOSIT,
        'min_deposit' => Bonus::EVENT_MINIMAL_DEPOSIT,
        'email_confirmation' => Bonus::EVENT_EMAIL_CONFIRMATION,
        'registration' => Bonus::EVENT_REGISTRATION,
        'manually_assign' => Bonus::EVENT_MANUALLY_ASSIGN,
        'cas2pay' => Bonus::EVENT_CAS2PAY_INSTALL,
        'marketing_bonus' => Bonus::EVENT_MARKETING_BONUS,
        'collection_quest' => Bonus::EVENT_COLLECTING_QUEST,
        'assign_status' => Bonus::EVENT_ASSIGN_STATUS,
        'low_balance' => Bonus::EVENT_LOW_BALANCE,
        'phone_confirmation' => Bonus::EVENT_PHONE_CONFIRM,
        'end_activation_time' => Bonus::EVENT_END_ACTIVATION_TIME,
        'dc2_install' => Bonus::EVENT_DC2_INSTALL,
        'auth_site' => Bonus::EVENT_LOGIN,
        'buy_for_points' => Bonus::EVENT_BUY_FOR_POINTS,
        'assign_achievement' => Bonus::EVENT_ASSIGN_ACHIEVEMENT,
        'follow_link' => Bonus::EVENT_FOLLOW_LINK,
        'birthday_bonus' => Bonus::EVENT_BIRTHDAY_BONUS,
        'assign_achievement_ser' => Bonus::EVENT_ACHIEVEMENT_V2,
        'deposit_extra_bonus' => Bonus::EVENT_DEPOSIT_EXTRA_BONUS,
        'promo_code' => Bonus::EVENT_PROMO_CODE,
        'assign_achievement_reward_ser' => Bonus::EVENT_ACHIEVEMENT_REWARD,
        'activation' => Bonus::EVENT_ON_ACTIVATE,
    ];

    public function __construct(
        private readonly Refcodes $refcodesRepo,
        private readonly Bonuses $bonusesRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    protected function repository(): Bonuses
    {
        return $this->bonusesRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();
        if (!empty($row['ref_code'])) {
            $row['refcode_id'] = $this->refcodesRepo->getIdByCode(trim($row['ref_code'], '*'));
        }
        unset($row['ref_code']);

        if (!empty($row['login_ref_code'])) {
            $row['login_refcode_id'] = $this->refcodesRepo->getIdByCode(trim($row['login_ref_code'], '*'));
        }
        unset($row['login_ref_code']);

        if (isset($row['type']) && !array_key_exists($row['type'], static::TYPES)) {
            $this->log->error("Unknown type: {$row['type']}");
            return false;
        }
        $row['type_id'] = static::TYPES[$row['type']] ?? null;
        unset($row['type']);

        if (isset($row['event']) && !array_key_exists($row['event'], static::EVENTS)) {
            $this->log->error("Unknown event: {$row['event']}");
            return false;
        }
        $row['event_id'] = static::EVENTS[$row['event']] ?? null;
        unset($row['event']);

        return parent::beforeFind($row);
    }
}
