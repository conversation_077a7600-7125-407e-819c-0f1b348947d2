<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\DateHelper;
use app\back\entities\Employee;
use app\back\entities\EmployeeCrypt;
use app\back\modules\task\ImportTask;
use app\back\modules\task\requests\SynefPgpKeysRequest;
use app\back\repositories\BaseRepository;
use app\back\repositories\EmployeeCrypts;
use app\back\repositories\Employees;
use Yiisoft\Db\Query\Query;

/** @property SynefPgpKeysRequest $request */
class EmployeesMailsPublicKeysTask extends ImportTask
{
    public function __construct(
        private readonly EmployeeCrypts $employeeCryptsRepo,
        private readonly Employees $employeesRepo,
    ) {
    }

    protected function batchSize(): int
    {
        return 10;
    }

    protected function getData(): iterable
    {
        /** @var Employee[] $employees */
        $employees = (new Query($this->employeesRepo->db))
            ->select([
                'e.employee_id',
                'e.email',
            ])
            ->from(['e' => Employees::TABLE_NAME])
            ->leftJoin(['ec' => EmployeeCrypts::TABLE_NAME], 'ec.employee_id = e.employee_id')
            ->where(['e.status' => Employee::STATUS_ACTIVE])
            ->andWhere([
                'OR',
                ['ec.updated_at' => null],
                ['<', 'ec.expired_at', date(DateHelper::DATE_FORMAT_PHP, strtotime('+1 month', $this->fromTime))],
            ])
            ->orderBy(['e.employee_id' => SORT_ASC])
            ->all();

        foreach ($employees as $e) {
            $keyInfo = $this->createRequest(['email' => $e['email']])->finalData()[0] ?? null;

            if ($keyInfo === null) {
                $this->log->notice("No key found for {$e['email']}. Skipping");
                yield [
                    'employee_id' => $e['employee_id'],
                    'pgp_public_key' => null,
                    'created_at' => EmployeeCrypt::SQL_NOW_DATETIME,
                ];
                continue;
            }

            yield [
                'employee_id' => $e['employee_id'],
                'pgp_public_key' => $keyInfo['key'],
                'created_at' => $keyInfo['created_at'],
                'expired_at' => $keyInfo['expired_at'],
            ];
        }
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        return $repository->batchUpsert($rows, ['updated_at' => 'NOW()']);
    }

    protected function repository(): BaseRepository
    {
        return $this->employeeCryptsRepo;
    }
}
