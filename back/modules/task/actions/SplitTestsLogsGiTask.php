<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\SplitTestLogs;

class SplitTestsLogsGiTask extends ImportTask
{
    use TaskWithFromToRequest;

    public function __construct(
        private readonly SplitTestLogs $splitTestLogsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    protected function repository(): BaseRepository
    {
        return $this->splitTestLogsRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();
        $row['log_id'] = md5($row['user_id'] . '|' . $row['split_slug']);
        $row['identity_key'] = $row['user_id'];

        return true;
    }
}
