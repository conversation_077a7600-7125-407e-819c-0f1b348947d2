<?php

declare(strict_types=1);

namespace app\back\modules\task\components;

use app\back\components\helpers\Arr;
use app\back\components\Initializable;
use app\back\repositories\UserLyraActivities;
use Yiisoft\Db\Expression\Expression;

trait UsersLyraActivityTrait
{
    private readonly UserLyraActivities $userLyraActivitiesRepo;

    #[Initializable]
    final public function initUserLyraActivitiesRepo(UserLyraActivities $userLyraActivitiesRepo): void
    {
        $this->userLyraActivitiesRepo = $userLyraActivitiesRepo;
    }

    protected function updateLyraUsersActivity(int $source, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['site_id', 'user_id']);

        $table = UserLyraActivities::TABLE_NAME;
        $updateCondition = "CASE WHEN {$table}.source_updated_at > excluded.source_updated_at THEN";
        return $this->userLyraActivitiesRepo->batchInsertOnDuplicateUpdate(
            array_map(static fn($activityRow) => [
                'site_id' => $activityRow['site_id'],
                'user_id' => $activityRow['user_id'],
                'source_updated_at' => $activityRow['date'],
                'source_id' => $source,
                'update_count' => 1
            ], $rows),
            [
                'update_count' => new Expression("{$updateCondition} {$table}.update_count ELSE {$table}.update_count + 1 END"),
                'updated_at' => new Expression("{$updateCondition} {$table}.updated_at ELSE NOW() END"),
                'source_updated_at' => new Expression("{$updateCondition} {$table}.source_updated_at ELSE excluded.source_updated_at END"),
                'source_id' => new Expression("{$updateCondition} {$table}.source_id ELSE excluded.source_id END")
            ]
        );
    }
}
