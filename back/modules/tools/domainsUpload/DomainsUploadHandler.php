<?php

declare(strict_types=1);

namespace app\back\modules\tools\domainsUpload;

use app\back\components\helpers\Arr;
use app\back\components\SessionMessages;
use app\back\repositories\HostInfos;
use Yiisoft\Db\Expression\Expression;

class DomainsUploadHandler
{
    public function __construct(
        private readonly HostInfos $hostInfosRepo,
        private readonly SessionMessages $sessionMessages,
    ) {
    }

    public function __invoke(array $rows): void
    {
        $rows = array_map(static fn ($value) => array_merge($value, ['log' => null]), $rows);
        $logValue = $this->hostInfosRepo->jsonLogAppendedValue('log', [...Arr::uniqueKeys($rows), 'updated_at']);
        $affected = $this->hostInfosRepo->batchUpsert($rows, [
            'updated_at' => new Expression('NOW()'),
            'log' => $logValue,
        ]);
        $processed = count($rows);
        $this->sessionMessages->success("$affected of $processed rows affected by changes");
    }
}
