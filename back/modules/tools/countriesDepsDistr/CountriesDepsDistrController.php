<?php

declare(strict_types=1);

namespace app\back\modules\tools\countriesDepsDistr;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\ResponseCsv;
use app\back\components\WebController;

#[AccessCheckPage]
class CountriesDepsDistrController extends WebController
{
    public function actionData(CountriesDepsDistrDataForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionUpdate(CountriesDepsDistrUpdateForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->update();
        $this->bl()->modify($request->json());
    }

    public function actionReset(CountriesDepsDistrResetForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->reset();
        $this->bl()->modify($request->json());
    }

    public function actionCsv(CountriesDepsDistrDataForm $form): ResponseCsv
    {
        return $form->csv();
    }
}
