<?php

declare(strict_types=1);

namespace app\back\modules\tools\cidVisualisation;

use app\back\components\AllowedLists;
use app\back\components\Form;
use app\back\components\validators\SiteUserIdMultilineValidator;
use app\back\repositories\Sites;

class CidVisUsersForm
{
    use Form;

    #[SiteUserIdMultilineValidator]
    public string $users;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly CidVisResponse $cidVisResponse,
        private readonly Sites $sites,
    ) {
    }

    public function data(): array
    {
        $users = SiteUserIdMultilineValidator::explodeToSiteUserArrays($this->users, $this->allowedLists->sites(true));
        return $this->cidVisResponse->usersNodes($users);
    }
}
