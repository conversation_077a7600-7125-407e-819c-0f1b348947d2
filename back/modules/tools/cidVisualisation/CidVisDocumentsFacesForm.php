<?php

declare(strict_types=1);

namespace app\back\modules\tools\cidVisualisation;

use app\back\components\AllowedLists;
use app\back\components\Form;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\validators\IdValidator;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Query\Query;

class CidVisDocumentsFacesForm
{
    use Form;

    #[IdValidator]
    public int $faceId;

    public function __construct(
        private readonly SecondaryConnection $db,
        private readonly FileStorage $storage,
        private readonly AllowedLists $allowedLists,
    ) {
    }

    public function faces(): array
    {
        $query = (new Query($this->db))
            ->select(['ud.site_id', 'ud.user_id', 'ud.filename'])
            ->from(['udf' => UserDocumentFaces::TABLE_NAME])
            ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'udf.user_document_id = ud.id')
            ->where(['udf.similar_id' => $this->faceId])
            ->groupBy(['ud.site_id', 'ud.user_id', 'ud.filename'])
            ->orderBy('ud.site_id, ud.user_id');

        $allowedSites = $this->allowedLists->sites();

        return array_map(function ($doc) use ($allowedSites) {
            if (array_key_exists($doc['site_id'], $allowedSites)) {
                return $this->storage->getPublicUrlByKey("{$doc['site_id']}/{$doc['user_id']}/{$doc['filename']}");
            }
            return '/images/cid/avatar.png';
        }, $query->all());
    }
}
