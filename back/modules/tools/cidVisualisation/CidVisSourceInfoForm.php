<?php

declare(strict_types=1);

namespace app\back\modules\tools\cidVisualisation;

use app\back\components\Form;
use app\back\components\validators\StringValidator;

class CidVisSourceInfoForm
{
    use Form;

    #[StringValidator]
    public string $key;

    public function __construct(
        private readonly CidVisResponse $cidVisResponse,
    ) {
    }

    public function data(): array
    {
        return $this->cidVisResponse->sourceInfoNodes($this->key);
    }
}
