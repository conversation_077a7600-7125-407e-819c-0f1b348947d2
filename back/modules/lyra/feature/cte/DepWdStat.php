<?php

namespace app\back\modules\lyra\feature\cte;

use app\back\entities\UserTransaction;
use app\back\modules\lyra\feature\FeatureFactory;
use app\back\repositories\CountryDepDistrs;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class DepWdStat extends BaseFeatureCTE
{
    public const string DEP_COUNT_WD_COUNT_RATIO_RETURN_KEY_VALUE = 'dcwc';
    public const string DEP_SUM_WD_SUM_RATIO_RETURN_KEY_VALUE = 'dsws';
    public const string WD_SUM_INDEX_RETURN_KEY_VALUE = 'wi';

    public function toQuery(string $mainTableAlias): Query
    {
        $statTable = (new Query($this->db))
            ->select([
                'user_id' => 'f.user_id',
                'site_id' => 'f.site_id',
                'wd_sum' => new Expression('SUM(us.amount_usd) FILTER (WHERE op_id = :op_out)', ['op_out' => UserTransaction::OP_OUT]),
                'wd_count' => new Expression('count(us.*) FILTER (WHERE op_id = :op_out)::numeric', ['op_out' => UserTransaction::OP_OUT]),
                'dep_sum' => new Expression('SUM(us.amount_usd) FILTER (WHERE op_id = :op_in)', ['op_in' => UserTransaction::OP_IN]),
                'dep_count' => new Expression('count(us.*) FILTER (WHERE op_id = :op_in)::numeric', ['op_in' => UserTransaction::OP_IN]),
            ])
            ->from(['f' => $mainTableAlias])
            ->innerJoin(['us' => UserTransactions::TABLE_NAME], 'f.user_id = us.user_id AND f.site_id = us.site_id')
            ->where([
                'AND',
                [
                    'us.op_id' => [UserTransaction::OP_OUT, UserTransaction::OP_IN],
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                ],
                ['>=', 'us.updated_at', $this->statDateFrom],
                ['<', 'us.updated_at', $this->statDateTo],
            ])
            ->groupBy(['f.site_id', 'f.user_id']);

        $paymentActivityPercentileQuery = (new Query($this->db))
            ->select([
                'cdd.percentile',
            ])
            ->from(['cdd' => CountryDepDistrs::TABLE_NAME])
            ->where('cdd.country = u.country')
            ->andWhere('stat.wd_sum >= cdd.amount_usd')
            ->orderBy('cdd.percentile DESC')
            ->limit(1);

        return (new Query($this->db))
            ->select([
                FeatureFactory::RETURN_KEY_SITE_ID => 'stat.site_id',
                FeatureFactory::RETURN_KEY_USER_ID => 'stat.user_id',
                self::DEP_COUNT_WD_COUNT_RATIO_RETURN_KEY_VALUE => new Expression(
                    "stat.dep_count / NULLIF(stat.wd_count, 0)",
                    ['op_in' => UserTransaction::OP_IN, 'op_out' => UserTransaction::OP_OUT]
                ),
                self::DEP_SUM_WD_SUM_RATIO_RETURN_KEY_VALUE => new Expression(
                    "(stat.wd_sum / NULLIF(stat.dep_sum, 0)) * 100",
                    ['op_in' => UserTransaction::OP_IN, 'op_out' => UserTransaction::OP_OUT]
                ),
                self::WD_SUM_INDEX_RETURN_KEY_VALUE => 'perc.percentile',
            ])
            ->from(['stat' => $statTable])
            ->leftJoin(['u' => Users::TABLE_NAME], 'stat.user_id = u.user_id AND stat.site_id = u.site_id')
            ->join('CROSS JOIN LATERAL', ['perc' => $paymentActivityPercentileQuery]);
    }
}
