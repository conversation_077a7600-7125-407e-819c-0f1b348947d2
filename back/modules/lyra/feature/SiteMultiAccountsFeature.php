<?php

namespace app\back\modules\lyra\feature;

use app\back\modules\lyra\feature\cte\MultiAccounts;
use app\back\modules\lyra\feature\trait\MultiAccountsFeatureMappingTrait;
use Yiisoft\Db\Expression\Expression;

class SiteMultiAccountsFeature extends BaseFeature
{
    use MultiAccountsFeatureMappingTrait;

    protected function getFieldName(): string
    {
        return MultiAccounts::RETURN_KEY_SITE_MULTI_ACCOUNTS;
    }

    public function mapValue(mixed $value): string
    {
        return $this->mapIntToVal($value);
    }
}
