<?php

declare(strict_types=1);

namespace app\back\modules\lyra\featureSets;

use app\back\components\Form;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\LyraFeatureSet;
use app\back\repositories\LyraFeatureSets;

class FeatureSetsModifyForm
{
    use Form;

    #[IdValidator]
    public int $id;
    #[BooleanValidator]
    public ?bool $is_active = null;
    #[StringValidator(1, 200)]
    public ?string $name = null;
    #[BooleanValidator]
    public ?bool $show_predict = null;
    #[BooleanValidator]
    public ?bool $verified = null;
    #[IdValidator]
    public ?int $ttl_days = null;

    #[StringInArrayValidator(['is_active', 'name', 'show_predict', 'verified', 'ttl_days'], true)]
    public string $column;

    public function __construct(
        private readonly LyraFeatureSets $lyraFeatureSetsRepo,
    ) {
    }

    public function update(): void
    {
        /** @var LyraFeatureSet $entity */
        $entity = $this->lyraFeatureSetsRepo->findOneOr404(['id' => $this->id]);
        $column = $this->column;
        $entity->$column = $this->$column;
        $this->lyraFeatureSetsRepo->update($entity, [$column]);
    }
}
