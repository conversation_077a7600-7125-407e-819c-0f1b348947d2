<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\proxies;

use app\back\components\helpers\Arr;
use app\back\components\RichTable;
use app\back\components\validators\BooleanArrayValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\Proxy;
use app\back\repositories\Proxies;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class ProxiesListForm
{
    use RichTable;

    #[StringValidator(0, 500)]
    public ?string $config = null;
    #[IntArrayValidator(Proxy::GROUPS)]
    public array $group = [];
    #[StringValidator(0, 500)]
    public ?string $comment = null;
    #[BooleanArrayValidator]
    public array $isActive = [];

    public function __construct(private readonly ConnectionInterface $db)
    {
        $this->pageSize = 500;
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'ID', 'code' => 'id'],
            ['name' => 'Config', 'slotName' => 'config'],
            ['name' => 'Active', 'slotName' => 'active'],
            ['name' => 'Group', 'code' => 'groupName'],
            ['name' => 'Comment', 'slotName' => 'comment'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(3, 'config', 'Config', ['focusOnMount' => true]),
                $this->selectBooleanCell(2, 'isActive', 'Active'),
                $this->listCell(4, 'group', 'Group', [
                    'list' => Arr::assocToIdName(Proxy::GROUPS),
                ]),
                $this->textInputCell(3, 'comment', 'Comment'),
            ],
        ];
    }

    public function data(): array
    {
        $query = (new Query($this->db))
            ->select([
                'id' => 'p.id',
                'config' => 'p.config',
                'isActive' => 'p.is_active',
                'group' => 'p.group',
                'comment' => "p.comment"
            ])
            ->from(['p' => Proxies::TABLE_NAME])
            ->orderBy(['p.id' => SORT_ASC])
            ->offset($this->getOffset())
            ->limit($this->getLimit());

        $this->applyFilters($query);

        $rows = $query->all();

        foreach ($rows as &$row) {
            $row['groupName'] = Proxy::getGroupNameById($row['group']);
            $row['comment'] ??= '';
        }

        return $rows;
    }

    protected function total(): int
    {
        $total = (new Query($this->db))
            ->select('COUNT(*)')
            ->from(['p' => Proxies::TABLE_NAME]);

        $this->applyFilters($total);

        return (int) $total->scalar();
    }

    private function applyFilters(Query $query): void
    {
        if ($this->config) {
            $query->andFilterWhere(['p.config' => trim($this->config)]);
        }

        if ($this->comment) {
            $query->andFilterWhere(['ILIKE', 'p.comment', trim($this->comment)]);
        }

        if ($this->isActive) {
            $query->andFilterWhere(['p.is_active' => $this->isActive]);
        }

        if ($this->group) {
            $query->andFilterWhere(['p.group' => $this->group]);
        }
    }
}
