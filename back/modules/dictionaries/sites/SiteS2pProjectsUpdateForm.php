<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\sites;

use app\back\components\Form;
use app\back\components\helpers\Str;
use app\back\components\SessionMessages;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\PropNameInErrorMessage;
use app\back\components\validators\StringMultilineValidator;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pProjects;
use app\back\repositories\S2pTransactions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class SiteS2pProjectsUpdateForm
{
    use Form;

    #[IdValidator]
    public int $siteId;
    #[PropNameInErrorMessage('Project')]
    #[StringMultilineValidator]
    #[CallableValidator([self::class, 'validateValues'])]
    public ?string $projectNames = null;

    private array $validProjectNames = [];

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly SessionMessages $sessionMessages,
    ) {
    }

    public function update(): void
    {
        $transactions = $this->db->beginTransaction();
        $affectedProjects = $this->db->createCommand()->update(S2pProjects::TABLE_NAME, ['site_id' => null], ['site_id' => $this->siteId])->execute();
        $affectedOrders = $affectedTransactions = 0;
        if (count($this->validProjectNames) > 0) {
            $affectedProjects = $this->db->createCommand()->update(S2pProjects::TABLE_NAME, ['site_id' => $this->siteId], ['name' => $this->validProjectNames])->execute();
            [$affectedOrders, $affectedTransactions] = $this->updateS2pOrdersAndTransactions();
        }
        $transactions->commit();

        $this->sessionMessages->success("Updated $affectedProjects projects, $affectedOrders orders and $affectedTransactions transactions");
    }

    private function updateS2pOrdersAndTransactions(): array
    {
        $projectIds = (new Query($this->db))->select(['id'])->from([S2pProjects::TABLE_NAME])->where(['name' => $this->validProjectNames])->column();
        $updateCondition = ['AND', ['project_id' => $projectIds], ['IS DISTINCT FROM', 'site_id', $this->siteId]];
        $affectedOrders = $this->db->createCommand()->update(S2pOrders::TABLE_NAME, ['site_id' => $this->siteId], $updateCondition)->execute();
        $affectedTransactions = $this->db->createCommand()->update(S2pTransactions::TABLE_NAME, ['site_id' => $this->siteId], $updateCondition)->execute();
        return [$affectedOrders, $affectedTransactions];
    }

    public static function validateValues(mixed $values, self $form): ?string
    {
        $values = Str::explodeText($values, "\n", true);

        if (!isset($form->siteId) || count($values) === 0) {
            return null;
        }

        $existingProjects = (new Query($form->db))
            ->select(['s2p.site_id'])
            ->from(['s2p' => S2pProjects::TABLE_NAME])
            ->where(['name' => $values])
            ->indexBy('s2p.name')
            ->column();

        foreach ($values as $value) {
            if (!array_key_exists($value, $existingProjects)) {
                return "'$value' doesn't exist";
            }
            if ($existingProjects[$value] !== null && $existingProjects[$value] !== $form->siteId) {
                return "'$value' already assigned to site $existingProjects[$value]";
            }
        }
        $form->validProjectNames = $values;
        return null;
    }
}
