<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\canonicalPaySys;

use app\back\components\exceptions\InvalidException;
use app\back\components\FormGrid;
use app\back\components\helpers\Str;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\entities\CanonicalPaySySource;
use app\back\repositories\CanonicalPaySySources;
use app\back\repositories\PaySystems;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class CanonicalPaySysEditSourcesForm
{
    use FormGrid;

    #[IdValidator]
    public ?int $id = null;
    #[IntInArrayValidator(CanonicalPaySySource::SOURCES)]
    public ?int $source = null;
    #[StringMultilineValidator(0, 250)]
    #[CallableValidator([self::class, 'nameExistValidate'])]
    public ?string $names = null;

    public function __construct(
        private readonly CanonicalPaySySources $sources,
        private readonly ConnectionInterface $db,
        private readonly PaySystems $paySystemsRepo,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textAreaCell(12, 'name', 'Name'),
                $this->submitCell(12, 'Save', [
                    'buttonIcon' => 'icn-save',
                ]),
            ],
        ];
    }

    public function edit(): void
    {
        $oldNames = (new Query($this->db))
            ->select('name')
            ->where([
                'canonical_pay_sys_id' => $this->id,
                'source' => $this->source
            ])
            ->from(['cps' => CanonicalPaySySources::TABLE_NAME])
            ->column();

        $newNames = Str::explodeText($this->names);
        $toInsert = array_diff($newNames, $oldNames);
        $toDelete = array_diff($oldNames, $newNames);

        if (!empty($toInsert) || !empty($toDelete)) {
            $tr = $this->db->beginTransaction();

            foreach ($toInsert as $name) {
                $pss = new CanonicalPaySySource();
                $pss->source = $this->source;
                $pss->canonical_pay_sys_id = $this->id;
                $pss->name = $name;

                $this->sources->insert($pss);
            }

            foreach ($toDelete as $name) {
                $pss = $this->sources->findOneOr404(['source' => $this->source, 'name' => $name,]);
                $this->sources->delete($pss);
            }

            $tr->commit();
        }
    }

    public static function nameExistValidate(?string $value, self $form): ?string
    {
        foreach (Str::explodeText($value) as $name) {
            if (!$form->paySystemsRepo->exists($name)) {
                throw new InvalidException("Source '$name' does not exist");
            }
        }

        return null;
    }
}
