<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\canonicalPaySys;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class CanonicalPaySysController extends WebController
{
    public function actionData(CanonicalPaySysFilterForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionAddForm(CanonicalPaySysAddForm $form): array
    {
        return $form->response();
    }

    public function actionAdd(CanonicalPaySysAddForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->save();
        $this->bl()->create($request->json());
    }

    public function actionDelete(CanonicalPaySysDeleteForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->delete();
        $this->bl()->delete($request->json());
    }

    public function actionUpdate(CanonicalPaySysUpdateForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->update();
        $this->bl()->modify($request->json());
    }

    public function actionEditSources(CanonicalPaySysEditSourcesForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->edit();
        $this->bl()->modify($request->json());
    }

    public function actionUnmappedPaySys(CanonicalPaySysUnmappedForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }
}
