<?php

declare(strict_types=1);

namespace app\back\modules\api;

use app\back\components\exceptions\InvalidException;
use app\back\components\validators\BaseValidator;

abstract class ApiModifyMethod extends ApiMethod
{
    public const string AFFECTED_ROWS_RESULT = 'affected_rows';

    protected function validate(): array
    {
        $r = new \ReflectionClass($this);

        $result = [];
        foreach ($this->sortedPropsWithValidators($r) as [$prop, $validatorAttributes]) {
            /** @var \ReflectionProperty $prop */

            $propName = $prop->getName();

            if ((!array_key_exists($propName, $this->params) && !$prop->isInitialized($this))) {
                throw new InvalidException("$propName is required");
            }

            $paramExist = array_key_exists($propName, $this->params);

            $value = $paramExist ? $this->params[$propName] : $prop->getValue($this);

            foreach ($validatorAttributes as $validatorAttribute) {
                /** @var BaseValidator $validator */
                $validator = $validatorAttribute->newInstance();

                $value = $validator->prepare($value, $this, $this->params);

                if ($value === null && $paramExist) {
                    $result[$propName] = null;
                }

                if ($value === null && !$validator::VALIDATE_ON_NULL) {
                    continue;
                }

                $invalidMessage = $validator->validate($value, $this, $this->params);
                if ($invalidMessage !== null) {
                    throw new InvalidException("$propName $invalidMessage");
                }

                $value = $validator->cast($value, $this);
                if ($value === null) {
                    continue;
                }
                $result[$propName] = $value;
            }
        }

        return $result;
    }

    protected function affectedResult($count): array
    {
        return [
            [static::AFFECTED_ROWS_RESULT => $count]
        ];
    }

    protected function errorResult(string $message): array
    {
        $resp = $this->affectedResult(0);

        $resp[0]['message'] = 'Error: ' . $message;

        return $resp;
    }
}
