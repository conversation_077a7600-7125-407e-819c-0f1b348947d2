<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\marketing;

use app\back\components\validators\DateValidator;
use app\back\entities\UserTransaction;
use app\back\modules\api\components\Operators;
use app\back\modules\api\params\ApiParamDateHour;
use app\back\modules\api\params\ApiParamSiteIdSecured;
use app\back\repositories\Refcodes;
use app\back\repositories\UseragentApps;
use app\back\repositories\UseragentPlatforms;
use app\back\repositories\Useragents;
use app\back\repositories\UserLogins;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

/** Class copied from LoginRefcodeStatsAction */
class RevenueLoginMethod extends BaseMarketingGetMethod
{
    use ApiParamSiteIdSecured;
    use ApiParamDateHour;

    #[DateValidator]
    #[Operators(Operators::EQ)]
    protected array $reg_to = [];

    public function run(): iterable
    {
        $request = $this->createRequest();

        [$dateFrom, $dateTo, $siteId] = $this->getDatesAndSite($request);

        $query = (new Query($this->db))
            ->select([
                'date' => 'DATE(us.updated_at)',
                'hour' => 'EXTRACT(HOUR FROM us.updated_at)',
                'refcode' => 'rl.code',
                'country' => 'u.country',
                'locale' => 'u.locale',
                'platform' => static::REPLACED_PLATFORM,
                'refcode_registration_prefix' => "SPLIT_PART(rr.code, '_', 1)",
                'platform_os' => 'uagp.name',
                'platform_variant' => 'uag.variant_id',
                'date_fd' => 'DATE(usi.dep_first_at)',
                'deposits_count' => 'COUNT(*) FILTER (WHERE us.dir = :in)',
                'deposits_sum' => 'COALESCE(SUM(us.amount_usd) FILTER (WHERE us.dir = :in), 0)',
                'withdrawals_count' => 'COUNT(*) FILTER (WHERE us.dir = :out)',
                'withdrawals_sum' => 'COALESCE(SUM(us.amount_usd) FILTER (WHERE us.dir = :out), 0)',
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = us.site_id AND u.user_id = us.user_id')
            ->innerJoin(['rr' => Refcodes::TABLE_NAME], 'rr.id = u.refcode_id')
            ->leftJoin(['ua' => UserLogins::TABLE_NAME], 'ua.site_id = us.site_id AND ua.login_id = us.login_id')
            ->leftJoin(['rl' => Refcodes::TABLE_NAME], 'rl.id = ua.refcode_id')
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u.site_id AND usi.user_id = u.user_id')
            ->leftJoin(['uag' => Useragents::TABLE_NAME], 'uag.id = u.useragent_id')
            ->leftJoin(['uagp' => UseragentPlatforms::TABLE_NAME], 'uagp.id = uag.platform_id')
            ->leftJoin(['uaga' => UseragentApps::TABLE_NAME], 'uaga.id = uag.app_id')
            ->where([
                'AND',
                ['>=', 'us.updated_at', $dateFrom],
                ['<', 'us.updated_at', $dateTo],
                [
                    'us.site_id' => $siteId,
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                    'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                ],
            ])
            ->andFilterWhere(['<', 'u.date', $request->getParam('reg_to', null, true)])
            ->groupBy(new Expression('1, 2, 3, 4, 5, 6, 7, 8, 9, 10'))
            ->addParams([
                'in' => UserTransaction::DIR_IN,
                'out' => UserTransaction::DIR_OUT,
            ]);

        $request->filterParams($query);

        return $this->fetchEach($query);
    }

    public function decorator(): \Closure
    {
        return static function ($row) {
            $row['platform_variant'] = static::VARIANT_NAMES[$row['platform_variant'] ?? 0] ?? 'Unknown';

            return $row;
        };
    }
}
