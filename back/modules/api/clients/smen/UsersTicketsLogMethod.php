<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\smen;

use app\back\components\helpers\Str;
use app\back\components\validators\IntInArrayValidator;
use app\back\entities\UserTicket;
use app\back\modules\api\components\Operators;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketLogs;
use Yiisoft\Db\Query\Query;

class UsersTicketsLogMethod extends BaseSmenGetMethod
{
    #[Operators(Operators::EQ_IN)]
    #[IntInArrayValidator(UserTicket::SOURCES)]
    protected array $source = [];

    protected function run(): iterable
    {
        $request = $this->createRequest();

        $query = (new Query($this->db))
            ->select([
                'utl.ticket_id',
                'ut.product_ticket_id',
                'ut.jira_key',
                'utl.status',
                'utl.source',
                'utl.created_by',
                'utl.created_at',
                'updated_at' => 'utl.upserted_at',
            ])
            ->from(['utl' => UserTicketLogs::TABLE_NAME])
            ->innerJoin(['ut' => UserTickets::TABLE_NAME], 'utl.ticket_id = ut.id')
            ->where(['ut.site_id' => $this->siteId]);

        $request->addLimitOffset($query);
        $request->map(['updated_at' => 'utl.upserted_at'], true);
        $request->filterParams($query);

        return $this->fetchEach($query);
    }

    public function decorator(): \Closure
    {
        return static function ($row) {
            $row['status'] = Str::wordsToSnake(UserTicket::STATUSES[$row['status']]);
            $row['source'] = Str::wordsToSnake(UserTicket::SOURCES[$row['source']]);

            return $row;
        };
    }
}
