<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\wp3;

use app\back\components\validators\DateValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\entities\TrafficSource;
use app\back\modules\api\ApiGetMethod;
use app\back\modules\api\components\Operators;
use app\back\modules\api\params\ApiParamSiteIdSecured;
use app\back\modules\api\components\ApiRequest;

abstract class BaseWp3GetMethod extends ApiGetMethod
{
    use ApiParamSiteIdSecured;

    protected const array CURRENCIES = [
        'USD',
        'RUB',
        'EUR',
    ];

    #[IntValidator]
    #[Operators(Operators::EQ)]
    protected array $brand_id = [];

    #[IntInArrayValidator(TrafficSource::NAMES)]
    #[Operators(Operators::EQ_IN_NOT_IN)]
    protected array $traffic_source = [];

    #[DateValidator]
    #[Operators(Operators::EQ)]
    protected array $date;

    protected function getDates(ApiRequest $request): array
    {
        $dateFrom = $request->getParam('date', date('Y-m-d'), true);
        $dateTo = date('Y-m-d', strtotime('+ 1 day', strtotime($dateFrom)));

        return [$dateFrom, $dateTo];
    }
}
