<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\crm\dict;

use app\back\modules\api\ApiGetMethod;
use app\back\repositories\YhOperators;
use Yiisoft\Db\Query\Query;

class DictOperatorsMethod extends ApiGetMethod
{
    public function run(): iterable
    {
        $query = (new Query($this->db))
            ->select(['id', 'email', 'name', 'status'])
            ->from(YhOperators::TABLE_NAME)
            ->orderBy(['id' => SORT_ASC]);

        return $this->fetchEach($query);
    }
}
