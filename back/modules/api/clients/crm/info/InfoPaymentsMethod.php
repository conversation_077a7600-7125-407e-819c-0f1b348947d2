<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\crm\info;

use app\back\components\validators\DateValidator;
use app\back\modules\api\components\Operators;
use app\back\repositories\Refcodes;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

class InfoPaymentsMethod extends BaseCrmInfoMethod
{
    #[Operators(Operators::COMPARISONS)]
    #[DateValidator]
    protected array $payed_at = [];

    public function columns(): array
    {
        return [
            'site_id' => 'us.site_id',
            'user_id' => 'us.user_id',
            'refcode' => 'r.code',
            'dir' => 'us.dir',
            'amount' => 'us.amount_orig',
            'currency' => 'us.currency',
            'payed_at' => 'us.updated_at',
        ];
    }

    public function getMainQuery(): Query
    {
        $query = (new Query($this->db))
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = us.refcode_id');

        $request = $this->createRequest();

        $request->map([
            'payed_at' => 'us.updated_at',
        ], true);

        $request->filterParams($query);

        return $query;
    }

    public function getMainQueryAlias(): string
    {
        return 'us';
    }
}
