<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\crm\filter;

use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\entities\UserGameToken;
use app\back\modules\api\components\Operators;
use app\back\repositories\UserGameTokens;
use Yiisoft\Db\Query\Query;

class GamesMethod extends BaseCrmFilterMethod
{
    #[Operators(Operators::COMPARISONS, Operators::EQ)]
    #[MoneyValidator]
    protected array $bet_sum = [];

    #[Operators(Operators::COMPARISONS, Operators::EQ)]
    #[MoneyValidator]
    protected array $win_sum = [];

    #[Operators(Operators::COMPARISONS, Operators::EQ)]
    #[IntValidator]
    protected array $bet_count = [];

    #[Operators(Operators::COMPARISONS, Operators::EQ)]
    #[IntValidator]
    protected array $win_count = [];

    #[Operators(Operators::COMPARISONS, Operators::EQ)]
    #[MoneyValidator(2, PHP_INT_MIN)]
    protected array $bet_win_sum = [];

    #[Operators(Operators::COMPARISONS)]
    #[DateTimeValidator]
    protected array $played_at = [];

    #[Operators(Operators::IN_NOT_IN)]
    #[IdValidator]
    protected array $game_id = [];

    #[Operators(Operators::COMPARISONS)]
    #[IdValidator]
    protected array $number_of_games_played = [];

    protected function getMainQuery(): Query
    {
        $request = $this->createRequest();

        $query = (new Query($this->db))
            ->select([
                'ug.user_id',
            ])
            ->groupBy([
                'ug.user_id',
            ])
            ->from(['ug' => UserGameTokens::TABLE_NAME]);

        $request->map([
            'site_id' => 'ug.site_id',
            'game_id' => 'ug.game_id',
            'played_at' => 'ug.created_at',
        ], true);

        $query->andWhere($request->buildWhere());

        $havingRequest = $this->createRequest();

        $numberGamesPlayedExpr = 'COUNT(DISTINCT ug.game_id) FILTER (WHERE ug.balance_type = :balance_type_real)';

        $havingRequest->map([
            'bet_sum' => 'COALESCE(SUM(ug.bet_amount_usd), 0)',
            'win_sum' => 'COALESCE(SUM(ug.win_amount_usd), 0)',
            'bet_count' => 'COALESCE(SUM(ug.bet_count), 0)',
            'win_count' => 'COALESCE(SUM(ug.win_count), 0)',
            'bet_win_sum' => 'COALESCE(SUM(ug.bet_amount_usd), 0) - COALESCE(SUM(ug.win_amount_usd), 0)',
            'number_of_games_played' => $numberGamesPlayedExpr,
        ], true);

        $query->andHaving($havingRequest->buildWhere(), $havingRequest->existParam($numberGamesPlayedExpr) ? ['balance_type_real' => UserGameToken::BALANCE_TYPE_REAL] : []);

        return $query;
    }

    protected function getMainQueryAlias(): string
    {
        return 'ug';
    }
}
