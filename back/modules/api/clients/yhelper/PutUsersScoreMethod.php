<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\yhelper;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\entities\UserMetric;
use app\back\modules\api\ApiModifyMethod;
use app\back\repositories\UserMetrics;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class PutUsersScoreMethod extends ApiModifyMethod
{
    #[IdValidator]
    #[IntInArrayValidator([self::class, 'availableSiteIds'], true)]
    protected int $site_id;
    #[BigIdValidator]
    protected int $user_id;
    #[IntValidator(1, 5)]
    public int $score;

    public function __construct(private readonly ConnectionInterface $db, private readonly UserMetrics $metricsRepo)
    {
    }

    public function run(): iterable
    {
        [$metricMarksId, $columnMarksCount, $columnMarksSum] = UserMetric::M_YH_SCORE;

        $userMetrics = (new Query($this->db))
            ->select([
                'scoreCount' => $columnMarksCount,
                'scoreSum' => $columnMarksSum,
            ])
            ->from(UserMetrics::TABLE_NAME)
            ->where([
                'site_id' => $this->params['site_id'],
                'user_id' => $this->params['user_id'],
                'metric' => $metricMarksId,
            ])
            ->one();

        if ($userMetrics === null) {
            $userMetrics['scoreCount'] = UserMetric::YH_SCORE_MARKS_COUNT_START;
            $userMetrics['scoreSum'] = UserMetric::YH_SCORE_MARKS_SUM_START;
        }

        $userMetrics['scoreCount']++;
        $userMetrics['scoreSum'] += $this->score;

        $affected = $this->metricsRepo->updateMetricByUser(
            $this->params['site_id'],
            $this->params['user_id'],
            UserMetric::M_YH_SCORE,
            [$userMetrics['scoreCount'], $userMetrics['scoreSum']]
        );

        return [
            [
                static::AFFECTED_ROWS_RESULT => $affected,
                'player_score' => round($userMetrics['scoreSum'] / $userMetrics['scoreCount'], 1),
                'player_score_count' => $userMetrics['scoreCount']
            ]
        ];
    }
}
