<?php

declare(strict_types=1);

namespace app\back\modules\api\components;

use app\back\components\helpers\File;
use app\back\components\Request;
use Monolog\Formatter\LineFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class ApiLogger extends Logger
{
    private const string CRM_HOST_HEADER = 'Crm-Host-Name';
    private const string CRM_SEGMENT_HEADER = 'Crm-Segment-Id';

    public function setup(string $uniqueId, ?string $requestId, Request $request): void
    {
        $logFilename = APP_ROOT . 'runtime/logs/api/' . date('Y-m-d') . "/$uniqueId.log";
        File::createDirectoryIfNotExists($logFilename, fileNameToDir: true);

        $logHandler = new StreamHandler($logFilename, self::DEBUG, true, 0666);
        $ip = $request->getClientIp() ?? 'no-ip';

        if (empty($requestId) || !preg_match('#^[0-9a-zA-Z]{10,32}$#', $requestId)) {
            $requestId = uniqid('', false);
        }

        $prefix = "[$requestId]";

        $crmHost = $request->headers->get(self::CRM_HOST_HEADER);
        if ($crmHost !== null) {
            $prefix = "[crm-host:$crmHost]$prefix";
        } else {
            $prefix = "[$ip]$prefix";
        }

        $crmSegment = $request->headers->get(self::CRM_SEGMENT_HEADER);
        if ($crmSegment !== null) {
            $prefix .= "[crm-segment:$crmSegment]";
        }

        $logHandler->setFormatter(new LineFormatter("%datetime% $prefix %message% %context%\n", 'Y-m-d H:i:s.v'));

        $this->pushHandler($logHandler);
    }

    public function start(array $query): void
    {
        $this->info('Start', self::getSmallQuery($query));
    }

    public function end(array $info): void
    {
        $this->info('End', $info);
    }

    private static function getSmallQuery(array $query): array
    {
        $result = [];
        foreach ($query as $field => $operators) {
            if (!is_array($operators)) { // PUT
                $result[$field] = $operators;
                continue;
            }

            foreach ($operators as $operator => $values) {
                if (is_array($values) && count($values) > 100) {
                    $values = '[Array ' . count($values) . ']';
                }
                $result[$field][$operator] = $values;
            }
        }

        return $result;
    }
}
