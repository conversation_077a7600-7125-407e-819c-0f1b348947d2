<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\UserIgnoreId;
use Yiisoft\Db\Query\Query;

class UserIgnoreIds extends BaseRepository
{
    public const string ENTITY_CLASS = UserIgnoreId::class;
    public const string TABLE_NAME = 'users_ignores_ids';
    public const array PRIMARY_KEY = ['site_id', 'user_id'];

    public static function excludeUsers(Query $query, string $table, int $mode = UserIgnoreId::MODE_IGNORE): void
    {
        if (in_array($mode, [UserIgnoreId::MODE_IGNORE, UserIgnoreId::MODE_ONLY], true)) {
            $query
                ->leftJoin(['_ui' => static::TABLE_NAME], "_ui.site_id = $table.site_id AND _ui.user_id = $table.user_id")
                ->andWhere([$mode === UserIgnoreId::MODE_ONLY ? 'IS NOT' : 'IS', '_ui.user_id', null]);
        }
    }
}
