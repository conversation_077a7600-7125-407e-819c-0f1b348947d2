<?php

declare(strict_types=1);

namespace app\back\repositories\views;

use Yiisoft\Db\Query\Query;

trait DbMaterializedViewHelper
{
    public function refresh(): void
    {
        $viewName = static::TABLE_NAME;

        $populated = (new Query($this->db))
            ->select('relispopulated')
            ->from('pg_class')
            ->where(['relname' => $viewName])
            ->scalar();

        if ($populated) {
            $this->db->createCommand("REFRESH MATERIALIZED VIEW CONCURRENTLY $viewName")->execute();
        } else {
            $this->db->createCommand("REFRESH MATERIALIZED VIEW $viewName")->execute();
        }
    }
}
