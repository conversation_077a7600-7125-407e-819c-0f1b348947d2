<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\Employee;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use Yiisoft\Db\Query\Query;

class UserKycs extends BaseRepository
{
    public const string ENTITY_CLASS = UserKyc::class;
    public const string TABLE_NAME = 'users_kyc';
    public const array PRIMARY_KEY = ['site_id', 'user_id'];

    public function currentStatus(int $siteId, int $userId): ?int
    {
        return (new Query($this->db))
            ->select('kyc_status')
            ->from(self::TABLE_NAME)
            ->where([
                'site_id' => $siteId,
                'user_id' => $userId,
            ])
            ->scalar() ?: UserKyc::KYC_NOT_VERIFIED;
    }

    public function getLastRequestDepartment(int $siteId, int $userId): ?int
    {
        return (new Query($this->db))
            ->select('(CASE kyc_request.source WHEN :kyc_source_api_s2p THEN :kyc_dep_s2p ELSE e.kyc_department END)')
            ->from(['kyc' => self::TABLE_NAME])
            ->leftJoin(['kyc_request' => UserDocumentProgresses::TABLE_NAME], 'kyc_request.id = kyc.last_request_progress_id')
            ->leftJoin(['e' => Employees::TABLE_NAME], 'kyc_request.created_by = e.employee_id')
            ->where([
                'kyc.site_id' => $siteId,
                'kyc.user_id' => $userId,
            ])
            ->addParams([
                ':kyc_dep_s2p' => Employee::KYC_DEPARTMENT_S2P_RISK,
                ':kyc_source_api_s2p' => UserDocumentProgress::SOURCE_API_S2P,
            ])
            ->scalar() ?: null;
    }
}
