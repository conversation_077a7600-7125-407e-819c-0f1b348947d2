<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\components\helpers\Str;
use app\back\entities\AffParam;
use Yiisoft\Db\Query\Query;

class AffParams extends BaseRepository
{
    public const string ENTITY_CLASS = AffParam::class;
    public const string TABLE_NAME = 'aff_params';
    public const array PRIMARY_KEY = ['id'];

    public const string AFF_PARAMS_OBJ_COL_NAME = 'params_obj';

    public static function affParamsElements(string $tableAlias): string
    {
        return "(SELECT jsonb_array_elements_text($tableAlias.aff_params)::int)";
    }

    public static function allParamsExpression(string $tableAlias = 'ap', string $colName = self::AFF_PARAMS_OBJ_COL_NAME): string
    {
        return "(SELECT STRING_AGG(k || '=' || v, '&' ORDER BY k) FROM jsonb_each_text($tableAlias.$colName) as t(k, v))";
    }

    public function getIds(?string $affData): ?array
    {
        $params = self::parseAffData($affData);

        $data = [];

        foreach ($params as $param => $value) {
            $paramId = AffParam::PARAM_IDS[$param] ?? null;

            if ($paramId === null) {
                continue;
            }

            if ($param === AffParam::P_SUBDATA && !preg_match('#^[0-9a-z]{32}$#', $value)) {
                continue;
            }

            $data[] = [
                'param_id' => $paramId,
                'value' => $value,
            ];
        }

        if (empty($data)) {
            return null;
        }

        // Check happy way
        $ids = $this->selectIdsByParamIdsAndValues($data);

        if (count($ids) === count($data)) {
            return $ids; // All params already inserted
        }

        $this->batchUpsert($data, [], '(param_id, value)');

        return $this->selectIdsByParamIdsAndValues($data);
    }

    public function getSubDataAffParamId(?string $affData): ?int
    {
        $subdata = self::parseAffData($affData)[AffParam::P_SUBDATA] ?? null;

        if (empty($subdata) || strlen($subdata) !== 32) {
            return null;
        }
        $paramId = AffParam::PARAM_IDS[AffParam::P_SUBDATA];

        $this->upsert(['param_id', 'value'], ['param_id' => $paramId, 'value' => $subdata]);

        return (new Query($this->db))
            ->select(['id'])
            ->from(self::TABLE_NAME)
            ->where('param_id = :param_id and value = :value', [':param_id' => $paramId, ':value' => $subdata])
            ->limit(1)
            ->column()[0] ?? null;
    }

    private function selectIdsByParamIdsAndValues(array $data): array
    {
        return (new Query($this->db))
            ->select(['id'])
            ->from(self::TABLE_NAME)
            ->where(['IN', ['param_id', 'value'], $data])
            ->orderBy(['id' => SORT_ASC])
            ->column();
    }

    public static function parseAffData(?string $val): array
    {
        if ($val === '' || $val === null) {
            return [];
        }

        //click_id%3D3H85xYHx8iuWrXxdCtVHmteTVg66dcBJJ9%26geo%3Dru%26referer%3D939266
        if ((str_contains($val, '%3D') || str_contains($val, '%26')) && !str_contains($val, '=')) {
            $val = urldecode($val);
        }

        parse_str($val, $params);

        foreach ($params as $p => &$v) {
            if (!is_string($v)) {
                unset($params[$p]);
                continue;
            }

            if ($p === 'src') {
                $decoded = base64_decode($v);

                if ($decoded !== false && Str::isValidDomain($decoded)) {
                    $v = $decoded;
                }
            }
            $v = Str::cleanString($v);
            if (mb_strlen($v, 'UTF-8') > 200) {
                $v = mb_substr($v, 0, 200, 'UTF-8');
            }
        }
        unset($v);

        $params = array_filter($params, static fn ($v) => $v !== '');

        ksort($params);

        return $params;
    }

    public static function clickIdExpression(string $table = 'ap', string $field = 'value'): string
    {
        return "SUBSTRING($table.$field, '(?:(?:[a-zA-Z0-9]{12}|[a-zA-Z0-9]{32})_)?([\w-]{7,150})(?:&|$|%|\w)')";
    }

    public static function tokenIdExpression(string $table = 'ap', string $field = 'value'): string
    {
        return "SUBSTRING($table.$field, '(^[a-zA-Z0-9]{12}(?=_)|^[a-zA-Z0-9]{32})')";
    }

    /**
     * Universal query to get all aff params as jsonb object
     * @see self::AFF_PARAMS_OBJ_COL_NAME
     */
    public static function paramsAsObjJoinQuery(string $tableAlias): string
    {
        $idToNameExpression = self::paramNameExpression('ap');
        $colName = self::AFF_PARAMS_OBJ_COL_NAME;
        $affParamsElements = self::affParamsElements($tableAlias);
        $table = self::TABLE_NAME;
        return "(
            SELECT
                jsonb_object_agg($idToNameExpression, ap.value) as $colName
            FROM
                $table ap
            WHERE
                ap.id IN $affParamsElements
        )";
    }

    /**
     * Universal query to get one specific aff param
     */
    public static function paramValueJoinQuery(string $tableAlias, string $paramName): string
    {
        $paramId = AffParam::PARAM_IDS[$paramName];
        $affParamsElements = self::affParamsElements($tableAlias);
        $table = self::TABLE_NAME;

        return "(
            SELECT
                ap.value
            FROM
                $table ap
            WHERE
                ap.id IN $affParamsElements
            AND
                ap.param_id = $paramId
        )";
    }

    private static function paramNameExpression(string $tableAlias): string
    {
        $cases = [];
        foreach (AffParam::PARAM_IDS as $name => $id) {
            $cases[] = "WHEN $id THEN '$name'";
        }

        $paramIdColumn = $tableAlias ? "$tableAlias.param_id" : 'param_id';

        return "CASE $paramIdColumn " . implode(' ', $cases) . ' END';
    }
}
