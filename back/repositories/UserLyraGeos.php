<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\UserLyraGeo;
use Yiisoft\Db\Expression\Expression;

class UserLyraGeos extends BaseRepository
{
    public const string ENTITY_CLASS = UserLyraGeo::class;
    public const string TABLE_NAME = 'users_lyra_geo';
    public const array PRIMARY_KEY = ['site_id', 'user_id'];

    public function getGeoSumFieldSql(array $fieldIds, string $usersTable = 'u', $usersLyraGeoTable = 'ulg'): Expression
    {
        $fields = array_map(static fn($f) => "COALESCE({$f}, :ev)", $this->getFieldsMapSql($fieldIds, $usersTable, $usersLyraGeoTable));

        return new Expression("CONCAT_WS(:del, " . implode(',', $fields) . ")", ['del' => UserLyraGeo::DELIMITER, 'ev' => UserLyraGeo::EMPTY_VALUE]);
    }

    private function getFieldsMapSql(array $fieldIds = [], string $usersTable = 'u', $usersLyraGeoTable = 'ulg'): array
    {
        sort($fieldIds);
        $fieldsMap = [];
        foreach ($fieldIds as $fieldId) {
            $fieldsMap[] = match ($fieldId) {
                UserLyraGeo::FIELD_ID_REG_COUNTRY => "{$usersTable}.country",
                UserLyraGeo::FIELD_ID_LAST_LOGIN_COUNTRY => "{$usersLyraGeoTable }.last_login_country",
                UserLyraGeo::FIELD_ID_SYS_LANGUAGE => "{$usersLyraGeoTable}.last_session_system_language",
                UserLyraGeo::FIELD_ID_LAST_DEP_COUNTRY => "{$usersLyraGeoTable}.last_dep_country",
                UserLyraGeo::FIELD_ID_LAST_DEP_CURRENCY => "{$usersLyraGeoTable}.last_dep_currency",
                UserLyraGeo::FIELD_ID_LAST_WD_COUNTRY => "{$usersLyraGeoTable}.last_wd_country",
                UserLyraGeo::FIELD_ID_LAST_WD_CURRENCY => "{$usersLyraGeoTable}.last_wd_currency"
            };
        }
        return $fieldsMap;
    }
}
