<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\UserStatusPrevipThreshold;
use Yiisoft\Db\Query\Query;

class UserStatusPrevipThresholds extends BaseRepository
{
    public const string ENTITY_CLASS = UserStatusPrevipThreshold::class;
    public const string TABLE_NAME = 'users_statuses_previp_thresholds';
    public const array PRIMARY_KEY = ['country'];

    public function paramsByCountryConfig(): array
    {
        return (new Query($this->db))
            ->select(['country', 'in_amount_initial', 'in_amount_week'])
            ->from(['nt' => self::TABLE_NAME])
            ->indexBy('country')
            ->all();
    }
}
