<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\UserWallet;
use Yiisoft\Db\Query\Query;

class UserWallets extends BaseRepository
{
    public const string ENTITY_CLASS = UserWallet::class;
    public const string TABLE_NAME = 'users_wallets';
    public const array PRIMARY_KEY = ['site_id', 'wallet_id', 'type'];

    public static function getActiveCondition(?string $table = null): string
    {
        if ($table) {
            $table .= '.';
        }

        return "({$table}expired_at IS NULL OR {$table}expired_at > now()) AND ({$table}deleted_at IS NULL OR {$table}deleted_at > now())";
    }

    public function walletBalancesSubQuery(string $userTableAlias, int $type): Query
    {
        $mapPostfixes = [
            '_usd' => '_usd',
            '_orig' => '',
            '_eur' => '_eur',
        ];

        $select['currency'] = 'ANY_VALUE(uw.currency) FILTER (WHERE ' . self::getActiveCondition('uw') . ')';
        foreach ($mapPostfixes as $postfixAlias => $postfixColumn) {
            $select["balance$postfixAlias"] = "COALESCE(SUM(uw.balance$postfixColumn) FILTER (WHERE " . self::getActiveCondition('uw') . '), 0)';
        }

        return (new Query($this->db))
            ->select($select)
            ->from(['uw' => self::TABLE_NAME])
            ->where(['AND',
                ['uw.type' => $type],
                "uw.site_id = $userTableAlias.site_id AND uw.user_id = $userTableAlias.user_id"
            ]);
    }
}
