<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\TrafficSourceRule;

class TrafficSourceRules extends BaseRepository
{
    public const string ENTITY_CLASS = TrafficSourceRule::class;
    public const string TABLE_NAME = 'traffic_sources_rules';
    public const array PRIMARY_KEY = ['code'];

    public static function getSortExpression(?string $tableAlias = null): string
    {
        $column = '"order"';
        if ($tableAlias) {
            $column = "$tableAlias.$column";
        }

        return "$column DESC NULLS LAST";
    }
}
