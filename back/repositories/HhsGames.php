<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\HhsGame;
use Yiisoft\Db\Query\Query;

class HhsGames extends BaseRepository
{
    use DbDictionaryHelper;

    public const string ENTITY_CLASS = HhsGame::class;
    public const string TABLE_NAME = 'hhs_games';
    public const array PRIMARY_KEY = ['game_id'];
    public const string NAME_KEY = 'game_name';

    public function getUniqNamesByPrefix(string $param): array
    {
        $map = $this->getNamesByPrefix($param);
        $map = array_unique($map);
        natcasesort($map);

        return array_combine($map, $map);
    }

    public function getVendors(): array
    {
        static $map = null;

        if (is_null($map)) {
            $map = (new Query($this->db))
                ->select('vendor')
                ->from(self::TABLE_NAME)
                ->groupBy('vendor')
                ->column();
            natcasesort($map);

            $map = array_combine($map, $map);
        }

        return $map;
    }

    public function getUniqNames(): array
    {
        static $map = null;

        if (is_null($map)) {
            $map = $this->getNames();
            $map = array_unique($map);
            natcasesort($map);
            $map = array_combine($map, $map);
        }

        return $map;
    }
}
