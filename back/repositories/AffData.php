<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\AffDatum;
use Yiisoft\Db\Query\Query;

class AffData extends BaseRepository
{
    public const string ENTITY_CLASS = AffDatum::class;
    public const string TABLE_NAME = 'aff_data';
    public const array PRIMARY_KEY = ['id'];

    public static function extractFromUrl(string $url): ?string
    {
        $url = urldecode($url);
        $query = parse_url($url, PHP_URL_QUERY);
        if (!is_string($query)) {
            return null;
        }

        parse_str($query, $params);
        if (empty($params['affdata'])) {
            return null;
        }

        return http_build_query($params['affdata']);
    }

    private function add(string $affData): AffDatum
    {
        $entity = new AffDatum();
        // TODO: validation
        $entity->aff_data = $affData;
        parse_str($affData, $params);
        $this->insert($entity);

        return $entity;
    }

    /** The only way to save new AffData */
    public function getIdByAffData(?string $affData): ?int
    {
        $affParams = AffParams::parseAffData($affData);

        if (empty($affParams)) {
            return null;
        }

        $affData = http_build_query($affParams);

        if (mb_strlen($affData, 'UTF-8') > 255) {
            $affData = mb_substr($affData, 0, 255, 'UTF-8');
        }

        $id = (new Query($this->db))
            ->select(['id'])
            ->from(self::TABLE_NAME)
            ->where(['aff_data' => $affData])
            ->scalar();

        if ($id === false) {
            $id = $this->add($affData)->id;
        }

        return $id;
    }
}
