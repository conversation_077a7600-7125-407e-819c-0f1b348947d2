<template>
    <div
        v-for="groupIndex in Array(Math.ceil(items.length / groupSize)).keys()"
        class="btn-group flex-wrap d-block"
        :class="{
            [`btn-group-${size}`]: true,
            'is-invalid': isInvalid,
            'vertical-list': vertical,
        }"
    >
        <button
            v-for="item in items.slice(groupIndex * groupSize, (groupIndex + 1) * groupSize)"
            type="button"
            :class="classObject(item)"
            @click="onClick(item)"
            @keydown="onKeyDown"
        >
            {{ item.name }} <span class="badge bg-primary">{{ item.count }}</span>
        </button>
    </div>
</template>

<script lang="ts" setup>
import { ClassList, FormComponentCommon, Item, ScalarOrEmpty, ScalarValue, Value } from '@/types.ts'
import { computed } from 'vue'

defineOptions({
    inheritAttrs: false,
})

type ScalarItem = Item<ScalarValue> & {
    class?: string
}

const $props = withDefaults(defineProps<FormComponentCommon & {
    value: Value
    list: ScalarItem[]
    multiple?: boolean
    vertical?: boolean
    hideInvalidItem?: boolean
    groupSize?: number
    activeWithShadow?: boolean
}>(), {
    enabled: true,
    multiple: true,
    groupSize: 100000,
})

const $emit = defineEmits<{
    change: [value: null | ScalarValue | ScalarValue[]]
}>()

const normalizedValue = computed<ScalarValue[]>(() => {
    if ($props.value === undefined || $props.value === null || $props.value === '') {
        return []
    }

    if (!Array.isArray($props.value)) {
        return [$props.value]
    }

    return [...$props.value]
})

const items = computed(() => {
    const result = $props.list.slice()

    if (!$props.hideInvalidItem) {
        const availableIds = result.map(item => item.id)
        const values = normalizedValue.value.slice()

        const missedIds = values.filter(v => !listHasSameValue(availableIds, v))

        missedIds.forEach(id => {
            result.push({id, name: id + ' (invalid or not exist)'})
        })
    }

    return result
})

function onClick (item: ScalarItem) {
    let newValue = null

    if ($props.multiple) {
        newValue = normalizedValue.value.slice()
        const idx = newValue.findIndex((val) => isSame(item.id, val))
        if (idx !== -1) {
            newValue.splice(idx, 1)
        } else {
            newValue.push(item.id)
        }
    } else {
        if (Array.isArray($props.value)) {
            throw new Error('Value is array, but list is not multiple')
        }
        newValue = isSame($props.value, item.id) ? null : item.id
    }

    $emit('change', newValue)
}

function classObject (item: ScalarItem) {
    const result: ClassList = {
        'btn': true,
        'mb-1': true,
        [item.class || 'btn-outline-secondary']: true,
        'btn-outline-danger': $props.isInvalid,
        active: isActive(item),
        activeWithShadow: isActive(item) && $props.activeWithShadow,
        disabled: !$props.enabled || !(item.id !== null && item.id !== ''),
    }

    return result
}

function isActive ({id}: ScalarItem) {
    return listHasSameValue(normalizedValue.value, id)
}

function onKeyDown (e: KeyboardEvent) {
    const t = e.target as HTMLElement
    if ((e.code === 'ArrowUp' || e.code === 'ArrowLeft') && t.previousSibling) {
        (t.previousSibling as HTMLElement).focus()
        e.preventDefault()
    } else if ((e.code === 'ArrowDown' || e.code === 'ArrowRight') && t.nextSibling) {
        (t.nextSibling as HTMLElement).focus()
        e.preventDefault()
    }
}

function listHasSameValue (list: ScalarOrEmpty[], value: ScalarOrEmpty) {
    return list.some((itemVal) => isSame(value, itemVal))
}

function isSame (val1: ScalarOrEmpty, val2: ScalarOrEmpty) {
    val1 = val1 === undefined ? null : val1
    val2 = val2 === undefined ? null : val2

    return val1 === val2 ||
        (val1 !== null && val2 !== null && val1.toString() === '' + val2.toString())
}
</script>

<style lang="scss">
.vertical-list button {
    width: 100%;
    margin: 0 !important;

    &:first-child {
        border-radius: 5px 5px 0 0 !important;
    }

    &:last-child {
        border-radius: 0 0 5px 5px !important;
    }

    &:not(:first-child) {
        border-top: 0 !important;
    }
}
.activeWithShadow {
    box-shadow: var(--bs-btn-focus-box-shadow);
    text-shadow: 0 0 1px var(--bs-btn-active-color), 0 0 1px var(--bs-btn-active-color);
}
</style>
