<template>
    <div
        class="btn-group d-flex flex-wrap"
        :class="{[`btn-group-${size}`]: true, 'is-invalid': isInvalid}"
    >
        <template v-for="item in items">
            <input
                :id="`${id}-${item.id}`"
                type="radio"
                autocomplete="off"
                class="btn-check"
                :checked="value === item.id"
                @change="$emit('change', item.id)"
                @focus="focused = item.id"
                @blur="focused = null"
            >
            <label
                class="btn"
                :class="classObject(item)"
                :for="`${id}-${item.id}`"
            >{{ item.name }}</label>
        </template>
    </div>
</template>

<script lang="ts" setup>
import { FormComponentCommon, Item, ScalarOrEmpty, ScalarValue } from '@/types.ts'
import { computed, ref } from 'vue'

type ScalarItem = Item<ScalarValue>

const $props = withDefaults(defineProps<FormComponentCommon & {
    value: ScalarOrEmpty
    list: ScalarItem[]
    buttonStyle?: string
    ignoreNotExists?: boolean
}>(), {
    enabled: true,
    size: 'md',
    buttonStyle: 'btn-outline-secondary',
})

const $emit = defineEmits<{
    change: [value: null | ScalarValue | ScalarValue[]]
}>()

const focused = ref<ScalarOrEmpty>(null)

const items = computed(() => {
    const list = [...$props.list]

    if (!$props.ignoreNotExists && $props.value !== null && $props.value !== undefined && !list.some(({ id }) => id === $props.value)) {
        list.push({ id: $props.value, name: `${$props.value} (not exists)` })
    }

    return list
})

function classObject (item: ScalarItem) {
    return {
        focus: item.id === focused.value,
        active: item.id === $props.value,
        disabled: !$props.enabled,
        [$props.isInvalid ? 'btn-outline-danger': $props.buttonStyle]: true,
    }
}

</script>
