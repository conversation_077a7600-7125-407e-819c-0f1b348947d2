<template>
    <tr>
        <td
            v-for="column in columns.filter(({hidden}) => !hidden)"
            v-bind="cellProps(row, column)"
            :key="column.code || column.slotName"
        >
            <slot
                v-if="column.slotName"
                v-bind="{column, row, rowIndex}"
                :name="column.slotName"
            />
            <div
                v-else-if="column.raw"
                v-html="row[column.code]"
            />
            <template v-else-if="typeof row[column.code] === 'object' && 'text' in(row[column.code] as TableStyleValue)">
                {{ (row[column.code] as TableStyleValue)?.text }}
            </template>
            <template v-else>
                {{ row[column.code] }}
            </template>
        </td>
    </tr>
</template>

<script lang="ts" setup>
import type { TdHTMLAttributes } from 'vue'
import { TableRow, TableColumn, TableStyleValue } from '@/types'

defineProps<{
    row: TableRow
    rowIndex: number
    columns: TableColumn[]
    cellProps: ((row: TableRow, column: TableColumn) => TdHTMLAttributes)
}>()
</script>
