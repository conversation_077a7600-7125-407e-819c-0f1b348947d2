<template>
    <nav v-if="lastPage > 1">
        <ul class="pagination pagination-sm justify-content-center">
            <li
                class="page-item"
                :class="{disabled: isPreviousItemDisabled}"
                @click="selectPreviousItem"
            >
                <a class="page-link"><Icona name="icn-arrow-left" /></a>
            </li>
            <li
                v-for="i in items"
                class="page-item"
                :class="{active: i === page, disabled}"
            >
                <a
                    class="fw-bold page-link"
                    href="javascript:void(0)"
                    @click="pageSelect(i)"
                >{{ i }}</a>
            </li>
            <li
                class="page-item"
                :class="{disabled: isNextItemDisabled}"
                @click="selectNextItem"
            >
                <a class="page-link"><Icona name="icn-arrow-right" /></a>
            </li>
        </ul>
    </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icona from './icona.vue'
import { PaginationType } from '@/types.ts'

export interface PageChangedEvent {
    page: number
}

const $props = withDefaults(defineProps<PaginationType>(), {
    lastPage: 1,
    page: 1,
    pageSize: 100,
    firstItemsNum: 5,
})

const $emit = defineEmits<{
    pageChanged: [PageChangedEvent]
}>()

const items = computed((): number[] => {
    const list: number[] = []
    const firstItems = $props.lastPage <= $props.firstItemsNum ? $props.lastPage : $props.firstItemsNum
    const midItem = Math.floor($props.firstItemsNum / 2)
    const offset = $props.page > midItem ? midItem : $props.page - 1

    if ($props.page > midItem + 1) {
        list.push(1)
    }

    list.push(
        ...Array.from(Array(firstItems).keys())
            .map(i => i - offset + $props.page)
            .filter(i => i < $props.lastPage),
    );

    [2, 5, 10, 100].forEach(
        multiplier => {
            const page = $props.firstItemsNum * multiplier

            if (page < $props.lastPage && page > $props.page && !list.includes(page)) {
                list.push($props.firstItemsNum * multiplier)
            }
        },
    )

    if ($props.lastPage > $props.firstItemsNum || (list.length < $props.lastPage && !list.includes($props.lastPage))) {
        list.push($props.lastPage)
    }

    return list
})


const isPreviousItemDisabled = computed(() => {
    return $props.page === 1 || $props.disabled
})

const isNextItemDisabled = computed(() => {
    return $props.page === $props.lastPage || $props.disabled
})

function pageSelect(pageNum: number) {
    if (pageNum !== $props.page) {
        $emit('pageChanged', { page: pageNum })
    }
}

function selectPreviousItem() {
    if (!isPreviousItemDisabled.value) {
        pageSelect($props.page - 1)
    }
}

function selectNextItem() {
    if (!isNextItemDisabled.value) {
        pageSelect($props.page + 1)
    }
}
</script>
