import { promises as fs } from 'fs';
import { PluginOption } from 'vite'

export default function svgLoader(): PluginOption {
    return {
        name: 'svg-loader',
        enforce: 'pre',

        async load(id: string) {
            if (!id.match(/\.svg$/)) {
                return;
            }
            const [path] = id.split('?', 2);
            const svg = await fs.readFile(path, 'utf-8');
            return `export default ${JSON.stringify(svg)}`;
        },
    };
}
