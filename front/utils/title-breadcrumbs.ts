import { RouteRecordNormalized } from 'vue-router'
import { ref, readonly } from 'vue'

const breadcrumbs = ref([] as RouteRecordNormalized[])

export function useTitleBreadcrumbs() {

    function setTitle (newTitle: string, push = false): void {
        setDocumentTitle(newTitle)

        if (push) {
            breadcrumbs.value.push({ meta: { title: newTitle } } as unknown as RouteRecordNormalized)
        } else {
            breadcrumbs.value.slice(-1)[0].meta.title = newTitle // Change title of last route
        }
    }

    function setDocumentTitle (newTitle: string): void {
        document.title = newTitle + ' - Analytics'
    }

    function setMatchedRoutes (routes: RouteRecordNormalized[]): void {
        breadcrumbs.value = routes.slice()
        const lastRoute = breadcrumbs.value.slice(-1)[0] as unknown as RouteRecordNormalized
        if (lastRoute?.meta?.title !== undefined) {
            setDocumentTitle(lastRoute.meta.title as string)
        }
    }

    return {
        breadcrumbs: readonly(breadcrumbs),
        setDocumentTitle,
        setTitle,
        setMatchedRoutes,
    }
}
