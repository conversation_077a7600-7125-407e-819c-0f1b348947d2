import { Notyf, NotyfEvent } from 'notyf'
import { NotifyMessage } from '@/types'
import { nextTick } from 'vue'

export function useNotify () {
    const notify = new Notyf({
        position: { x: 'center', y: 'top' },
        duration: 8000,
        dismissible: false,
        types: [
            { type: 'success', className: 'border-success-subtle border border-3 rounded bg-success-subtle text-body', background: 'none', icon: false },
            { type: 'warning', className: 'border-warning-subtle border border-3 rounded bg-warning-subtle text-body', background: 'none', icon: false },
            { type: 'error', className: 'border-danger-subtle border border-3 rounded bg-danger-subtle text-body', background: 'none', icon: false },
            { type: 'info', className: 'border-info-subtle border border-3 rounded bg-info-subtle text-body', background: 'none', icon: false },
        ],
    })

    return function (notification: NotifyMessage) {
        if (typeof notification === 'string') {
            notification = { message: notification, type: 'info' }
        }
        nextTick(() => {
            const instance = notify.open(notification)
            instance.on(NotyfEvent.Click, () => notify.dismiss(instance))
        })
    }
}

