export default function (text: string, name: string, mimeType = 'text/csv;charset=utf-8') {
    const pom = document.createElement('a')
    pom.setAttribute('href', 'data:' + mimeType + ',' + encodeURIComponent(text))
    pom.setAttribute('download', name)

    if (document.createEvent) {
        const event = document.createEvent('MouseEvents')
        event.initEvent('click', true, true)
        pom.dispatchEvent(event)
    } else {
        pom.click()
    }
}
