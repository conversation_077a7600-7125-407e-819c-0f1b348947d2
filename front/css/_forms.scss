.form-control-xs {
    padding: $input-padding-y-xs $input-padding-x-xs;
    font-size: $font-size-xs;
    @include border-radius($input-border-radius-xs);
}

.input-group-xs > .form-control,
.input-group-xs > .custom-select,
.input-group-xs > .input-group-text,
.input-group-xs > .btn {
    padding: $input-padding-y-xs $input-padding-x-xs;
    @include font-size($font-size-xs);
    @include border-radius($input-border-radius-xs);
}

.form-control {
    &.is-invalid, &.is-valid {
        background: none;
    }
}

.label-lg {
    font-size: $font-size-lg;
    padding-bottom: $input-btn-padding-y-lg;
}

.label-sm {
    font-size: $font-size-sm;
    padding-bottom: $input-btn-padding-y-sm;
}

.label-xs {
    font-size: $font-size-xs;
    padding-bottom: $input-btn-padding-y-xs;
}

.form-control.is-invalid, .form-control:invalid {
    border-color: var(--bs-danger)
}
.invalid-tooltip {
    max-width: none;
    min-width: 4.5rem; // To fit "is required" on one line
    font-size: 0.75rem;
    &:hover {
        opacity: 100%;
    }
}
