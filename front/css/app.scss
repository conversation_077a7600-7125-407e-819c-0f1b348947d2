@use "sass:map";

@import "colors";

$color-mode-type:             data;
$min-contrast-ratio:            3;

@import "bootstrap/scss/functions";
@import "bootstrap/scss/mixins";

@import "colors-light";
@import "colors-dark";

@import "variables";
@import "bootstrap/scss/bootstrap";
@import "buttons";
@import "forms";
@import "card";
@import "timeline";
@import "direct-chat";
@import "highcharts";

@import "flatpickr-theme";
@import 'notyf/notyf.min.css';

/* Vue.js */
[v-cloak] {
    display: none;
}

/* Notify */
.notyf {
    padding-top: 53px;
    z-index: 900; // Lower than 1000 because of main manu dropdown cover
}
.notyf__toast {
    max-width: map.get($container-max-widths, lg);
    .notyf__wrapper {
        padding-right: 0;
    }
}

/* Other */
a {
    text-decoration: none;
}

.pointer:hover {
    cursor: pointer;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
}

.cursor-draggable {
    cursor: grab;
}

/* fix chrome 92.0.4515.107 full screen image view position */
.lum-lightbox-inner .lum-lightbox-image-wrapper {
    display: block;
}

.badge {
    line-height: unset; // Badge style like XS button, see _variables.scss
    .bg-secondary {
        color: var(--bs-body-color);
    }
    display: inline; // Different height in btn-group with and without badges
}
.sticky-top {
    z-index: auto;
}

pre.wrapped {
    white-space: pre-wrap;
    margin-bottom: 0;
    line-height: 1.2;
}

.smallest {
    font-size: 12px;
}

/* to emulate "disabled" attribute visual and behaviour */
.entity-table-disabled {
    button, input,  textarea, select, span.an-popover, th.pointer {
        opacity: 0.6;
        pointer-events: none;
    }
}

@include color-mode(dark, true) {
    .colors-inverse {
        &.text-body {
            color: var(--bs-gray-900) !important;
        }
        &.bg-body {
            background-color: var(--bs-gray-100) !important;
        }
    }
}

@include color-mode(light, true) {
    .colors-inverse {
        &.text-body {
            color: var(--bs-gray-100) !important;
        }
        &.bg-body {
            background-color: var(--bs-gray-900) !important;
        }
    }
}

.popover-header { // For popover an popover-singleton components with buttons in header and close button at the end
    display: flex;
    justify-content: space-between;
}
