<template>
    <Card>
        <RichTable
            v-bind="richTable"
            showPagination
            showRefresh
            @reload="onReload"
        >
            <template #afterTitle="{refreshCallback}">
                <Popover
                    position="bottom"
                    @open="onOpenCreate"
                >
                    <button
                        type="button"
                        class="btn btn-sm btn-success"
                    >
                        <Icona name="icn-plus" /> Add
                    </button>
                    <template #content>
                        <FormGrid
                            v-bind="createForm"
                            @change="createForm.values = $event"
                            @submit="onCreateSubmit($event, refreshCallback)"
                        />
                    </template>
                </Popover>
            </template>

            <template #name="{row, refreshCallback}">
                <InplaceEdit
                    :value="row.ts_id"
                    type="select"
                    :list="row.all_traffic_sources"
                    @submit="onUpdateSubmit({code: row.code, ts_id: $event, column: 'ts_id' }, refreshCallback)"
                />
            </template>

            <template #order="{row, refreshCallback}">
                <InplaceEdit
                    :value="row.order"
                    type="input"
                    @submit="onUpdateSubmit({code: row.code, order: $event, column: 'order' }, refreshCallback)"
                />
            </template>

            <template #actions="{row, refreshCallback}">
                <div class="btn-group btn-group-sm">
                    <button
                        type="button"
                        class="btn btn-xs btn-info"
                        @click="onConflictInfo(row.code, refreshCallback)"
                    >
                        <Icona name="icn-info" /> Conflict rules
                    </button>

                    <button
                        type="button"
                        class="btn btn-xs btn-primary"
                        @click="onCount(row.code, refreshCallback)"
                    >
                        <Icona name="icn-info" /> Count
                    </button>

                    <button
                        type="button"
                        class="btn btn-xs btn-danger"
                        @click="onDelete(row.code, refreshCallback)"
                    >
                        <Icona name="icn-delete" /> Delete
                    </button>
                </div>
            </template>
        </RichTable>
    </Card>
</template>

<script lang="ts">
import { FormGrid, RichTable, Popover, InplaceEdit, Icona } from '@/components'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, Values, NextWithReload, RichTableType } from '@/types'

export default defineComponent({
    components: {
        Icona,
        Card,
        Popover,
        RichTable,
        FormGrid,
        InplaceEdit,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {} as RichTableType,
            createForm: {} as FormGridType,
        }
    },

    methods: {
        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },
        reload (params: Values) {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        async onOpenCreate () {
            this.createForm = await this.$fetch(this.$route.path + '/create-form')
        },
        onDelete (code: string, refreshCallback: () => void) {
            if (confirm(`Do you really want to delete traffic source rule ${code} ?`)) {
                this.$fetch(this.$route.path + '/delete', { code })
                    .then(refreshCallback)
            }
        },
        onCount(code: string, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/count', { code })
                .then(refreshCallback)
        },
        onConflictInfo(code: string, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/conflictInfo', { code })
                .then(refreshCallback)
        },
        onCreateSubmit (values: Values, refreshCallback: () => void) {
            this.$processFormResponse(this.$fetch(this.$route.path + '/create', values), this.createForm)
                .then(refreshCallback)
        },
        onUpdateSubmit (values: Values, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/update', values)
                .then(refreshCallback)
        },
    },
})
</script>
