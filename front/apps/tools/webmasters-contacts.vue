<template>
    <Card>
        <RichTable
            v-bind="richTable"
            :showTotal="false"
            :reloadOnChange="false"
            @reload="onReload"
            @change="onChange"
        />
    </Card>
</template>

<script lang="ts">

import { RichTable } from '@/components'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { NextWithReload, Values, FormGridType, RichTableType } from '@/types'

export default defineComponent({
    components: {
        Card,
        RichTable,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {} as RichTableType,
        }
    },
    methods: {
        onChange (values: Values) {
            if (this.richTable.form) {
                this.richTable.form.values = values
                this.$historyReplaceParams(values)
            }
        },
        async reload (params: Values) {
            if (Object.keys(params).length === 0) {
                return this.$fetch(this.$route.path + '/form').then((form: FormGridType) => this.richTable.form = form)
            }

            return this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        onReload (params: Values) {
            return this.reload(params)
        },
    },
})
</script>
