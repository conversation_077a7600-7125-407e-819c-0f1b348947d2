<template>
    <div>
        <h4>Verification change</h4>
        <div class="row">
            <FormGrid
                class="kyc-status-form col"
                v-bind="verifyForm"
                @change="onVerificationChange"
                @submit="onVerificationSubmit"
            />
        </div>
        <div class="form-grid">
            <div
                v-if="detailsFormList.length || detailsAddForm !== null"
                class="form-group"
            >
                <div class="container-fluid px-0">
                    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-xl-4 gy-4">
                        <div v-for="(details, detailIndex) in detailsFormList" class="col">
                            <div class="border rounded position-relative p-2 pb-0">
                                <button
                                    v-if="detailsAddForm !== null"
                                    class="position-absolute btn link-danger top-0 end-0 pe-2 pt-2 border-0"
                                    :disabled="!verifyForm.enabled"
                                    @click="onDetailsDelete(detailIndex)"
                                >
                                    <Icona name="icn-delete" />
                                </button>
                                <FormGrid
                                    v-bind="details"
                                    :enabled="verifyForm.enabled"
                                    @change="onDetailsChange(detailIndex, $event)"
                                />
                            </div>
                        </div>
                        <div v-if="detailsAddForm !== null" class="col">
                            <div class="border rounded position-relative p-2 pb-0">
                                <FormGrid
                                    v-bind="detailsAddForm"
                                    :enabled="verifyForm.enabled"
                                    @change="onDetailsAdd($event)"
                                    @paste="onDetailsAddPaste($event.target)"
                                />
                                <div class="text-center w-100 pb-3" style="font-size: 3.75rem">
                                    <Icona name="icn-plus" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">

import { FormGrid } from '@/components'
import { defineComponent, PropType } from 'vue'
import { Errors, FormElement, FormGridType, Values } from '@/types'
import Icona from '@/components/icona.vue'

interface VerifyForm extends FormGridType {
    values: Values
    statusInDb: number
    changeImmediately?: {status: number}
}

const KYC_URL = '/finance/documents-kyc'

interface User {
    siteId: number
    userId: number
}

interface DetailsFormsDefaultsResponse {
    formsFilled?: FormGridType[]
    formAdd?: FormGridType
}

export default defineComponent({
    components: {
        Icona,
        FormGrid,
    },
    props: {
        user: {
            type: Object as PropType<User>,
            required: true,
        },
        transactionIds: {
            type: Array<string>,
            required: true,
        },
    },
    emits: ['saved'],
    data () {
        return {
            detailsAddForm: null as null | FormGridType,
            detailsFormList: [] as FormGridType[],
            verifyForm: {} as VerifyForm,
        }
    },
    computed: {
        kycSubmitButton (): FormElement {
            return this.verifyForm.blocks?.flat().find((f: FormElement) => (f as {action: string} & FormElement)?.action === 'submit') || {} as FormElement
        },
    },
    watch: {
        user (user: User) {
            this.onVerificationLoad(user)
        },
    },
    beforeMount () {
        this.onVerificationLoad(this.user)
    },
    methods: {
        async onVerificationLoad (user: User) {
            this.verifyForm = await this.$fetch(`${KYC_URL}/kyc-form`, {
                ...user,
                docRequestTransactions: this.transactionIds,
            })
            this.onVerificationFormLoaded()
            if (this.verifyForm.changeImmediately) {
                Object.assign(this.verifyForm.values, this.verifyForm.changeImmediately)
                this.onVerificationChange(this.verifyForm.values)
            }
        },
        onVerificationSubmit () {
            this.$processFormResponse(this.$fetch(`${KYC_URL}/kyc-submit`, {
                ...this.verifyForm.values,
                details: this.detailsFormList.map((form: FormGridType) => form.values),
                docRequestTransactions: this.transactionIds,
            }), this.verifyForm, this.passDetailsErrors).then((data: VerifyForm) => {
                this.verifyForm = data
                this.onVerificationFormLoaded()
                this.$emit('saved')
            })
        },
        onVerificationChange (values: Values) {
            this.verifyForm.errors = {}
            this.verifyForm.values = values
            this.verifyForm.enabled = false
            this.kycSubmitButton.enabled = false
            this
                .$fetch(`${KYC_URL}/kyc-details-defaults`, {
                    ...this.user,
                    status: this.verifyForm.values.status,
                    transactionIds: this.transactionIds,
                })
                .then((response: DetailsFormsDefaultsResponse) => {
                    this.detailsFormList = response.formsFilled || []
                    this.detailsAddForm = response.formAdd || null
                })
                .finally(() => {
                    this.setKycSubmitEnabledByStatus()
                    this.verifyForm.enabled = true
                })
        },
        setKycSubmitEnabledByStatus () {
            this.kycSubmitButton.enabled = this.verifyForm.values.status !== this.verifyForm.statusInDb
        },
        passDetailsErrors (data: VerifyForm) {
            if (data.errors?.details) {
                (JSON.parse(data.errors.details) as Errors[]).forEach((errors, i) => this.$setForm(this.detailsFormList[i], {errors}))
                delete data.errors?.details
            }
            this.$setForm(this.verifyForm, data)
        },
        onVerificationFormLoaded () {
            this.detailsAddForm = null
            this.detailsFormList = []
            this.setKycSubmitEnabledByStatus()
        },
        onDetailsAdd (params: Values) {
            this.verifyForm.enabled = false
            this.$fetch(`${KYC_URL}/kyc-details-form`, {
                ...params,
                ...this.user,
            }).then((formResp: FormGridType[]) => {
                formResp.forEach(form => this.detailsFormList.push(form))
            }).finally(() => {
                this.verifyForm.enabled = true
                this.detailsAddForm!.values = {}
            });
        },
        onDetailsAddPaste (input: HTMLInputElement) {
            // setTimeout to blur(focusout) after paste => focusout to trigger change => on change submit
            setTimeout(() => input.blur(), 100)
        },
        onDetailsChange (formIndex: number, values: Values) {
            this.detailsFormList[formIndex].values = values
        },
        onDetailsDelete (formIndex: number) {
            this.detailsFormList.splice(formIndex, 1);
        },
    },
})
</script>
<style lang="scss">
.kyc-status-form label {
    z-index: 0 !important;
}
</style>
