export function submitFormData (action: string, form: HTMLFormElement, values: Record<string, null | undefined | string | string[] | boolean | number | unknown>) {
    let input
    for (const [filterKey, filterVal] of Object.entries(values)) {
        if (filterVal === null || filterVal === undefined) {
            continue
        }

        if (Array.isArray(filterVal)) {
            for (const inputVal of filterVal) {
                input = document.createElement('input')
                input.setAttribute('value', inputVal)
                input.setAttribute('name', `${filterKey}[]`)

                form.appendChild(input)
            }
        } else {
            if ((typeof filterVal === 'string') && filterVal.includes('\n')) {
                input = document.createElement('textarea')
                input.innerHTML = filterVal
            } else {
                input = document.createElement('input')
                input.setAttribute('value', (typeof filterVal === 'boolean' ? (filterVal ? 1 : 0) : filterVal).toString())
            }

            input.setAttribute('name', filterKey)

            form.appendChild(input)
        }
    }

    form.setAttribute('action', action)
    form.submit()
    form.innerHTML = ''
}
