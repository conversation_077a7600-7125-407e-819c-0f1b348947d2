<template>
    <div>
        <div class="container">
            <Breadcrumbs
                @helpLoaded="onHelpLoaded"
            >
                <template #afterHelp="{}">
                    <li>
                        <button
                            type="button"
                            class="ms-1 btn btn-secondary"
                            title="Reset default values"
                            :disabled="!!inProcess"
                            @click="onResetParams"
                        >
                            <Icona name="icn-refresh" />
                        </button>
                    </li>
                    <ReportPresets
                        :presets="presets"
                        :current="lastLoadedPresetName"
                        @select="onPresetSelect"
                        @delete="onPresetDelete"
                        @save="onPresetSave"
                    />
                </template>

                <template #footer="{}">
                    <button
                        class="btn btn-secondary ms-1"
                        @click="hintsEditOpened = !hintsEditOpened"
                    >
                        <template v-if="hintsEditOpened">
                            <Icona name="icn-undo" /> Back to view mode
                        </template>
                        <template v-else>
                            <Icona name="icn-question" /> Edit hints
                        </template>
                    </button>
                </template>
            </Breadcrumbs>
        </div>

        <div class="container">
            <div class="card card-primary">
                <div class="card-body row">
                    <div class="col-md-6">
                        <FormParams
                            :inputsSets="inputsSets"
                            :formValues="flatFormValues"
                            :errors="errors"
                            :help="help.elements"
                            :disabled="!!inProcess"
                            :hintsEditOpened="hintsEditOpened"
                            :sortableActiveSet="sortableActiveSet"
                            dragAndDrop
                            @paramClick="onParamClick"
                            @hintSubmit="onHintSubmit"
                            @sortableStart="onParamsSortableStart"
                            @sortableEnd="onSortableEnd"
                        />
                    </div>
                    <div class="col-md-6">
                        <FormList
                            ref="list"
                            class="mt-1"
                            :formBlocks="formBlocks"
                            :inputsByName="inputsByName"
                            :disabled="!!inProcess"
                            :sortableActiveSet="sortableActiveSet"
                            :focusOnFormValue="focusOnFilter"
                            dragAndDrop
                            @submit="onSubmit"
                            @change="onChange"
                            @changeOperator="onChangeOperator"
                            @delete="onDelete"
                            @sortableStart="onListSortableStart"
                            @sortableAdd="onListSortableAdd"
                            @sortableUpdate="onListSortableUpdate"
                            @sortableEnd="onSortableEnd"
                        />
                    </div>
                </div>
            </div>
        </div>

        <div class="container mt-2">
            <div class="card card-primary">
                <div class="card-body">
                    <div
                        v-if="columns.items?.length"
                        class="input-group my-1 w-100"
                    >
                        <label class="input-group-text">Columns</label>
                        <Dropdown
                            :enabled="!inProcess"
                            :isInvalid="'columns' in errors"
                            :value="resultConfig.columns"
                            :allowToggleAll="false"
                            v-bind="columns"
                            @input="resultConfig.columns = $event as string[]"
                        />

                        <FormError :error="errors.columns" />

                        <TimerButton
                            :isInProcess="inProcess === 1"
                            @click="onCancelReport"
                        />

                        <button
                            v-if="!inProcess"
                            type="button"
                            class="btn btn-primary ms-auto"
                            @click="onSubmit()"
                        >
                            <Icona name="icn-table-shortcut" /> Search
                        </button>

                        <button
                            type="button"
                            class="btn btn-success"
                            :disabled="!!inProcess"
                            @click="onDownload()"
                        >
                            <Icona name="icn-download" /> CSV
                        </button>

                        <button
                            v-if="showSql"
                            type="button"
                            class="btn btn-warning"
                            :disabled="!!inProcess"
                            @click="onShowSql()"
                        >
                            <Icona name="icn-database" /> SQL
                        </button>
                    </div>

                    <div v-if="'items' in metrics && metrics.items.length || 'items' in groups && groups.items.length" class="report-buttons-container">
                        <div class="input-group">
                            <label class="input-group-text">Metrics</label>
                            <Dropdown
                                :enabled="!inProcess"
                                :isInvalid="'metrics' in errors"
                                :value="resultConfig.metrics"
                                :allowToggleAll="false"
                                v-bind="metrics"
                                @input="resultConfig.metrics = $event as string[]"
                            />
                            <FormError :error="errors.metrics" />
                        </div>
                        <div class="input-group">
                            <span />
                            <label class="input-group-text">Block</label>
                            <Dropdown
                                :enabled="!inProcess"
                                :is-invalid="'block' in errors"
                                :value="resultConfig.block"
                                v-bind="Object.assign({}, groups, {multiple: false})"
                                @input="resultConfig.block = $event as string"
                            />
                            <FormError :error="errors.block" />
                        </div>
                        <div class="input-group">
                            <span />
                            <label class="input-group-text">Group</label>
                            <Dropdown
                                :enabled="!inProcess"
                                :is-invalid="'groups' in errors"
                                :value="resultConfig.groups"
                                :allow-toggle-all="false"
                                v-bind="groups"
                                @input="resultConfig.groups = $event as string[]"
                            />
                            <FormError :error="errors.groups" />
                        </div>
                        <div class="input-group">
                            <span />
                            <label class="input-group-text">Split</label>
                            <Dropdown
                                :enabled="!inProcess"
                                :is-invalid="'split' in errors"
                                :value="resultConfig.split"
                                v-bind="Object.assign({}, groups, {multiple: false})"
                                @input="resultConfig.split = $event as string"
                            />
                            <FormError :error="errors.split" />
                        </div>
                        <div class="input-group">
                            <span />
                            <TimerButton
                                :is-in-process="inProcess === 2"
                                @click="onCancelReport"
                            />

                            <button
                                v-if="!inProcess"
                                type="button"
                                class="btn btn-primary"
                                @click="onSubmit(true)"
                            >
                                <Icona name="icn-table-shortcut" /> Totals
                            </button>

                            <button
                                type="button"
                                class="btn btn-success"
                                :disabled="!!inProcess"
                                @click="onDownload(true)"
                            >
                                <Icona name="icn-download" /> CSV
                            </button>

                            <button
                                type="button"
                                class="btn btn-info"
                                :disabled="!!inProcess"
                                @click="onDrawChart"
                            >
                                <Icona name="icn-chart" /> Draw
                            </button>

                            <button
                                v-if="showSql"
                                type="button"
                                class="btn btn-warning"
                                :disabled="!!inProcess"
                                @click="onShowSql(true)"
                            >
                                <Icona name="icn-database" /> SQL
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template v-if="results.length">
            <div
                v-if="results.find(r => r.type === 'chart')"
                class="container mt-2"
            >
                <div class="card card-primary">
                    <div class="card-header">
                        Chart options
                    </div>
                    <div class="card-body row">
                        <div class="col-sm-6">
                            <BtnGroup
                                :items="chartTypes"
                                :value="[resultConfig.chartType]"
                                :multiple="false"
                                prefix="Chart type"
                                @change="onChartTypeChange"
                            />
                        </div>
                        <div class="col-sm-6">
                            <BtnGroup
                                :items="chartStackings"
                                :value="[resultConfig.chartStacking]"
                                :multiple="false"
                                prefix="Stacking"
                                @change="onChartStackingChange"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <template v-for="(result, _i) in results" :key="_i">
                <div class="container-fluid mt-2">
                    <div class="card card-primary">
                        <div class="card-body" style="overflow-x: auto;">
                            <h3
                                v-if="result.title"
                                class="text-center"
                            >
                                {{ result.title }}
                            </h3>
                            <EntityTable
                                v-if="result.type === 'table'"
                                v-bind="result.table"
                                sticky
                                :enable-inner-sort="true"
                                @sortChanged="result.table ? result.table.sort = $event : null"
                            />
                            <template v-else-if="result.type === 'chart'">
                                <div :id="result.chartId" />
                            </template>
                            <template v-else-if="result.type === 'text'">
                                <pre style="white-space: pre-line;">{{ result.text }}</pre>
                            </template>
                            <template v-else>
                                {{ result }}
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </template>

        <form
            ref="form"
            method="post"
            style="display: none;"
        />
    </div>
</template>

<script lang="ts">
import { FormList, FormParams, Dropdown, FormError, EntityTable, HighchartsLoader, BtnGroup, Icona } from '@/components'
import { Breadcrumbs } from '@/widgets'
import { submitFormData } from './utils'
import ReportPresets from './presets.vue'
import TimerButton from './timerButton.vue'
import { defineComponent } from 'vue'
import { DropdownType, FormInput, FormInputsSet, FormValue, FormValueIndex, FormBlock, FormValueWithBlock, Help, SortableFormEvent, TableType, Value, Values } from '@/types'
import { RouteLocationNormalized } from 'vue-router'
import { $emptyListParamWithKey, $toggleFormValue, $emptyFormValuesBlock, $emptyFormValue } from '@/utils/form-list-utils'
import { Chart, Options, OptionsStackingValue } from 'highcharts'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'
import { cloneDeep, pick, pickBy, toInteger } from 'lodash'

const resultConfigKeyNames: (keyof ReportResultConfig)[] = ['columns', 'metrics', 'groups', 'split', 'block', 'chartType', 'chartStacking']
const resultConfigForBack: (keyof ReportResultConfig)[] = ['columns', 'metrics', 'groups', 'split', 'block']

const emptyReportResultConfig: ReportResultConfig = { // Default params before load from route
    columns: [],
    metrics: [],
    groups: [],
    split: undefined,
    block: undefined,
    chartType: 'column',
    chartStacking: 'none',
}

let charts = [] as Chart[] // Array of all HighChart instances

interface ReportConfigResponse {
    title: string
    inputs: FormInputsSet[]
    columns: DropdownType
    metrics: DropdownType
    groups: DropdownType
    showSql: boolean
    defaultParams: [keyof ReportResultConfig | string, Value, string | undefined][] // filterName, filterValues, filterOperator
}

interface ReportResultConfig {
    columns: string[]
    metrics: string[]
    groups: string[]
    split?: string
    block?: string
    chartType: string
    chartStacking: string
}

export interface ReportPreset {
    name?: string,
    paramsList: DirtyFormValue[],
    namedParams: ReportResultConfig
}

interface ReportResult {
    type: 'table' | 'chart' | 'text',
    title?: string
    table?: TableType
    chart?: unknown
    chartId?: string
    text?: string
}

interface ReportResults {
    results: ReportResult[]
    errors?: Record<string, string>
}

interface DirtyFormValue {
    name: string,
    value?: Value,
    orBlock?: number | string,
    operator?: string,
}

export default defineComponent({
    components: {
        Icona,
        BtnGroup,
        FormList,
        FormParams,
        FormError,
        Breadcrumbs,
        Dropdown,
        EntityTable,
        ReportPresets,
        TimerButton,
    },
    beforeRouteEnter (to, _from, next: (nextHandler: (vm: { processRouteInit: (to: RouteLocationNormalized) => Promise<unknown> }) => void) => void) {
        next(vm => vm.processRouteInit(to))
    },
    beforeRouteUpdate (to, from) {
        if (from.path !== to.path) {
            this.processRouteInit(to)
        }
    },
    beforeRouteLeave() {
        this.titleBreadcrumbs.setTitle('')
    },
    props: {
        reportId: {
            type: String,
            required: true,
        },
    },
    setup() {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
        }
    },
    data () {
        return {
            // From backend
            inputsSets: [] as FormInputsSet[],
            columns: {} as DropdownType,
            metrics: {} as DropdownType,
            groups: {} as DropdownType,
            showSql: false,
            results: [] as ReportResult[],
            errors: {} as Record<string, string>,
            help: {} as Help,
            reportUniqueId: undefined as string | undefined,

            // Local
            formBlocks: [] as FormBlock[],
            resultConfig: {} as ReportResultConfig,
            sortableActiveSet: undefined as undefined | number,
            focusOnFilter: undefined as undefined | FormValue,
            inProcess: 0 as 0 | 1 | 2,
            hintsEditOpened: false,
            presets: [] as ReportPreset[],
            defaultPreset: {} as ReportPreset,
            lastLoadedPresetName: '',

            chartStackings: [
                { id: 'none', name: 'None' },
                { id: 'normal', name: 'Normal' },
                { id: 'percent', name: 'Percent' },
            ],
            chartTypes: [
                { id: 'column', name: 'Column' },
                { id: 'areaspline', name: 'Area' },
                { id: 'spline', name: 'Line' },
                { id: 'area', name: 'Area (rough)' },
                { id: 'line', name: 'Line (rough)' },
                { id: 'pie', name: 'Pie' },
                { id: 'bar', name: 'Bar' },
            ],
        }
    },
    computed: {
        flatFormValues (): FormValue[] {
            return this.formBlocks.map(b => b.blockSets.map(s => s.values).flat()).flat()
        },
        inputsByName () {
            const inputs = {} as Record<string, FormInput>
            this.inputsSets.forEach(set => set.inputs.forEach(f => {
                inputs[f.name] = f
            }))
            return inputs
        },
        inputNamesToBlockSetIndex () {
            const inputNamesToSetIndex: Record<string, number> = {}
            this.inputsSets.forEach((set, setIndex) => set.inputs.forEach(input => {
                inputNamesToSetIndex[input.name] = setIndex
            }))
            return inputNamesToSetIndex
        },
    },
    watch: {
        formBlocks: {
            handler (formBlocks) {
                this.pushUrl(formBlocks, this.resultConfig)
            },
            deep: true,
        },
        resultConfig: {
            handler (resultConfig) {
                this.pushUrl(this.formBlocks, resultConfig)
            },
            deep: true,
        },
    },

    methods: {
        async processRouteInit (to: RouteLocationNormalized) {
            return this.loadConfig(to)
                .then(() => {
                    switch (to.hash) {
                        case '#submit':
                            this.onSubmit()
                            break
                        case '#totals':
                            this.onSubmit(true)
                            break
                        case '#draw':
                            this.onDrawChart()
                            break
                    }
                })
        },
        uniqid () {
            return '_' + Math.random().toString(36).substring(2, 11)
        },
        async loadConfig (toRoute: RouteLocationNormalized) {
            return this.$fetch<ReportConfigResponse>(this.backUrl('config', toRoute.params.reportId as string)).then((data: ReportConfigResponse) => {
                this.titleBreadcrumbs.setTitle(data.title)
                this.inputsSets = data.inputs;

                (['columns', 'metrics', 'groups'] as const).forEach(p => {
                    this[p] = data[p] || {}
                })

                this.showSql = data.showSql
                this.reportUniqueId = this.uniqid()
                this.clearResults()

                this.setDefaultPreset(data)
                this.loadParamsFromRoute(toRoute)
                this.applyNewOperators(this.flatFormValues, true)
                this.loadPresets()
            })
        },
        applyNewOperators (formValues: FormValue[] | DirtyFormValue[], flushLinkUpdatedMessage = false) {
            return formValues.filter(value => {
                const input = this.inputsByName[value.name]
                if (!value.operator && input?.operators?.length) {
                    const firstOperator = input?.operators[0]
                    value.operator = firstOperator.id as string
                    if (flushLinkUpdatedMessage) {
                        this.$notify({
                            type: 'info',
                            message: `Default operator "${firstOperator.name}" applied for filter "${input.title}". Link updated. Everything ok`,
                        })
                    }
                    return true
                }
            }).length > 0
        },
        urlParamsToFiltersWithBlockId (params: Values): FormValueWithBlock[] {
            const keys = Object.keys(params)
                .map(k => {
                    const m = k.match(/f(\d+)n/)
                    return m ? parseInt(m[1], 10) : null
                })
                .filter(k => k !== null)
                .sort() as number[]

            const key = (index: number, type: string): `f${number}${string}` => `f${index}${type}`
            return this.dirtyToFormFilters(keys.map(index => ({
                name: params[key(index, 'n')],
                value: params[key(index, 'v')],
                orBlock: params[key(index, 'b')],
                operator: params[key(index, 'o')],
            } as DirtyFormValue)))
        },
        dirtyToFormFilters (params: DirtyFormValue[]): FormValueWithBlock[] {
            return params
                .map(p => ({
                    value: undefined,
                    operator: undefined,
                    ...$emptyListParamWithKey(),
                    ...p,
                    orBlock: toInteger(p.orBlock),
                }))
                .sort((a, b) => a.orBlock - b.orBlock)
        },
        flatFiltersToBlocks (paramsObjects: FormValueWithBlock[]): FormBlock[] {
            const blockIds = [...paramsObjects.reduce((s, actual) => s.add(actual.orBlock), new Set<number>([0]))].sort()
            blockIds.push(Math.max(...blockIds) + 1)
            const blocks: Record<number, FormBlock> = Object.fromEntries(blockIds.map(id => [id, $emptyFormValuesBlock(this.inputsSets) as FormBlock]))

            paramsObjects.forEach(param => {
                if (!(param.name in this.inputNamesToBlockSetIndex)) {
                    // eslint-disable-next-line no-console
                    console.log(`Filter "${param.name}" unavailable and was removed!`)
                    return
                }
                const setIndex = this.inputNamesToBlockSetIndex[param.name]
                blocks[param.orBlock].blockSets[setIndex].values.push(pickBy(param, (_, k) => k !== 'orBlock') as FormValue)
            })

            return blockIds.map(blockId => blocks[blockId])
        },
        blocksToUrlParams (blocks: FormBlock[]): Values {
            let index = 0
            const result: Values = {}
            const key = (index: number, type: string): `f${number}${string}` => `f${index}${type}`

            blocks.forEach((b, blockIndex) => b.blockSets.forEach(s => s.values.forEach(formValue => {
                result[key(index, 'n')] = formValue.name
                result[key(index, 'v')] = formValue.value

                if (formValue?.operator !== undefined) {
                    result[key(index, 'o')] = formValue.operator
                }

                if (blockIndex > 0) {
                    result[key(index, 'b')] = blockIndex
                }
                index++
            })))

            return result
        },
        setDefaultPreset (data: ReportConfigResponse) {
            const newPreset = {
                paramsList: [],
                namedParams: Object.assign({}, emptyReportResultConfig),
            } as ReportPreset

            if (!data.defaultParams) {
                return
            }

            data.defaultParams.forEach(([paramName, paramValue, paramOperator]) => {
                if (resultConfigKeyNames.includes(paramName as keyof ReportResultConfig)) {
                    Object.assign(newPreset.namedParams, {[paramName]: paramValue})
                } else {
                    const newParam = {
                        name: paramName,
                        value: paramValue,
                    } as FormValue

                    if (paramOperator) {
                        newParam.operator = paramOperator
                    }

                    newPreset.paramsList.push(newParam)
                }
            })

            this.defaultPreset = newPreset
        },
        loadParamsFromRoute (toRoute: RouteLocationNormalized) {
            const routeParams = this.$decodeParams(toRoute.query)

            if (!Object.keys(routeParams).length) {
                if (this.defaultPreset.paramsList.length > 0 || Object.keys(this.defaultPreset.namedParams).length > 0) {
                    this.onResetParams()
                }
                return false
            }

            this.formBlocks = this.flatFiltersToBlocks(this.urlParamsToFiltersWithBlockId(routeParams))
            this.resultConfig = Object.assign({}, emptyReportResultConfig, pick(routeParams, resultConfigKeyNames))
        },
        backUrl (postfix: string, reportId?: string) {
            if (reportId === undefined) {
                reportId = this.reportId
            }

            return `/reports/${reportId}/${postfix}`
        },
        pushUrl (blocks: FormBlock[], resultConfig: ReportResultConfig) {
            const obj = Object.assign(this.blocksToUrlParams(blocks), resultConfig)

            Object.entries(emptyReportResultConfig).forEach(([k, v]) => {
                if (obj[k] === v) {
                    delete obj[k]
                }
            })
            this.$router.replace({ path: this.$route.path, query: this.$normalizeParamsObj(obj, false), hash: this.$route.hash })
        },
        collectSubmitParams () {
            const urlParams = this.blocksToUrlParams(this.formBlocks)
            const resultConfig = {} as Record<keyof ReportResultConfig, Value>
            resultConfigForBack.forEach(k => {
                resultConfig[k] = this.resultConfig[k]
            })

            return Object.assign({ reportUniqueId: this.reportUniqueId }, urlParams, resultConfig)
        },
        onParamClick (input: FormInput) {
            const setIndex = this.inputNamesToBlockSetIndex[input.name]
            this.focusOnFilter = $toggleFormValue(input, this.formBlocks[0].blockSets[setIndex])
        },
        onListSortableStart (event: SortableFormEvent) {
            this.onParamsSortableStart(event.from.setIndex)
        },
        onParamsSortableStart (setIndex: number) {
            this.sortableActiveSet = setIndex
        },
        onSortableEnd () {
            this.sortableActiveSet = undefined
        },
        onListSortableAdd (event: SortableFormEvent) {
            const toFilters = this.formBlocks[event.to.blockIndex].blockSets[event.to.setIndex].values
            const emptyFilter = $emptyFormValue(this.inputsByName[event.valueName])
            toFilters.splice(event.to.valueIndex, 0, emptyFilter)
            this.ensureLastBlockEmpty()
            this.focusOnFilter = emptyFilter
        },
        onListSortableUpdate (event: SortableFormEvent) {
            const blockSetFrom = this.formBlocks[event.from.blockIndex].blockSets[event.from.setIndex].values
            const blockSetTo = this.formBlocks[event.to.blockIndex].blockSets[event.to.setIndex].values
            blockSetTo.splice(event.to.valueIndex, 0, ...blockSetFrom.splice(event.from.valueIndex, 1))
            // fix caching value position by vue
            blockSetTo[event.to.valueIndex].key = Symbol('')
            this.removeEmptyMiddleBlocks()
            this.ensureLastBlockEmpty()
        },
        onChange ({ valueIndex, value }: { valueIndex: FormValueIndex, value: Value }) {
            this.formBlocks[valueIndex.blockIndex].blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].value = value
        },
        onChangeOperator ({ valueIndex, operator }: { valueIndex: FormValueIndex, operator: string }) {
            this.formBlocks[valueIndex.blockIndex].blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].operator = operator
        },
        onDelete (valueIndex: FormValueIndex) {
            this.formBlocks[valueIndex.blockIndex].blockSets[valueIndex.setIndex].values.splice(valueIndex.valueIndex, 1)
            this.removeEmptyMiddleBlocks()
        },
        removeEmptyMiddleBlocks () {
            const lastBlockIndex = this.formBlocks.length - 1
            const emptyBlockIndexes = this.formBlocks.map((block, index) => {
                return index === lastBlockIndex || block.blockSets.filter(s => s.values.length > 0).length > 0 ? 0 : index
            }).filter(i => i !== 0).reverse()
            emptyBlockIndexes.forEach((blockIndex) => this.formBlocks.splice(blockIndex, 1))
        },
        ensureLastBlockEmpty () {
            if (this.formBlocks[this.formBlocks.length - 1].blockSets.filter(s => s.values.length > 0).length) {
                this.formBlocks.push($emptyFormValuesBlock(this.inputsSets))
            }
        },
        onSubmit (isTotals = false) {
            this.submit({ isTotals })
        },
        onShowSql (isTotals = false) {
            this.submit({ isTotals, showSql: true })
        },
        submit (additionalParams: Values) {
            this.clearResults()
            this.inProcess = additionalParams.isTotals ? 2 : 1
            this.$fetch<ReportResults>(this.backUrl('data'), Object.assign({}, additionalParams, this.collectSubmitParams()))
                .then(data => {
                    data.results.forEach((r, i) => {
                        switch (r.type) {
                            case 'chart':
                                 
                                const id = `chart-container-${i}`
                                 
                                const chartConfig = r.chart as Options

                                this.setChartTypeAndStacking(chartConfig)

                                HighchartsLoader().then(({ default: Highcharts }) => {
                                    this.$nextTick(() => {
                                        charts.push(Highcharts.chart(id, chartConfig))
                                    })
                                })
                                data.results[i].chartId = id
                                delete data.results[i].chart
                                break
                        }
                    })
                    this.results = data.results
                })
                .catch(resp => {
                    if (resp.status !== 422) {
                        return
                    }

                    return resp.json().then((data: ReportResults) => {
                        this.errors = data.errors || {}
                        this.flatFormValues.forEach((formValue, index) => {
                            formValue.error = this.errors[`f${index}e`] || this.errors[formValue.name]
                        })
                    })
                })
                .finally(() => {
                    this.inProcess = 0
                })
        },
        setChartTypeAndStacking (chartConfig: Options) {
            Object.assign(chartConfig, {
                chart: {
                    type: this.resultConfig.chartType,
                },
                plotOptions: {
                    series: {
                        marker: {
                            enabled: false,
                        },
                        stacking: this.resultConfig.chartStacking === 'none' ? undefined : this.resultConfig.chartStacking,
                    },
                },
                time: {
                    useUTC: true,
                },
            })
        },
        onChartTypeChange (types: string[]) {
            this.resultConfig.chartType = types[0]
            charts.forEach(chart => {
                chart.update({
                    chart: {
                        type: this.resultConfig.chartType,
                    },
                })
            })
        },
        onChartStackingChange (stackings: string[]) {
            this.resultConfig.chartStacking = stackings[0]
            charts.forEach(chart => {
                chart.update({
                    plotOptions: {
                        series: {
                            marker: {
                                enabled: false,
                            },
                            stacking: this.resultConfig.chartStacking === 'none' ? undefined : this.resultConfig.chartStacking as OptionsStackingValue,
                        },
                    },
                })
            })
        },
        onDownload (isTotals = false) {
            this.clearResults()
            submitFormData(this.apiUrl + this.backUrl('csv'), this.$refs.form as HTMLFormElement, Object.assign({ isTotals }, this.collectSubmitParams()))
        },
        onDrawChart () {
            this.submit({ isChart: true, isTotals: true })
        },
        onResetParams () {
            this.lastLoadedPresetName = ''
            this.applyPreset(this.defaultPreset)
        },
        onPresetSelect (preset: ReportPreset) {
            this.lastLoadedPresetName = preset.name || ''
            this.applyPreset(preset)
        },
        applyPreset (preset: ReportPreset) {
            this.clearErrors()
            const formValues = preset.paramsList.map(p => Object.assign({}, p))

            if (this.applyNewOperators(formValues)) {
                Object.assign(this.presets.find(p => p.name = preset.name) || {}, {paramsList: formValues})
                this.savePresets()
            }

            this.resultConfig = cloneDeep(pick(preset.namedParams, resultConfigKeyNames)) as ReportResultConfig
            this.formBlocks = this.flatFiltersToBlocks(this.dirtyToFormFilters(formValues as DirtyFormValue[]))
            this.ensureLastBlockEmpty()
        },
        onPresetDelete (index: number) {
            this.lastLoadedPresetName = ''
            this.presets.splice(index, 1)
            this.savePresets()
        },
        onPresetSave () {
            let name
            do {
                name = prompt('Preset name (1-50 chars)', this.lastLoadedPresetName)
                if (name === null) {
                    return
                }
            } while (!(name.length > 0 && name.length < 50))

            const savedIndex = this.presets.map(p => p.name).indexOf(name)

            if (savedIndex !== -1) {
                if (!confirm('Update already saved preset?')) {
                    return
                }
                this.presets.splice(savedIndex, 1)
            }

            this.presets.push({
                name,
                paramsList: this.formBlocks.map((b, orBlock) => b.blockSets.map(s => s.values.map(f => {
                    f = Object.assign({orBlock}, f)
                    delete f.error
                    return f
                })).flat()).flat(),
                namedParams: cloneDeep(pickBy(this.resultConfig as ReportResultConfig, (v, k) => {
                    return v !== undefined && resultConfigKeyNames.includes(k as keyof ReportResultConfig)
                })) as ReportResultConfig,
            })

            this.savePresets()
        },
        savePresets () {
            const storageKey = 'report-presets-' + this.reportId
            localStorage.setItem(storageKey, JSON.stringify(this.presets))
        },

        loadPresets () {
            const storageKey = 'report-presets-' + this.reportId
            const savedPresets = localStorage.getItem(storageKey) as string | null

            if (savedPresets === null) {
                this.presets = []
                return
            }

            this.presets = JSON.parse(savedPresets)
        },
        clearResults () {
            this.clearErrors();

            // Deleting old charts
            (charts || []).forEach(chart => {
                chart.destroy()
            })
            charts = []

            this.results = []
        },
        clearErrors () {
            this.errors = {}
            this.flatFormValues.forEach(value => {
                value.error = undefined
            });
        },
        onHintSubmit (name: string, value: string) {
            this.$fetch('/back/help/save-elements', { url: this.help.url, name, value }).then(() => {
                this.help.elements[name] = value
            })
        },
        onCancelReport () {
            this.$fetch(this.backUrl('kill', this.reportId), { reportUniqueId: this.reportUniqueId })
        },
        onHelpLoaded (help: Help) {
            this.help = help
        },
    },
})
</script>
<style lang="scss">
.report-buttons-container {
    display: flex;
    gap: 0.5rem;

    .input-group {
        max-width: unset;
        flex: 0 0 250px; // Fixed width for all input groups
        min-width: 250px;

        &:last-child {
            flex: 0 1 auto; // Allow last element to shrink but not grow
            min-width: fit-content; // Minimum width to fit content
            max-width: none;
        }
    }
}
</style>
