<template>
    <div>
        <div class="row">
            <div class="col-sm-10">
                <FormGrid
                    v-bind="panel.form"
                    @change="onPanelChange"
                />
            </div>
            <div class="col-sm-2">
                <label>&nbsp;</label><br>
                <button
                    type="button"
                    class="btn btn-danger"
                    @click="onPanelDelete(panel.id)"
                >
                    <Icona name="icn-delete" /> Delete
                </button>
            </div>
        </div>

        <table class="charts-panel">
            <tr v-for="(cols, row) in panel.layout" :key="row">
                <td
                    v-for="(chart, col) in cols"
                    :key="col"
                    class="align-top"
                    :style="{width: Math.floor(100 / panel.form.values.cols * chart.col_span) + '%'}"
                    :rowspan="Math.max(chart.row_span, 1)"
                    :colspan="Math.max(chart.col_span, 1)"
                >
                    <div
                        class="btn btn-lg d-block"
                        :draggable="!draggedChart.inProcess"
                        :class="getChartSelectorClasses(row, col, chart)"
                        @click.prevent="onChartSelect({row, col})"
                        @dragstart="onDragStart($event, row, col, chart)"
                        @dragover.prevent
                        @drop.prevent="onDrop($event, row, col, chart)"
                        @dragend.prevent="onDragEnd"
                    >
                        {{ chart.name || '(empty)' }}
                    </div>
                </td>
            </tr>
        </table>
        <div v-if="selectedChart.row !== null">
            <template v-if="panel.layout[selectedChart.row][selectedChart.col].name === null">
                <FormGrid
                    v-bind="chartAddForm"
                    @change="chartAddForm.values = $event"
                    @submit="onChartAdd"
                />
            </template>
            <template v-else>
                <div class="card mb-4">
                    <div class="card-body">
                        <h3>
                            Chart
                            <button
                                type="button"
                                class="btn btn-danger btn-sm"
                                @click="onChartDelete"
                            >
                                <Icona name="icn-delete" /> Delete
                            </button>
                        </h3>
                        <FormGrid
                            v-bind="chartForm"
                            @change="onChartChange"
                        />
                        <h3>Series</h3>
                        <Tabs
                            v-model:active-tab-index="activeTab"
                            :tabs="tabs"
                            @on-activate-tab="processSqlView(!tabs[activeTab].isAddSerie && sql !== false)"
                        >
                            <template #default="{tab}">
                                <template v-if="tab.isAddSerie">
                                    <FormGrid
                                        v-if="addSerieForm"
                                        v-bind="addSerieForm"
                                        @change="addSerieForm.values = $event"
                                        @submit="onChartNewSerieAdd"
                                    />
                                </template>
                                <template v-else>
                                    <FormGrid
                                        v-if="tab.serieForm"
                                        v-bind="tab.serieForm"
                                        @change="onChartSerieChange"
                                    />
                                    <div class="card-body row px-0">
                                        <div class="col-md-6">
                                            <FormParams
                                                :errors="tab.errors || []"
                                                :disabled="!!inProcess"
                                                :formValues="tab.flatFormValues"
                                                :inputsSets="tab.inputsSets"
                                                @paramClick="onParamClick"
                                            />
                                        </div>
                                        <div class="col-md-6">
                                            <FormList
                                                ref="list"
                                                class="mt-1"
                                                :disabled="!!inProcess"
                                                :focusOnFormValue="focusOnFilter"
                                                :formBlocks="[tab.formValuesBlock]"
                                                :inputsByName="tab.inputsByName"
                                                @change="onChange"
                                                @changeOperator="onChangeOperator"
                                                @delete="onDelete"
                                            />
                                        </div>
                                    </div>
                                    <div class="input-group">
                                        <label class="input-group-text">Metric</label>
                                        <Dropdown
                                            :enabled="!inProcess"
                                            :isInvalid="'metric' in (tab.errors || {})"
                                            :value="tab.metric"
                                            :allow-toggle-all="false"
                                            v-bind="tab.metricDropdown"
                                            @input="onMetricChanged($event)"
                                        />
                                        <button
                                            type="button"
                                            class="btn btn-danger"
                                            :disabled="inProcess"
                                            @click="onChartSerieDelete()"
                                        >
                                            <Icona name="icn-delete" /> Delete serie
                                        </button>
                                        <button
                                            v-if="!!panel.showSqlButton && !hasError"
                                            type="button"
                                            class="btn btn-outline-warning"
                                            :disabled="inProcess"
                                            @click="processSqlView(sql === false)"
                                        >
                                            <Icona name="icn-database" /> SQL
                                        </button>
                                        <button
                                            type="button"
                                            class="btn btn-success"
                                            :disabled="inProcess"
                                            @click="onChartSerieClone()"
                                        >
                                            <Icona name="icn-copy" /> Clone serie
                                        </button>
                                    </div>
                                </template>
                            </template>
                        </Tabs>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <template v-if="sql && !hasError">
                            <h3>SQL</h3>
                            <pre
                                class="p-3"
                                style="white-space: pre-line;"
                            >{{ sql }}</pre>
                        </template>
                        <h3>Preview</h3>
                        <div
                            id="panels-chart-highchart-preview"
                            style="min-height: 500px;"
                        />
                    </div>
                </div>
            </template>
        </div>
        <p
            v-if="selectedChart.row === null"
            class="font-italic text-center"
        >
            Select chart to edit &uarr; or drag to rearrange
        </p>
    </div>
</template>

<script lang="ts">

import { FormList, FormParams, FormGrid, HighchartsLoader, Tabs, Dropdown, Icona } from '@/components'
import { defineComponent } from 'vue'
import { $emptyFormValuesBlock, $fromValuesToUrlParams, $toggleFormValue } from '@/utils/form-list-utils'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'
import type {
    FormGridType,
    FormBlock,
    FormInput,
    FormInputsSet,
    FormValue,
    FormValueIndex,
    Item,
    Values,
    Value,
    Errors
} from '@/types'

// Chart-specific interfaces
interface ChartLayout {
    name: string | null
    row_span: number
    col_span: number
}

interface PanelForm extends FormGridType {
    values: {
        name: string
        cols: number
        rows: number
    }
}

interface Panel {
    id: number
    form: PanelForm
    layout: ChartLayout[][]
    showSqlButton: boolean
}

interface SelectedChart {
    row: number | null
    col: number | null
}

interface DraggedChart {
    chart?: ChartLayout
    row?: number
    col?: number
    run?: boolean
    inProcess?: boolean
}

interface SerieForm extends FormGridType {
    values: {
        name: string
        [key: string]: unknown
    }
}

interface MetricDropdown {
    items: Item[]
    groups: Item[]
    multiple: boolean
}

interface SerieSourceConfig {
    metric: string
    filters: [string, Value, string][]
}

interface ChartTab {
    title: string
    isAddSerie?: boolean
    serieForm?: SerieForm
    metricDropdown?: MetricDropdown
    inputsSets?: FormInputsSet[]
    inputsByName?: Record<string, FormInput>
    metric?: string
    flatFormValues?: FormValue[]
    formValuesBlock?: FormBlock
    errors?: Errors
}

export default defineComponent({
    components: {
        Icona,
        FormList,
        FormParams,
        Tabs,
        FormGrid,
        Dropdown,
    },
    beforeRouteEnter (to, _from, next) {
        next(vm => {
            // Reset state
            vm.reset()
            return vm.loadPanel(to.params.panelId).catch(resp => {
                if (resp.status === 403) {
                    vm.$notify({
                        type: 'error',
                        text: 'Forbidden! You have no access.',
                    })
                } else {
                    vm.$notify({
                        type: 'error',
                        text: 'Failed to load panel',
                    })
                }
                vm.$router.push({ name: 'panel-list' })
            })
        })
    },
    props: {
        panelId: {
            type: [String, Number],
            required: true,
        },
    },
    setup() {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
        }
    },
    data () {
        return {
            sql: false,
            hasError: false,
            inProcess: false,
            panel: { form: {} },
            selectedChart: {
                row: null,
                col: null,
            },
            chartAddForm: {},
            chartForm: {},
            addSerieForm: {},
            tabs: [],
            activeTab: 0,
            draggedChart: {},
            focusOnFilter: undefined,
        }
    },

    mounted () {
        this.resetTabs()
    },

    methods: {
        async loadPanel (id) {
            this.panel = await this.$fetch('/monitoring/charts/panel-edit', { panelId: id })
            this.titleBreadcrumbs.setTitle('Edit panel: ' + this.panel.form.values.name)
        },
        getChartSelectorClasses (row, col, chart) {
            let btnColor = 'btn-secondary'
            if (this.draggedChart.chart && this.draggedChart.chart !== chart) {
                btnColor = this.isChartSpanEqual(this.draggedChart.chart, chart)
                    ? 'btn-success'
                    : 'btn-danger'
            } else if (parseInt(row) === parseInt(this.selectedChart.row) && parseInt(col) === parseInt(this.selectedChart.col)) {
                btnColor = 'btn-primary'
            }

            return {
                [btnColor]: true,
                'font-italic': chart.name === null,
            }
        },
        isChartSpanEqual (sChart, tChart) {
            return parseInt(sChart.row_span) === parseInt(tChart.row_span) && parseInt(sChart.col_span) === parseInt(tChart.col_span)
        },
        onPanelDelete (id) {
            if (!confirm('Really delete panel?')) {
                return
            }

            return this.$fetch('/monitoring/charts/panel-delete', { panelId: id }).then(() => {
                this.$notify({
                    type: 'success',
                    message: 'Panel deleted',
                })
                this.$router.push({ name: 'panel-list' })
            })
        },
        onPanelChange (params) {
            params.panelId = this.panel.id
            this.$processFormResponse(this.$fetch('/monitoring/charts/panel-save', params), this.panel.form).then(() => {
                this.reset()
                this.loadPanel(params.panelId)
            })
        },
        onChartSelect ({ row, col }) {
            this.selectedChart = {
                row,
                col,
            }

            if (this.panel.layout[row][col].name === null) {
                // New chart form
                this.$fetch('/monitoring/charts/chart-add-form', this.selectedChartId()).then(form => {
                    this.chartAddForm = form
                })
            } else {
                // Existing chart form
                return Promise.all([
                    this.loadChartForm(),
                    this.loadChart(),
                    this.$fetch('/monitoring/charts/serie-add-form', this.selectedChartId()).then(form => {
                        this.addSerieForm = form
                    }),
                ])
            }
        },
        onChartAdd (params) {
            this.$processFormResponse(this.$fetch('/monitoring/charts/chart-add', params), this.chartAddForm).then(() => {
                this.loadPanel(params.panelId).then(() => {
                    this.onChartSelect({
                        row: params.row,
                        col: params.col,
                    })
                })
            })
        },
        onChartDelete () {
            if (!confirm('Really delete chart?')) {
                return
            }

            this.$processFormResponse(this.$fetch('/monitoring/charts/chart-delete', this.selectedChartId()), {} /* fake form */).then(() => {
                this.loadPanel(this.panel.id)
            })
        },
        flatFormFiltersToBlock (formFilters, inputsSets) {
            const inputsNamesToSetIndex = {}
            inputsSets.forEach((set, setIndex) => set.inputs.forEach(input => {
                inputsNamesToSetIndex[input.name] = setIndex
            }))

            const block = $emptyFormValuesBlock(inputsSets)
            formFilters.forEach(f => {
                block.blockSets[inputsNamesToSetIndex[f.name]].values.push(f)
            })
            return block
        },
        inputsByName (inputsSets) {
            const inputs = {}
            inputsSets.forEach(set => set.inputs.forEach(input => {
                inputs[input.name] = input
            }))
            return inputs
        },
        loadChartForm () {
            return Promise.all([
                this.$fetch('/monitoring/charts/chart-edit', this.selectedChartId()).then(form => {
                    this.chartForm = form
                }).catch(() => {
                    this.$notify({
                        type: 'error',
                        message: 'Chart loading failed! Danger! Can lead to chart config loss! Do not edit',
                    })
                }),

                this.$fetch('/monitoring/charts/chart-series', this.selectedChartId()).then(series => {
                    this.resetTabs()
                    series.reverse().forEach(serie => {
                        const flatFormValues = serie.sourceConfig.filters.map(([name, value, operator]) => ({ name, value, operator }))
                        this.tabs.unshift({
                            title: serie.serie.values.name,
                            serieForm: serie.serie,
                            metricDropdown: serie.metricDropdown,
                            inputsSets: serie.inputsSets,
                            inputsByName: this.inputsByName(serie.inputsSets),
                            metric: serie.sourceConfig.metric,
                            flatFormValues,
                            formValuesBlock: this.flatFormFiltersToBlock(flatFormValues, serie.inputsSets),
                        })
                    })
                }).catch(() => {
                    this.$notify({
                        type: 'error',
                        message: 'Chart series loading failed! Danger! Can lead to series config loss! Do not edit',
                    })
                }),
            ])
        },
        onChartChange (chart) {
            const params = Object.assign({ config: chart }, this.selectedChartId())
            delete params.config.series
            this.$processFormResponse(this.$fetch('/monitoring/charts/chart-save', params), this.chartForm).then(() => {
                this.loadPanel(this.panel.id)
                this.loadChartForm()
            }).catch(() => this.hasError = true)
        },
        processSqlView (show) {
            if (!show) {
                this.sql = false
                return
            }
            return show && this.$fetch('/monitoring/charts/chart-config', this.selectedChartId()).then(conf => {
                const to = (new Date()).valueOf() - 60 * 1000
                const from = (new Date(to - conf.xAxis[0].range)).valueOf()
                return this.loadSql(from, to)
            })
        },
        loadSql (from, to) {
            this.sql = false
            return this.$fetch('/monitoring/charts/chart-serie-sql', Object.assign({}, this.selectedChartId(), this.selectedChartSerieId(), { from, to }))
                .then(data => this.sql = data.sql)
        },
        loadChart () {
            return this.$fetch('/monitoring/charts/chart-config', this.selectedChartId()).then(conf => {
                this.destroyHighchart()
                return HighchartsLoader().then(({ default: Highcharts }) => {
                    this.$options.highchart = Highcharts.stockChart('panels-chart-highchart-preview', conf)
                    return this.loadChartData(conf)
                })
            }).then(series => {
                series.forEach((serieData, i) => {
                    this.$options.highchart.series[i].setData(serieData, false)
                })
                this.$options.highchart.redraw()
            }).then(() => {
                this.hasError = false
            })
        },
        loadChartData (conf) {
            const to = (new Date()).valueOf() - 60 * 1000
            const from = (new Date(to - conf.xAxis[0].range)).valueOf()
            if (this.sql !== false) {
                this.loadSql(from, to)
            }
            return this.$fetch('/monitoring/charts/chart-data', Object.assign({}, this.selectedChartId(), { from, to }))
        },
        reset () {
            this.panel = {}
            this.selectedChart = {
                row: null,
                col: null,
            }
            this.destroyHighchart()
        },
        resetTabs () {
            this.activeTab = 0
            this.tabs = [
                {
                    title: 'Add new serie',
                    isAddSerie: true,
                },
            ]
        },
        destroyHighchart () {
            if (this.$options.highchart) {
                this.$options.highchart.destroy()
            }
        },
        selectedChartId () {
            return {
                panelId: this.panel.id,
                col: this.selectedChart.col,
                row: this.selectedChart.row,
            }
        },
        selectedChartSerieId () {
            return Object.assign({ serieIndex: this.activeTab }, this.selectedChartId())
        },
        onChartNewSerieAdd (source) {
            const params = Object.assign(source, this.selectedChartId())
            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-add', params), this.addSerieForm).then(() => {
                this.loadChartForm().then(() => {
                    this.activeTab = Math.max(0, this.tabs.length - 2)
                    this.loadChart()
                })
            })
        },
        onChartSerieDelete () {
            if (!confirm('Really delete serie?')) {
                return
            }

            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-delete', this.selectedChartSerieId()), {} /* fake form */).then(() => {
                this.loadChartForm().then(() => {
                    this.loadChart()
                })
            })
        },
        onChartSerieClone () {
            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-clone', this.selectedChartSerieId()), {} /* fake form */).then(() => {
                this.loadChartForm().then(() => {
                    this.loadChart()
                })
            })
        },
        onChartSerieChange (serie) {
            const params = Object.assign({ config: serie }, this.selectedChartSerieId())
            delete params.config.sourceConfig

            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-save', params), this.tabs[this.activeTab].serieForm).then(() => {
                this.loadChartForm().then(() => {
                    this.loadChart()
                })
            })
        },
        onParamClick (input) {
            const setIndex = this.tabs[this.activeTab].inputsSets.findIndex(s => s.inputs.some(f => f.name === input.name))
            this.focusOnFilter = $toggleFormValue(input, this.tabs[this.activeTab].formValuesBlock.blockSets[setIndex])
        },
        onChange ({ valueIndex, value }) {
            this.tabs[this.activeTab].formValuesBlock.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].value = value
            this.onValuesChanged()
        },
        onChangeOperator ({ valueIndex, operator }) {
            this.tabs[this.activeTab].formValuesBlock.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].operator = operator
            this.onValuesChanged()
        },
        onDelete (valueIndex) {
            this.tabs[this.activeTab].formValuesBlock.blockSets[valueIndex.setIndex].values.splice(valueIndex.valueIndex, 1)
            this.onValuesChanged()
        },
        onMetricChanged (value) {
            this.tabs[this.activeTab].metric = value
            this.onValuesChanged()
        },
        onValuesChanged () {
            const curTab = this.tabs[this.activeTab]
            curTab.flatFormValues = curTab.formValuesBlock.blockSets.map(s => s.values).flat()
            const params = {
                ...this.selectedChartSerieId(),
                chartConfig: { ...$fromValuesToUrlParams(curTab.flatFormValues), ...{ metric: curTab.metric } },
            }

            this.inProcess = true
            this.$fetch('/monitoring/charts/source-save', params).then(() => {
                delete curTab.errors
                curTab.flatFormValues.forEach(param => delete param.error)
                return this.loadChart()
            })
                .catch(resp => {
                    this.hasError = true
                    if (resp.status !== 422) {
                        return
                    }

                    return resp.json().then(data => {
                        if (!('errors' in data)) {
                            return
                        }

                        curTab.errors = data.errors
                        curTab.flatFormValues.forEach((param, index) => {
                            const key = `f${index}e`

                            if (key in data.errors) {
                                param.error = data.errors[key]
                            } else if (param.name in data.errors) {
                                param.error = data.errors[param.name]
                            } else {
                                delete param.error
                            }
                        })
                    })
                })
                .then(() => this.inProcess = false)
        },
        onDragStart (e, row, col, chart) {
            this.draggedChart = { chart, row, col, run: true }
            // force some browsers enable drag'n'drop
            e.dataTransfer.setData('text/plain', '')
            e.dataTransfer.dropEffect = 'move'
        },
        onDragEnd () {
            this.draggedChart = {}
        },
        onDrop (e, row, col, chart) {
            if (!this.draggedChart.run || chart === this.draggedChart.chart) {
                return
            }
            if (!this.isChartSpanEqual(chart, this.draggedChart.chart)) {
                this.$notify({ type: 'warn', message: 'Charts with different sizes cannot be switched' })
                return
            }
            this.draggedChart.inProcess = true
            const { row: draggedRow, col: draggedCol } = (this.draggedChart)

            let selectedChart = this.selectedChart
            if (parseInt(row) === parseInt(this.selectedChart.row) && parseInt(col) === parseInt(this.selectedChart.col)) {
                selectedChart = { row: draggedRow, col: draggedCol }
            } else if (parseInt(draggedRow) === parseInt(this.selectedChart.row) && parseInt(draggedCol) === parseInt(this.draggedChart.col)) {
                selectedChart = { row, col }
            }
            this.$fetch('/monitoring/charts/chart-switch', {
                sourceRow: draggedRow,
                sourceCol: draggedCol,
                targetRow: row,
                targetCol: col,
                panelId: this.panelId,
            })
                .then(() => this.loadPanel(this.panel.id)
                    .then(() => this.selectedChart = selectedChart),
                )
                .finally(() => this.draggedChart.inProcess = false)
        },
    },
})
</script>
