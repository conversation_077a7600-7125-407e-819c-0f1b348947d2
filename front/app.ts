import { App, createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import AppRoutes from './apps/routes'

// App specific imports
import AppInstance from './app.vue'
import { initFetch, useFetch } from '@/utils/fetch'
import { useNotify } from '@/utils/notify'
import { useProcessResponse, useProcessFormResponse, useProcessRichTableResponse, useSetForm } from '@/utils/response-processor'
import { useDecodeParams, useEncodeParams, useHistoryPushParams, useHistoryReplaceParams, useNormalizeParamsObj } from '@/utils/url-params'
import MousedownOutside from '@/utils/mousedown-outside'

const API_URL = '/front'

const router = createRouter({
    history: createWebHistory('/'),
    routes: AppRoutes,
})

createApp(AppInstance)
    .directive('mousedown-outside', MousedownOutside)
    .use(router)
    .use({
        install: (app: App): void => { // For options api
            const $notify = useNotify()
            initFetch($notify, API_URL)

            app.config.globalProperties.apiUrl = API_URL
            app.config.globalProperties.$fetch = useFetch()
            app.config.globalProperties.$notify = $notify

            app.config.globalProperties.$processResponse = useProcessResponse()
            app.config.globalProperties.$processFormResponse = useProcessFormResponse()
            app.config.globalProperties.$processRichTableResponse = useProcessRichTableResponse()
            app.config.globalProperties.$setForm = useSetForm()

            app.config.globalProperties.$normalizeParamsObj = useNormalizeParamsObj()
            app.config.globalProperties.$historyReplaceParams = useHistoryReplaceParams(router)
            app.config.globalProperties.$historyPushParams = useHistoryPushParams(router)
            app.config.globalProperties.$encodeParams = useEncodeParams()
            app.config.globalProperties.$decodeParams = useDecodeParams()
        },
    })
    .mount('#an-app')

