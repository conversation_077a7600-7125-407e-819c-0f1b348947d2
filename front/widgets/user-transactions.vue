<template>
    <RichTable
        class="table-sm"
        showPagination
        showTotal
        showRefresh
        :rowProps="rowProps"
        v-bind="transactionsTable"
        @reload="load"
    >
        <template #afterTitle>
            Last comment:
            <Popover
                :title="popoverTitle"
                hideOnOutside
                position="right"
                @open="onOpenComments()"
            >
                <span
                    class="inplace-edit-link"
                    :title="'Add comment or show history'"
                >
                    {{ userComment || 'empty' }}
                </span>
                <template v-if="updatedBy">
                    <small class="text-muted"> ({{ updatedBy }})</small>
                </template>
                <template #content>
                    <Comments
                        v-if="commentsPopupOpened"
                        :siteUser="siteUser"
                    />
                </template>
            </Popover>
        </template>

        <template #statusName="{row: {statusName, plannedAt, withdrawalId}}">
            {{ statusName }}
            <Icona
                v-if="plannedAt"
                :title="`Planned at ${plannedAt}`"
                name="icn-hourglass"
            />
            <Icona
                v-else-if="withdrawalId"
                name="icn-refresh"
                class="icona-spinner"
            />
        </template>

        <template #commentWithdraw="{row: {commentWithdraw, siteId, transactionId, commentWithdrawUpdatedBy}, refreshCallback: innerRefreshCallback}">
            <InplaceEdit
                type="input"
                :disabled="transactionsTable.disabled"
                :value="commentWithdraw"
                :title="commentWithdrawUpdatedBy ? commentWithdrawUpdatedBy : ''"
                @submit="submitTransactionComment({comment: $event, siteId, transactionId}, innerRefreshCallback)"
            />
        </template>

        <template #btnAllow="{row, refreshCallback: innerRefreshCallback}">
            <button
                v-if="row.canAllow"
                class="btn btn-success btn-sm"
                :disabled="transactionsTable.disabled"
                @click="withdrawalsAllow(row, innerRefreshCallback)"
            >
                <Icona name="icn-check" />
            </button>
            <div v-else-if="row.statusName === 'success'">
                <span class="badge bg-success">Allowed</span>
            </div>
            <template v-else>
                -
            </template>
        </template>

        <template #btnDeny="{row, refreshCallback: innerRefreshCallback}">
            <button
                v-if="row.canDeny"
                class="btn btn-danger btn-sm"
                :disabled="transactionsTable.disabled"
                @click="withdrawalsDeny(() => $event.target as HTMLElement, row, innerRefreshCallback)"
            >
                <Icona name="icn-ban" />
            </button>
            <div v-else-if="row.statusName === 'fail'">
                <span class="badge bg-danger">Denied</span>
            </div>
            <template v-else>
                -
            </template>
        </template>
    </RichTable>
</template>

<script lang="ts" setup>
import { computed, onUnmounted, ref, watch } from 'vue'
import { Icona, InplaceEdit, Popover, RichTable } from '@/components'
import { ClassList, FormGridType, RichTableType, SiteIdUserId, Values } from '@/types'
import { DecisionData } from '@/apps/finance/withdrawals/withdrawals-table.vue'
import { DenyTargetType } from '@/apps/finance/withdrawals/deny-popover.vue'
import { Comments } from '@/widgets'
import { useFetch } from '@/utils/fetch'
import { useProcessRichTableResponse } from '@/utils/response-processor'

interface TransactionRow {
    siteId: number
    userId: number,
    transactionId: string
}

defineOptions({
    inheritAttrs: false,
})

const $props = withDefaults(defineProps<{
    refreshCallback?: () => void
    siteUser?: SiteIdUserId
}>(), {
    refreshCallback: () => false,
    siteUser: undefined,
})

const $emit = defineEmits<{
    deny: [data: DenyTargetType]
}>()

const $fetch = useFetch()
const $processRichTableResponse = useProcessRichTableResponse()

const transactionsTable = ref({
    form: {} as FormGridType,
    disabled: false,
} as RichTableType)
const userComment = ref<string>()
const updatedBy = ref<string>()
const commentsPopupOpened = ref(false)

const popoverTitle = computed((): string => {
    return `User ${$props.siteUser?.userId} comments`
})

async function load(filters = {}) {
    return Promise.all([
        $processRichTableResponse($fetch('/finance/users/transactions', Object.assign({}, $props.siteUser, filters)), transactionsTable.value),
        loadUserComment(),
    ])
}

function withdrawalsAllow(row: TransactionRow, refreshCallback: () => void) {
    return submitWithdrawalsDecision('allow', { siteId: row.siteId, userId: row.userId, transactionIds: [row.transactionId] }, refreshCallback)
}

function withdrawalsDeny(targetButton: () => HTMLElement, row: TransactionRow, refreshCallback: () => void) {
    const data: DenyTargetType = {
        denyButtonElementClosure: targetButton,
        requestPromiseCallback: async (requestPromise) => {
            transactionsTable.value.disabled = true
            await requestPromise
            refreshCallback()
        },
        siteId: row.siteId,
        userId: row.userId,
        siteUser: row.siteId + '-' + row.userId,
        transactionIds: [row.transactionId],
    }
    $emit('deny', data)
}

async function submitWithdrawalsDecision(decision: string, data: DecisionData, refreshCallback: () => void) {
    transactionsTable.value.disabled = true

    try {
        await $fetch(`/finance/withdrawals/${decision}`, data)
        refreshCallback()
    } finally {
        transactionsTable.value.disabled = false
    }
}

function rowProps(statusName: string) {
    const props = {
        class: {} as ClassList,
    }

    switch (statusName) {
        case 'success':
            props.class['table-success'] = true
            break
        case 'fail':
            props.class['table-danger'] = true
            break
    }

    return props
}

async function submitTransactionComment(params: Values, refreshCallback: () => void) {
    transactionsTable.value.disabled = true
    try {
        await $fetch('/finance/withdrawals/update-transaction-withdraw-comment', params)
    } finally {
        refreshCallback()
        transactionsTable.value.disabled = false
    }
}

async function loadUserComment() {
    const { comment, updatedBy: updatedByValue } = await $fetch('/finance/users/get-withdraw-comment', $props.siteUser)
    userComment.value = comment
    updatedBy.value = updatedByValue
}

function onOpenComments() {
    commentsPopupOpened.value = true
}

watch(() => $props.siteUser, async (siteUser) => {
    if (siteUser?.siteId && siteUser?.userId) {
        await load()
    }
}, { immediate: true })

onUnmounted(() => {
    if ($props.refreshCallback) {
        $props.refreshCallback()
    }
})
</script>
