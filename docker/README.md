#Analytics development environment with docker compose

##Requirements
 * You have to have generated your ssh key and added it to your repository profile.
 * Use ssh repository links instead of http
 

##Installing

https://docs.docker.com/engine/install/

https://docs.docker.com/compose/install/

If you wish running docker without root privileges run the following command:
```bash
sudo groupadd docker && sudo usermod -aG docker $USER
```

##Populate database

##Overview
All docker configuration stores inside "docker" folder in the root project directory.

Main entry point into analytics docker environment is shell script called "dc" in root directory of project. This script
 passes all arguments which receives to docker-compose command with default env variables included.

All services are splitted into separate containers so each container runs only single process.

Next services are available:
 * php - runs php prefork manager process.
 * nginx - runs nginx service configured for analytics project. Service port is **8080**. 
 * postgres - runs postgres instance.
 * data - service contains volume with postgres data so postgres service can be safely rebuild without data loses.
 * redis - runs redis service.
 * npm - contains npm.

##Usage
Full list of commands can be retrieved via executing `docker compose`.

To init docker environment with containers run `docker compose up`. Also can be used for refreshing environment 
 if compose file has been changed.
> Note: use "-d" flag with "up" command to run docker in detached mode.

To shut down environment and remove containers execute `docker compose down`.

To run services if they where stopped execute `docker compose start`.

To stop run services execute `docker compose stop`.

Container logs can be viewed running `docker compose logs`.

To run some command inside container execute `docker compose run [service name] arguments`. For ex.: `docker compose run php composer install`

Also it is possible to enter shell environment inside service via executing `docker compose run [service-name] sh`.

##Troubleshooting

If you initially ran Docker CLI commands using sudo before adding your user to the docker group, you may see the following error, which indicates that your ~/.docker/ directory was created with incorrect permissions due to the sudo commands.

WARNING: Error loading config file: /home/<USER>/.docker/config.json -
stat /home/<USER>/.docker/config.json: permission denied
To fix this problem, either remove the ~/.docker/ directory (it will be recreated automatically, but any custom settings will be lost), or change its ownership and pemissions using the following commands:

```
sudo chown "$USER":"$USER" /home/"$USER"/.docker -R \
 && sudo chmod g+rwx "/home/<USER>/.docker" -R
 && sudo chmod g+rwx "/var/run/docker.sock"
```
