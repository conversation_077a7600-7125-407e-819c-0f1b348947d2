# Analytics services

```mermaid
%%{init: {'flowchart': {'nodeSpacing': 20}}}%%
flowchart LR
subgraph db-02-nl
    slave[(PostgreSQL slave)]
end

subgraph db-01-nl
    master[(PostgreSQL master)]
    master-->|Replication| slave
end

subgraph node-04
    nginx1[Ngnix]-->PHP1
    PHP1[PHP]
    PHP1-->cron[Cron Tasks]
    PHP1-->events1[Event workers]
    PHP1-->minio1[Minio]
    PHP1-->Redis1[Redis]
    PHP1-->slave
    PHP1-->master
end

subgraph node-05
    nginx2[Ngnix]-->PHP2
    PHP2[PHP]
    PHP2-->master
    PHP2-->slave
    PHP2-->events2[Event workers]
    PHP2-->minio2[Minio]
    PHP2-->Redis2[Redis]
end

subgraph node-03
    documents[documents recognition]
    faces[face recognition]
    cron-->faces
    cron-->documents
    cron-->buffer
    documents[documents recognition]-->Python1[Python3.5]
    faces[face recognition]-->Python3[Python3.7]
    Redis3[Redis]
    Redis1<-->Redis2
    Redis1<-->Redis3
    Redis2<-->Redis3
    nginx3[Nginx]-->|round-robin| buffer
    buffer[buffer.anal.tools]-->PHP3[PHP]
end

subgraph google-s3
    minio3[Minio Storage]
    minio2<-->|proxy| minio3
    minio1<-->|proxy| minio3
end
```
